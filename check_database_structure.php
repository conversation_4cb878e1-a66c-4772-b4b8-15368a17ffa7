<?php
// Verificar estructura de la base de datos
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🔍 Verificación de Estructura de Base de Datos</h1>";
echo "<style>
body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
table { border-collapse: collapse; width: 100%; margin: 20px 0; }
th, td { border: 1px solid #444; padding: 10px; text-align: left; }
th { background: #333; }
.exists { color: #10b981; }
.missing { color: #ef4444; }
.section { margin: 30px 0; }
</style>";

// Función para verificar si una columna existe
function columnExists($pdo, $table, $column) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE table_name = ? 
        AND table_schema = DATABASE() 
        AND column_name = ?
    ");
    $stmt->execute([$table, $column]);
    return $stmt->fetchColumn() > 0;
}

// Función para verificar si una tabla existe
function tableExists($pdo, $table) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE table_name = ? 
        AND table_schema = DATABASE()
    ");
    $stmt->execute([$table]);
    return $stmt->fetchColumn() > 0;
}

// Verificar tabla orders
echo "<div class='section'>";
echo "<h2>📋 Tabla: orders</h2>";
$orders_columns = [
    'tmdb_id' => 'INT NULL',
    'media_type' => 'ENUM(\'movie\', \'tv\', \'person\') NULL',
    'tmdb_title' => 'VARCHAR(500) NULL',
    'tmdb_original_title' => 'VARCHAR(500) NULL',
    'tmdb_year' => 'INT NULL',
    'tmdb_poster_path' => 'VARCHAR(500) NULL',
    'tmdb_overview' => 'TEXT NULL',
    'tmdb_language' => 'VARCHAR(10) NULL'
];

echo "<table>";
echo "<tr><th>Columna</th><th>Tipo</th><th>Estado</th><th>SQL para agregar</th></tr>";
foreach ($orders_columns as $column => $type) {
    $exists = columnExists($pdo, 'orders', $column);
    $status = $exists ? '<span class="exists">✅ Existe</span>' : '<span class="missing">❌ Falta</span>';
    $sql = $exists ? '-' : "ALTER TABLE orders ADD COLUMN $column $type;";
    echo "<tr><td>$column</td><td>$type</td><td>$status</td><td>$sql</td></tr>";
}
echo "</table>";
echo "</div>";

// Verificar tabla m3u_content
echo "<div class='section'>";
echo "<h2>📺 Tabla: m3u_content</h2>";
$content_columns = [
    'tmdb_id' => 'INT NULL',
    'tmdb_title' => 'VARCHAR(500) NULL',
    'tmdb_original_title' => 'VARCHAR(500) NULL',
    'tmdb_poster_path' => 'VARCHAR(500) NULL',
    'tmdb_overview' => 'TEXT NULL',
    'tmdb_language' => 'VARCHAR(10) NULL',
    'tmdb_vote_average' => 'DECIMAL(3,1) NULL',
    'tmdb_release_date' => 'DATE NULL'
];

echo "<table>";
echo "<tr><th>Columna</th><th>Tipo</th><th>Estado</th><th>SQL para agregar</th></tr>";
foreach ($content_columns as $column => $type) {
    $exists = columnExists($pdo, 'm3u_content', $column);
    $status = $exists ? '<span class="exists">✅ Existe</span>' : '<span class="missing">❌ Falta</span>';
    $sql = $exists ? '-' : "ALTER TABLE m3u_content ADD COLUMN $column $type;";
    echo "<tr><td>$column</td><td>$type</td><td>$status</td><td>$sql</td></tr>";
}
echo "</table>";
echo "</div>";

// Verificar tablas nuevas
echo "<div class='section'>";
echo "<h2>🆕 Tablas Nuevas</h2>";
$new_tables = [
    'tmdb_similar_mapping' => 'Mapeo de contenido similar',
    'tmdb_search_cache' => 'Cache de búsquedas TMDB'
];

echo "<table>";
echo "<tr><th>Tabla</th><th>Descripción</th><th>Estado</th></tr>";
foreach ($new_tables as $table => $description) {
    $exists = tableExists($pdo, $table);
    $status = $exists ? '<span class="exists">✅ Existe</span>' : '<span class="missing">❌ Falta</span>';
    echo "<tr><td>$table</td><td>$description</td><td>$status</td></tr>";
}
echo "</table>";
echo "</div>";

// Mostrar estructura actual de orders
echo "<div class='section'>";
echo "<h2>🔍 Estructura Actual de 'orders'</h2>";
$stmt = $pdo->query("DESCRIBE orders");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table>";
echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column['Field']}</td>";
    echo "<td>{$column['Type']}</td>";
    echo "<td>{$column['Null']}</td>";
    echo "<td>{$column['Key']}</td>";
    echo "<td>{$column['Default']}</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Mostrar estructura actual de m3u_content
echo "<div class='section'>";
echo "<h2>🔍 Estructura Actual de 'm3u_content'</h2>";
$stmt = $pdo->query("DESCRIBE m3u_content");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table>";
echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column['Field']}</td>";
    echo "<td>{$column['Type']}</td>";
    echo "<td>{$column['Null']}</td>";
    echo "<td>{$column['Key']}</td>";
    echo "<td>{$column['Default']}</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Generar SQL personalizado
echo "<div class='section'>";
echo "<h2>🔧 SQL Personalizado para Ejecutar</h2>";
echo "<p>Copia y ejecuta solo las líneas que muestren '❌ Falta' arriba:</p>";
echo "<textarea style='width: 100%; height: 200px; background: #333; color: white; padding: 10px;'>";

// SQL para orders
foreach ($orders_columns as $column => $type) {
    if (!columnExists($pdo, 'orders', $column)) {
        echo "ALTER TABLE orders ADD COLUMN $column $type;\n";
    }
}

// SQL para m3u_content
foreach ($content_columns as $column => $type) {
    if (!columnExists($pdo, 'm3u_content', $column)) {
        echo "ALTER TABLE m3u_content ADD COLUMN $column $type;\n";
    }
}

// Tablas nuevas
if (!tableExists($pdo, 'tmdb_similar_mapping')) {
    echo "\nCREATE TABLE tmdb_similar_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT NOT NULL,
    similar_tmdb_id INT NOT NULL,
    similarity_score DECIMAL(5,2) DEFAULT 0.00,
    media_type ENUM('movie', 'tv') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tmdb_mapping (tmdb_id),
    INDEX idx_similar_mapping (similar_tmdb_id),
    UNIQUE KEY unique_mapping (tmdb_id, similar_tmdb_id)
);\n";
}

if (!tableExists($pdo, 'tmdb_search_cache')) {
    echo "\nCREATE TABLE tmdb_search_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_query VARCHAR(500) NOT NULL,
    search_hash VARCHAR(64) NOT NULL,
    tmdb_response JSON,
    language VARCHAR(10) DEFAULT 'es-MX',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
    INDEX idx_search_hash (search_hash),
    INDEX idx_expires (expires_at)
);\n";
}

echo "</textarea>";
echo "</div>";

echo "<br><br>";
echo "<a href='admin.php' style='color: #10b981;'>← Volver al admin</a>";
?>
