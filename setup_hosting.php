<?php
// Configuración específica para hosting compartido
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Credenciales de tu hosting
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>🚀 Configuración para Hosting Compartido</h1>";
echo "<p><strong>Base de datos:</strong> $db_name</p>";

try {
    // Conectar a la base de datos existente
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Conexión a base de datos establecida<br><br>";
    
    // Verificar qué tablas ya existen
    echo "<h2>📋 Verificando Tablas Existentes</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($existing_tables)) {
        echo "<strong>Tablas encontradas:</strong><br>";
        foreach ($existing_tables as $table) {
            echo "• $table<br>";
        }
        echo "<br>";
    } else {
        echo "No se encontraron tablas.<br><br>";
    }
    
    // Lista de tablas necesarias para el sistema
    $required_tables = [
        'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100),
                password_hash VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'support_tickets' => "
            CREATE TABLE IF NOT EXISTS support_tickets (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                subject VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                category VARCHAR(50) NOT NULL,
                status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
                assigned_to INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'ticket_responses' => "
            CREATE TABLE IF NOT EXISTS ticket_responses (
                id INT PRIMARY KEY AUTO_INCREMENT,
                ticket_id INT NOT NULL,
                user_id INT,
                message TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'chat_sessions' => "
            CREATE TABLE IF NOT EXISTS chat_sessions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                status ENUM('waiting', 'active', 'ended') DEFAULT 'waiting',
                started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ended_at TIMESTAMP NULL,
                rating INT NULL
            )",
        
        'chat_messages' => "
            CREATE TABLE IF NOT EXISTS chat_messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                session_id INT NOT NULL,
                sender_id INT NOT NULL,
                message TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'support_apps' => "
            CREATE TABLE IF NOT EXISTS support_apps (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                version VARCHAR(50) NOT NULL,
                platform ENUM('android', 'ios', 'windows', 'macos', 'smart_tv', 'firestick', 'web', 'other') NOT NULL,
                description TEXT,
                features TEXT,
                file_path VARCHAR(500),
                file_size BIGINT,
                download_url VARCHAR(500),
                external_url VARCHAR(500),
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'help_articles' => "
            CREATE TABLE IF NOT EXISTS help_articles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                category ENUM('setup', 'troubleshooting', 'apps', 'channels', 'billing', 'account', 'general') NOT NULL,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                is_featured BOOLEAN DEFAULT FALSE,
                views INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'channel_requests' => "
            CREATE TABLE IF NOT EXISTS channel_requests (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                channel_name VARCHAR(255) NOT NULL,
                channel_url VARCHAR(500),
                country VARCHAR(10) NOT NULL,
                language VARCHAR(50) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'activation_codes' => "
            CREATE TABLE IF NOT EXISTS activation_codes (
                id INT PRIMARY KEY AUTO_INCREMENT,
                code VARCHAR(50) UNIQUE NOT NULL,
                list_name VARCHAR(255) NOT NULL,
                description TEXT,
                status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active',
                expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
        
        'user_activations' => "
            CREATE TABLE IF NOT EXISTS user_activations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                activation_code_id INT NOT NULL,
                activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
        
        'activity_logs' => "
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT,
                action VARCHAR(100) NOT NULL,
                details TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
    ];
    
    echo "<h2>🔧 Creando Tablas Necesarias</h2>";
    
    foreach ($required_tables as $table_name => $sql) {
        try {
            $pdo->exec($sql);
            echo "✅ Tabla '$table_name' creada/verificada<br>";
        } catch (Exception $e) {
            echo "❌ Error creando tabla '$table_name': " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><h2>👥 Creando Usuarios Demo</h2>";
    
    // Insertar usuario demo
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'usuario'");
        $stmt->execute();
        if (!$stmt->fetch()) {
            $pdo->exec("INSERT INTO users (username, email, password_hash) VALUES ('usuario', '<EMAIL>', 'demo_hash')");
            echo "✅ Usuario 'usuario' creado<br>";
        } else {
            echo "✅ Usuario 'usuario' ya existe<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error creando usuario: " . $e->getMessage() . "<br>";
    }
    
    // Insertar admin demo
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
        $stmt->execute();
        if (!$stmt->fetch()) {
            $pdo->exec("INSERT INTO users (username, email, password_hash) VALUES ('admin', '<EMAIL>', 'admin_hash')");
            echo "✅ Usuario 'admin' creado<br>";
        } else {
            echo "✅ Usuario 'admin' ya existe<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error creando admin: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><h2>📱 Insertando Datos de Ejemplo</h2>";
    
    // Insertar aplicaciones de ejemplo
    try {
        $pdo->exec("
            INSERT IGNORE INTO support_apps (id, name, version, platform, description, status) VALUES
            (1, 'RGS IPTV Android', '2.1.0', 'android', 'Aplicación oficial para dispositivos Android', 'published'),
            (2, 'RGS IPTV iOS', '2.0.5', 'ios', 'Aplicación oficial para iPhone y iPad', 'published'),
            (3, 'RGS Player Windows', '1.8.2', 'windows', 'Reproductor para Windows 10/11', 'published')
        ");
        echo "✅ Aplicaciones de ejemplo insertadas<br>";
    } catch (Exception $e) {
        echo "⚠️ Aplicaciones ya existen o error: " . $e->getMessage() . "<br>";
    }
    
    // Insertar artículos de ayuda
    try {
        $pdo->exec("
            INSERT IGNORE INTO help_articles (id, title, content, category, status, is_featured) VALUES
            (1, 'Cómo configurar tu lista M3U', 'Guía paso a paso para configurar tu lista M3U en diferentes dispositivos...', 'setup', 'published', 1),
            (2, 'Solución a problemas de buffering', 'Si experimentas cortes o buffering, sigue estos pasos...', 'troubleshooting', 'published', 1),
            (3, 'Instalación en Smart TV', 'Instrucciones para instalar la aplicación en tu Smart TV...', 'apps', 'published', 0)
        ");
        echo "✅ Artículos de ayuda insertados<br>";
    } catch (Exception $e) {
        echo "⚠️ Artículos ya existen o error: " . $e->getMessage() . "<br>";
    }
    
    // Insertar códigos de activación
    try {
        $pdo->exec("
            INSERT IGNORE INTO activation_codes (id, code, list_name, description, status) VALUES
            (1, 'DEMO2024', 'Lista Premium Demo', 'Código de demostración para acceso premium', 'active'),
            (2, 'TEST123', 'Lista de Prueba', 'Lista de prueba con canales básicos', 'active')
        ");
        echo "✅ Códigos de activación insertados<br>";
    } catch (Exception $e) {
        echo "⚠️ Códigos ya existen o error: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h2>🎉 ¡Configuración Completada!</h2>";
    echo "<p><strong>El sistema está listo para usar.</strong></p>";
    echo "<br><strong>Credenciales de acceso:</strong><br>";
    echo "👤 <strong>Usuario:</strong> <code>usuario</code> / <strong>Contraseña:</strong> <code>demo123</code><br>";
    echo "🛠️ <strong>Admin:</strong> <code>admin</code> / <strong>Contraseña:</strong> <code>admin123</code><br>";
    echo "<br><strong>Enlaces de acceso:</strong><br>";
    echo "🏠 <a href='index2.php' style='color: #007bff;'><strong>Ir a Servicios de Soporte</strong></a><br>";
    echo "⚙️ <a href='admin2.php' style='color: #007bff;'><strong>Ir al Panel de Administración</strong></a><br>";
    echo "🔍 <a href='debug.php' style='color: #007bff;'>Ejecutar Diagnóstico</a><br>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ Error de Conexión</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<br><strong>Verifica:</strong><br>";
    echo "1. Que las credenciales sean correctas<br>";
    echo "2. Que la base de datos '$db_name' exista en tu hosting<br>";
    echo "3. Que el usuario '$db_user' tenga permisos<br>";
    echo "4. Que el servidor MySQL esté funcionando<br>";
    echo "</div>";
}
?>
