<?php
// Analizador de metadatos M3U
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$analysis_results = [];

if (isset($_POST['analyze_list'])) {
    $list_id = (int)$_POST['list_id'];
    
    // Obtener muestra de contenido
    $stmt = $pdo->prepare("
        SELECT id, title, logo_url, group_title, tvg_id
        FROM m3u_content 
        WHERE list_id = ?
        LIMIT 20
    ");
    $stmt->execute([$list_id]);
    $sample_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Analizar patrones
    $patterns = [
        'tmdb_posters' => 0,
        'years_in_title' => 0,
        'episode_info' => 0,
        'quality_info' => 0,
        'group_categories' => [],
        'logo_sources' => [],
        'title_patterns' => []
    ];
    
    foreach ($sample_content as $item) {
        // Analizar poster URLs
        if ($item['logo_url']) {
            if (strpos($item['logo_url'], 'image.tmdb.org') !== false) {
                $patterns['tmdb_posters']++;
            }
            
            $domain = parse_url($item['logo_url'], PHP_URL_HOST);
            if ($domain) {
                $patterns['logo_sources'][$domain] = ($patterns['logo_sources'][$domain] ?? 0) + 1;
            }
        }
        
        // Analizar títulos
        if (preg_match('/\(\d{4}\)/', $item['title'])) {
            $patterns['years_in_title']++;
        }
        
        if (preg_match('/[Ss]\d+[Ee]\d+/', $item['title'])) {
            $patterns['episode_info']++;
        }
        
        if (preg_match('/(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV)/i', $item['title'])) {
            $patterns['quality_info']++;
        }
        
        // Analizar grupos
        if ($item['group_title']) {
            $patterns['group_categories'][$item['group_title']] = ($patterns['group_categories'][$item['group_title']] ?? 0) + 1;
        }
    }
    
    // Estadísticas generales de la lista
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 END) as with_logo,
            COUNT(CASE WHEN logo_url LIKE '%tmdb%' THEN 1 END) as with_tmdb_logo,
            COUNT(CASE WHEN group_title IS NOT NULL AND group_title != '' THEN 1 END) as with_group,
            COUNT(CASE WHEN tvg_id IS NOT NULL AND tvg_id != '' THEN 1 END) as with_tvg_id,
            COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb_id
        FROM m3u_content 
        WHERE list_id = ?
    ");
    $stmt->execute([$list_id]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $analysis_results = [
        'sample_content' => $sample_content,
        'patterns' => $patterns,
        'stats' => $stats
    ];
}

// Obtener listas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        l.list_type,
        COUNT(c.id) as total_items
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name, l.list_type
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Analizador de Metadatos M3U</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        button {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #46d347;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9em;
        }
        
        .sample-item {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            border: 1px solid #404040;
        }
        
        .item-title {
            color: #46d347;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .item-meta {
            color: #b0b0b0;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .item-logo {
            max-width: 100px;
            max-height: 150px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .pattern-list {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .pattern-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #404040;
        }
        
        .pattern-item:last-child {
            border-bottom: none;
        }
        
        .success {
            color: #10b981;
        }
        
        .warning {
            color: #f59e0b;
        }
        
        .error {
            color: #ef4444;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🔍 Analizador de Metadatos M3U</h1>
        
        <div class="card">
            <h2>📋 Analizar Lista M3U</h2>
            <p>Esta herramienta analiza los metadatos disponibles en las listas M3U para identificar qué información ya está disponible.</p>
            
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['list_type']; ?>, <?php echo $list['total_items']; ?> items)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="analyze_list">
                    🔍 Analizar Metadatos
                </button>
            </form>
        </div>
        
        <?php if (!empty($analysis_results)): ?>
        <div class="card">
            <h2>📊 Estadísticas Generales</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($analysis_results['stats']['total']); ?></div>
                    <div class="stat-label">Total de Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($analysis_results['stats']['with_logo']); ?></div>
                    <div class="stat-label">Con Logo/Poster</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($analysis_results['stats']['with_tmdb_logo']); ?></div>
                    <div class="stat-label">Con Poster TMDB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($analysis_results['stats']['with_group']); ?></div>
                    <div class="stat-label">Con Categoría</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($analysis_results['stats']['with_tmdb_id']); ?></div>
                    <div class="stat-label">Con TMDB ID</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo $analysis_results['stats']['total'] > 0 ? round(($analysis_results['stats']['with_tmdb_logo']/$analysis_results['stats']['total'])*100, 1) : 0; ?>%
                    </div>
                    <div class="stat-label">% con TMDB</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🔍 Análisis de Patrones (Muestra de 20 items)</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $analysis_results['patterns']['tmdb_posters']; ?></div>
                    <div class="stat-label">Posters TMDB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $analysis_results['patterns']['years_in_title']; ?></div>
                    <div class="stat-label">Títulos con Año</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $analysis_results['patterns']['episode_info']; ?></div>
                    <div class="stat-label">Info de Episodios</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $analysis_results['patterns']['quality_info']; ?></div>
                    <div class="stat-label">Info de Calidad</div>
                </div>
            </div>
            
            <?php if (!empty($analysis_results['patterns']['logo_sources'])): ?>
            <h3>🖼️ Fuentes de Logos/Posters</h3>
            <div class="pattern-list">
                <?php foreach ($analysis_results['patterns']['logo_sources'] as $source => $count): ?>
                <div class="pattern-item">
                    <span><?php echo htmlspecialchars($source); ?></span>
                    <span class="<?php echo strpos($source, 'tmdb') !== false ? 'success' : 'warning'; ?>">
                        <?php echo $count; ?> items
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($analysis_results['patterns']['group_categories'])): ?>
            <h3>📂 Categorías Principales</h3>
            <div class="pattern-list">
                <?php 
                arsort($analysis_results['patterns']['group_categories']);
                $top_categories = array_slice($analysis_results['patterns']['group_categories'], 0, 10, true);
                foreach ($top_categories as $category => $count): 
                ?>
                <div class="pattern-item">
                    <span><?php echo htmlspecialchars($category); ?></span>
                    <span><?php echo $count; ?> items</span>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="card">
            <h2>📺 Muestra de Contenido</h2>
            <?php foreach (array_slice($analysis_results['sample_content'], 0, 5) as $item): ?>
            <div class="sample-item">
                <div class="item-title"><?php echo htmlspecialchars($item['title']); ?></div>
                <?php if ($item['group_title']): ?>
                <div class="item-meta">📂 Categoría: <?php echo htmlspecialchars($item['group_title']); ?></div>
                <?php endif; ?>
                <?php if ($item['tvg_id']): ?>
                <div class="item-meta">🆔 TVG ID: <?php echo htmlspecialchars($item['tvg_id']); ?></div>
                <?php endif; ?>
                <?php if ($item['logo_url']): ?>
                <div class="item-meta">
                    🖼️ Logo: 
                    <span class="<?php echo strpos($item['logo_url'], 'tmdb') !== false ? 'success' : 'warning'; ?>">
                        <?php echo strpos($item['logo_url'], 'tmdb') !== false ? 'TMDB' : 'Otro'; ?>
                    </span>
                    <br>
                    <small><?php echo htmlspecialchars(substr($item['logo_url'], 0, 80)) . '...'; ?></small>
                </div>
                <?php if (strpos($item['logo_url'], 'tmdb') !== false): ?>
                <img src="<?php echo htmlspecialchars($item['logo_url']); ?>" alt="Poster" class="item-logo" onerror="this.style.display='none'">
                <?php endif; ?>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="card">
            <h2>💡 Recomendaciones</h2>
            <ul>
                <?php if ($analysis_results['stats']['with_tmdb_logo'] > 0): ?>
                <li class="success">✅ Esta lista tiene <?php echo $analysis_results['stats']['with_tmdb_logo']; ?> elementos con posters TMDB. Usa el <strong>Extractor TMDB</strong> para procesarlos rápidamente.</li>
                <?php endif; ?>
                
                <?php if ($analysis_results['patterns']['years_in_title'] > 10): ?>
                <li class="success">✅ Muchos títulos incluyen años. Esto mejorará la precisión de las búsquedas TMDB.</li>
                <?php endif; ?>
                
                <?php if ($analysis_results['patterns']['quality_info'] > 5): ?>
                <li class="warning">⚠️ Muchos títulos incluyen información de calidad. Considera mejorar la limpieza de títulos.</li>
                <?php endif; ?>
                
                <?php if ($analysis_results['stats']['with_tmdb_logo'] / $analysis_results['stats']['total'] > 0.5): ?>
                <li class="success">✅ Más del 50% del contenido tiene posters TMDB. Esta lista es ideal para extracción automática.</li>
                <?php else: ?>
                <li class="warning">⚠️ Pocos elementos con posters TMDB. Necesitarás usar búsquedas por título.</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
