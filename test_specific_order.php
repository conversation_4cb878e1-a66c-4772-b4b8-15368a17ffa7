<?php
// Test específico para el pedido que está causando problemas
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener el pedido específico (ID 1)
$order_id = $_GET['id'] ?? 1;
$stmt = $pdo->prepare("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.id = ?
");
$stmt->execute([$order_id]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    die("Pedido no encontrado");
}

// Generar URL de prueba
$search_url = 'm3u_search_tmdb.php?q=' . urlencode($order['title']) . '&auto_search=1';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Test Pedido Específico</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .test-card {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 1rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #1a1a1a;
            border-radius: 4px;
            border-left: 4px solid #46d347;
        }
        
        .test-section h3 {
            color: #46d347;
            margin-bottom: 1rem;
        }
        
        .data-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #404040;
        }
        
        .data-row:last-child {
            border-bottom: none;
        }
        
        .data-label {
            color: #888;
            font-weight: bold;
        }
        
        .data-value {
            color: white;
            font-family: monospace;
            background: #0f1419;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }
        
        .url-section {
            background: #0f1419;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #3b82f6;
            margin: 1rem 0;
        }
        
        .url-section h4 {
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }
        
        .url-link {
            color: #3b82f6;
            text-decoration: none;
            word-break: break-all;
            display: block;
            margin: 0.5rem 0;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }
        
        .test-button {
            background: #46d347;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 1rem 0;
        }
        
        .test-button:hover {
            background: #3bc55a;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .warning {
            background: #f59e0b;
            color: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-weight: bold;
        }
        
        .success {
            background: #10b981;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🔍 Test Pedido #<?php echo $order['id']; ?></h1>
            <p>Verificación específica del enlace de coincidencias</p>
        </div>
        
        <div class="test-card">
            <div class="test-section">
                <h3>📋 Datos del Pedido</h3>
                
                <div class="data-row">
                    <div class="data-label">ID:</div>
                    <div class="data-value"><?php echo $order['id']; ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Usuario:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['username'] ?? 'Usuario #' . $order['user_id']); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Título:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['title']); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Título (Raw):</div>
                    <div class="data-value"><?php echo bin2hex($order['title']); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Título (Length):</div>
                    <div class="data-value"><?php echo strlen($order['title']); ?> caracteres</div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Tipo:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['media_type'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Año:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['year'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">TMDB ID:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['tmdb_id'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="data-row">
                    <div class="data-label">Estado:</div>
                    <div class="data-value"><?php echo htmlspecialchars($order['status']); ?></div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🔗 URL de Coincidencias</h3>
                
                <div class="url-section">
                    <h4>URL Generada:</h4>
                    <a href="<?php echo $search_url; ?>" target="_blank" class="url-link"><?php echo htmlspecialchars($search_url); ?></a>
                </div>
                
                <div class="url-section">
                    <h4>Parámetros Decodificados:</h4>
                    <div style="color: #cbd5e1;">
                        <strong>q:</strong> "<?php echo htmlspecialchars($order['title']); ?>"<br>
                        <strong>auto_search:</strong> 1
                    </div>
                </div>
                
                <div class="url-section">
                    <h4>URL Encoded:</h4>
                    <div style="color: #cbd5e1; font-family: monospace;">
                        <?php echo htmlspecialchars(urlencode($order['title'])); ?>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 Pruebas</h3>
                
                <?php if (empty(trim($order['title']))): ?>
                <div class="warning">
                    ⚠️ PROBLEMA: El título está vacío o solo contiene espacios
                </div>
                <?php elseif (strlen($order['title']) < 2): ?>
                <div class="warning">
                    ⚠️ PROBLEMA: El título es demasiado corto (menos de 2 caracteres)
                </div>
                <?php else: ?>
                <div class="success">
                    ✅ El título parece válido para búsqueda
                </div>
                <?php endif; ?>
                
                <a href="<?php echo $search_url; ?>" target="_blank" class="test-button">
                    🔍 Probar Búsqueda de Coincidencias
                </a>
                
                <div style="margin-top: 1rem;">
                    <strong>Instrucciones:</strong>
                    <ol style="color: #cbd5e1; line-height: 1.6; margin-top: 0.5rem;">
                        <li>Haz clic en el botón "Probar Búsqueda de Coincidencias"</li>
                        <li>Se abrirá una nueva pestaña con m3u_search_tmdb.php</li>
                        <li>Verifica que aparezca el banner verde "Búsqueda desde Panel Admin"</li>
                        <li>Verifica que el término de búsqueda sea correcto</li>
                        <li>Verifica que se muestren resultados (si existen)</li>
                    </ol>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🔧 Debug Adicional</h3>
                
                <div style="background: #0f1419; padding: 1rem; border-radius: 4px; margin-top: 1rem;">
                    <strong>Datos Completos del Pedido:</strong>
                    <pre style="color: #cbd5e1; font-size: 0.9rem; overflow-x: auto; margin-top: 0.5rem;"><?php echo htmlspecialchars(json_encode($order, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                </div>
                
                <div style="margin-top: 1rem;">
                    <strong>Pruebas Adicionales:</strong>
                    <ul style="color: #cbd5e1; line-height: 1.6;">
                        <li><a href="debug_admin_orders.php" style="color: #3b82f6;">Ver todos los pedidos (Debug)</a></li>
                        <li><a href="m3u_search_tmdb.php" style="color: #3b82f6;">Ir a búsqueda TMDB (sin parámetros)</a></li>
                        <li><a href="admin.php" style="color: #3b82f6;">Volver al panel admin</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
