<?php
// Herramienta de diagnóstico para búsquedas TMDB
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar títulos de diferentes formas
function cleanTitleVariations($title) {
    $variations = [];
    
    // Original
    $variations['original'] = $title;
    
    // Básica: solo quitar año
    $variations['basic'] = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    
    // Intermedia: quitar año y episodios
    $temp = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    $variations['intermediate'] = trim(preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/', '', $temp));
    
    // Avanzada: quitar mucha información extra
    $temp = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    $temp = trim(preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/', '', $temp));
    $temp = trim(preg_replace('/\s*(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV).*$/i', '', $temp));
    $temp = trim(preg_replace('/\s*\[.*?\]/', '', $temp));
    $temp = trim(preg_replace('/\s*\(.*?\)/', '', $temp));
    $variations['advanced'] = $temp;
    
    // Ultra limpia: solo palabras principales
    $temp = $variations['advanced'];
    $temp = preg_replace('/[^\w\s]/', ' ', $temp);
    $temp = preg_replace('/\s+/', ' ', $temp);
    $variations['ultra_clean'] = trim($temp);
    
    return $variations;
}

// Función para buscar en TMDB con debug
function debugTMDBSearch($query, $language = 'es-MX') {
    $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=$language&query=" . urlencode($query);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'url' => $url,
        'http_code' => $http_code,
        'error' => $error,
        'response' => $response,
        'data' => $response ? json_decode($response, true) : null
    ];
}

$debug_results = [];
$sample_titles = [];

// Obtener muestra de títulos para analizar
if (isset($_POST['analyze_sample'])) {
    $list_id = (int)$_POST['list_id'];
    
    $stmt = $pdo->prepare("
        SELECT id, title 
        FROM m3u_content 
        WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)
        LIMIT 5
    ");
    $stmt->execute([$list_id]);
    $sample_titles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sample_titles as $item) {
        $variations = cleanTitleVariations($item['title']);
        $item_debug = [
            'id' => $item['id'],
            'original_title' => $item['title'],
            'variations' => $variations,
            'searches' => []
        ];
        
        // Probar cada variación
        foreach ($variations as $type => $clean_title) {
            if (strlen($clean_title) > 2) {
                // Probar en español mexicano
                $result_es_mx = debugTMDBSearch($clean_title, 'es-MX');
                $item_debug['searches'][$type]['es-MX'] = $result_es_mx;
                
                // Probar en inglés si no hay resultados
                if (!$result_es_mx['data'] || empty($result_es_mx['data']['results'])) {
                    $result_en = debugTMDBSearch($clean_title, 'en-US');
                    $item_debug['searches'][$type]['en-US'] = $result_en;
                }
            }
        }
        
        $debug_results[] = $item_debug;
    }
}

// Obtener listas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as pending
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name
    HAVING pending > 0
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Búsquedas TMDB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        button {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .debug-item {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #404040;
        }
        
        .original-title {
            color: #46d347;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .variation {
            margin-bottom: 10px;
            padding: 10px;
            background: #2d2d2d;
            border-radius: 4px;
        }
        
        .variation-title {
            color: #f59e0b;
            font-weight: bold;
        }
        
        .search-result {
            margin-left: 20px;
            margin-top: 5px;
            padding: 5px;
            background: #404040;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .success {
            color: #10b981;
        }
        
        .error {
            color: #ef4444;
        }
        
        .warning {
            color: #f59e0b;
        }
        
        .result-item {
            background: #1a1a1a;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🔍 Debug Búsquedas TMDB</h1>
        
        <div class="card">
            <h2>📋 Analizar Muestra de Títulos</h2>
            <p>Esta herramienta analiza una muestra de títulos para ver por qué no se encuentran en TMDB.</p>
            
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['pending']; ?> pendientes)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="analyze_sample">
                    🔍 Analizar 5 Títulos de Muestra
                </button>
            </form>
        </div>
        
        <?php if (!empty($debug_results)): ?>
        <div class="card">
            <h2>📊 Resultados del Análisis</h2>
            
            <?php foreach ($debug_results as $item): ?>
            <div class="debug-item">
                <div class="original-title">
                    📺 Título Original: <?php echo htmlspecialchars($item['original_title']); ?>
                </div>
                
                <?php foreach ($item['variations'] as $type => $variation): ?>
                <div class="variation">
                    <div class="variation-title">
                        🔧 <?php echo ucfirst(str_replace('_', ' ', $type)); ?>: 
                        "<?php echo htmlspecialchars($variation); ?>"
                    </div>
                    
                    <?php if (isset($item['searches'][$type])): ?>
                        <?php foreach ($item['searches'][$type] as $lang => $search): ?>
                        <div class="search-result">
                            <strong><?php echo $lang; ?>:</strong>
                            <?php if ($search['http_code'] == 200): ?>
                                <?php if ($search['data'] && !empty($search['data']['results'])): ?>
                                    <span class="success">✅ <?php echo count($search['data']['results']); ?> resultados encontrados</span>
                                    <?php foreach (array_slice($search['data']['results'], 0, 2) as $result): ?>
                                    <div class="result-item">
                                        🎬 <?php echo htmlspecialchars($result['title'] ?? $result['name']); ?>
                                        (ID: <?php echo $result['id']; ?>, Tipo: <?php echo $result['media_type'] ?? 'unknown'; ?>)
                                    </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <span class="warning">⚠️ Sin resultados</span>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="error">❌ Error HTTP <?php echo $search['http_code']; ?></span>
                                <?php if ($search['error']): ?>
                                    <br>Error: <?php echo htmlspecialchars($search['error']); ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="card">
            <h2>💡 Recomendaciones</h2>
            <ul>
                <?php
                $total_searches = 0;
                $successful_searches = 0;
                
                foreach ($debug_results as $item) {
                    foreach ($item['searches'] as $type => $searches) {
                        foreach ($searches as $lang => $search) {
                            $total_searches++;
                            if ($search['data'] && !empty($search['data']['results'])) {
                                $successful_searches++;
                            }
                        }
                    }
                }
                
                $success_rate = $total_searches > 0 ? round(($successful_searches / $total_searches) * 100, 1) : 0;
                ?>
                <li><strong>Tasa de éxito:</strong> <?php echo $success_rate; ?>% (<?php echo $successful_searches; ?>/<?php echo $total_searches; ?> búsquedas exitosas)</li>
                
                <?php if ($success_rate < 50): ?>
                <li class="warning">⚠️ Baja tasa de éxito. Considera mejorar la limpieza de títulos.</li>
                <?php endif; ?>
                
                <?php if ($successful_searches > 0): ?>
                <li class="success">✅ Algunas búsquedas funcionan. El analizador puede mejorarse.</li>
                <?php endif; ?>
                
                <li>🔧 Prueba diferentes variaciones de limpieza de títulos</li>
                <li>🌍 Considera buscar en múltiples idiomas (es-MX, en-US, es-ES)</li>
                <li>📝 Los títulos muy específicos o con mucha información extra son más difíciles de encontrar</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
