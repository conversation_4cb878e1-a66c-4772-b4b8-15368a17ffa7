<?php
// Insertar datos de prueba si no existen
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>📊 Insertar Datos de Prueba</h1>";

try {
    // 1. Verificar y crear usuarios
    echo "<h2>👥 Creando Usuarios</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users");
    $stmt->execute();
    $user_count = $stmt->fetchColumn();
    
    if ($user_count == 0) {
        $users = [
            [1, 'usuario', '<EMAIL>', 'demo_hash'],
            [2, 'admin', '<EMAIL>', 'admin_hash']
        ];
        
        foreach ($users as $user) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO users (id, username, email, password_hash) VALUES (?, ?, ?, ?)");
            $stmt->execute($user);
            echo "✅ Usuario '{$user[1]}' creado<br>";
        }
    } else {
        echo "✅ Ya existen $user_count usuarios<br>";
    }
    
    // 2. Verificar y crear aplicaciones
    echo "<h2>📱 Creando Aplicaciones</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM support_apps");
    $stmt->execute();
    $app_count = $stmt->fetchColumn();
    
    if ($app_count < 5) {
        $apps = [
            [1, 'RGS IPTV Android', '2.1.0', 'android', 'Aplicación oficial para dispositivos Android con todas las funciones', 'published'],
            [2, 'RGS IPTV iOS', '2.0.5', 'ios', 'Aplicación oficial para iPhone y iPad', 'published'],
            [3, 'RGS Player Windows', '1.8.2', 'windows', 'Reproductor para Windows 10/11', 'published'],
            [4, 'RGS Smart TV', '1.5.0', 'smart_tv', 'Aplicación para Smart TV Samsung y LG', 'published'],
            [5, 'RGS FireStick', '1.3.1', 'firestick', 'Aplicación para Amazon Fire TV Stick', 'published'],
            [6, 'RGS macOS', '1.2.0', 'macos', 'Reproductor para macOS', 'published'],
            [7, 'RGS Web Player', '3.0.1', 'web', 'Reproductor web sin instalación', 'published'],
            [8, 'RGS Android TV', '1.4.0', 'android', 'Versión optimizada para Android TV', 'published'],
            [9, 'RGS Roku', '1.1.0', 'other', 'Canal oficial para dispositivos Roku', 'published']
        ];
        
        foreach ($apps as $app) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO support_apps (id, name, version, platform, description, status) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute($app);
            echo "✅ App '{$app[1]}' creada<br>";
        }
    } else {
        echo "✅ Ya existen $app_count aplicaciones<br>";
    }
    
    // 3. Verificar y crear artículos de ayuda
    echo "<h2>❓ Creando Artículos de Ayuda</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM help_articles");
    $stmt->execute();
    $article_count = $stmt->fetchColumn();
    
    if ($article_count < 5) {
        $articles = [
            [1, 'Cómo configurar tu lista M3U', 'Guía paso a paso para configurar tu lista M3U en diferentes dispositivos:\n\n1. Descarga la aplicación oficial\n2. Abre la aplicación\n3. Ve a Configuración\n4. Ingresa tu URL M3U\n5. Guarda la configuración\n6. Disfruta del contenido', 'setup', 'published', 1],
            [2, 'Solución a problemas de buffering', 'Si experimentas cortes o buffering, sigue estos pasos:\n\n1. Verifica tu conexión a internet\n2. Reinicia tu router\n3. Cambia la calidad de video\n4. Prueba con otro servidor\n5. Contacta soporte si persiste', 'troubleshooting', 'published', 1],
            [3, 'Instalación en Smart TV', 'Instrucciones para instalar la aplicación en tu Smart TV:\n\n1. Ve a la tienda de aplicaciones\n2. Busca "RGS IPTV"\n3. Descarga e instala\n4. Abre la aplicación\n5. Configura con tus credenciales', 'apps', 'published', 0],
            [4, 'Configuración de EPG', 'Cómo configurar la guía electrónica de programas:\n\n1. Ve a Configuración\n2. Selecciona EPG\n3. Ingresa la URL del EPG\n4. Actualiza la guía\n5. Disfruta de la programación', 'setup', 'published', 0],
            [5, 'Preguntas frecuentes sobre facturación', 'Respuestas a las preguntas más comunes sobre facturación:\n\n- ¿Cuándo se cobra?\n- ¿Cómo cambiar método de pago?\n- ¿Cómo cancelar suscripción?\n- ¿Hay reembolsos?', 'billing', 'published', 0],
            [6, 'Configuración en Android', 'Guía completa para configurar la app en Android:\n\n1. Instala la aplicación\n2. Abre y acepta permisos\n3. Configura tu lista\n4. Ajusta calidad de video\n5. Personaliza la interfaz', 'apps', 'published', 1],
            [7, 'Solución de problemas de audio', 'Cómo resolver problemas de audio:\n\n1. Verifica configuración de audio\n2. Cambia codec de audio\n3. Reinicia la aplicación\n4. Verifica drivers de audio', 'troubleshooting', 'published', 0],
            [8, 'Gestión de cuenta de usuario', 'Cómo gestionar tu cuenta:\n\n1. Accede a tu perfil\n2. Cambia contraseña\n3. Actualiza información\n4. Gestiona suscripciones', 'account', 'published', 0],
            [9, 'Información general del servicio', 'Todo lo que necesitas saber sobre nuestro servicio IPTV:\n\n- Qué incluye\n- Cómo funciona\n- Requisitos técnicos\n- Soporte disponible', 'general', 'published', 1]
        ];
        
        foreach ($articles as $article) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO help_articles (id, title, content, category, status, is_featured) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute($article);
            echo "✅ Artículo '{$article[1]}' creado<br>";
        }
    } else {
        echo "✅ Ya existen $article_count artículos<br>";
    }
    
    // 4. Verificar y crear códigos de activación
    echo "<h2>🔑 Creando Códigos de Activación</h2>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM activation_codes");
    $stmt->execute();
    $code_count = $stmt->fetchColumn();
    
    if ($code_count < 5) {
        $codes = [
            [1, 'DEMO2024', 'Lista Premium Demo', 'Código de demostración para acceso premium', 'active'],
            [2, 'TEST123', 'Lista de Prueba', 'Lista de prueba con canales básicos', 'active'],
            [3, 'PREMIUM2024', 'Lista Premium Completa', 'Acceso completo a todos los canales premium', 'active'],
            [4, 'SPORTS2024', 'Lista Deportes', 'Canales especializados en deportes', 'active'],
            [5, 'MOVIES2024', 'Lista Películas', 'Canales de películas y series', 'active']
        ];
        
        foreach ($codes as $code) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO activation_codes (id, code, list_name, description, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute($code);
            echo "✅ Código '{$code[1]}' creado<br>";
        }
    } else {
        echo "✅ Ya existen $code_count códigos<br>";
    }
    
    // 5. Crear algunos tickets de ejemplo
    echo "<h2>🎫 Creando Tickets de Ejemplo</h2>";

    // Primero verificar si la tabla tiene la estructura correcta
    try {
        $stmt = $pdo->query("DESCRIBE support_tickets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('subject', $columns)) {
            echo "❌ Tabla support_tickets no tiene la columna 'subject'<br>";
            echo "Por favor ejecuta <a href='fix_tables.php'>fix_tables.php</a> primero<br>";
        } else {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM support_tickets");
            $stmt->execute();
            $ticket_count = $stmt->fetchColumn();

            if ($ticket_count < 3) {
                $tickets = [
                    [1, 1, 'Problema con la reproducción', 'Los canales se cortan constantemente durante la reproducción', 'high', 'technical', 'open'],
                    [2, 1, 'Solicitud de canal', 'Me gustaría que agreguen CNN en español a la lista', 'medium', 'content', 'in_progress'],
                    [3, 1, 'Error en la aplicación', 'La app se cierra al abrir ciertos canales de deportes', 'urgent', 'app', 'resolved']
                ];

                foreach ($tickets as $ticket) {
                    try {
                        $stmt = $pdo->prepare("INSERT IGNORE INTO support_tickets (id, user_id, subject, description, priority, category, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute($ticket);
                        echo "✅ Ticket '{$ticket[2]}' creado<br>";
                    } catch (Exception $e) {
                        echo "❌ Error creando ticket '{$ticket[2]}': " . $e->getMessage() . "<br>";
                    }
                }
            } else {
                echo "✅ Ya existen $ticket_count tickets<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ Error verificando tabla support_tickets: " . $e->getMessage() . "<br>";
        echo "Por favor ejecuta <a href='fix_tables.php'>fix_tables.php</a> primero<br>";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin-top: 2rem;'>";
    echo "<h2>🎉 ¡Datos de Prueba Insertados!</h2>";
    echo "<p>Todos los datos de prueba han sido insertados correctamente.</p>";
    echo "<br><strong>Ahora puedes probar:</strong><br>";
    echo "• <a href='user_apps.php'>📱 Ver Aplicaciones (9 apps)</a><br>";
    echo "• <a href='user_help.php'>❓ Ver Artículos de Ayuda (9 artículos)</a><br>";
    echo "• <a href='user_tickets.php'>🎫 Crear Tickets</a><br>";
    echo "• <a href='user_chat.php'>💬 Iniciar Chat</a><br>";
    echo "• <a href='user_channels.php'>📺 Solicitar Canales</a><br>";
    echo "• <a href='admin2.php'>⚙️ Panel de Administración</a><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
