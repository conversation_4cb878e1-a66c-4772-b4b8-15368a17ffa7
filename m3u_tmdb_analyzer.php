<?php
// Analizador M3U con integración TMDB mejorada
session_start();

// Configurar límites para evitar timeouts
ini_set('max_execution_time', 300); // 5 minutos
ini_set('memory_limit', '512M');
set_time_limit(300);

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar títulos para búsqueda TMDB
function cleanTitleForTMDB($title) {
    // Remover información de temporadas/episodios
    $title = preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/i', '', $title);
    $title = preg_replace('/\s*[Tt]emporada\s*\d+.*$/i', '', $title);
    $title = preg_replace('/\s*[Ss]eason\s*\d+.*$/i', '', $title);
    
    // Remover años entre paréntesis
    $title = preg_replace('/\s*\(\d{4}\)/', '', $title);
    
    // Remover información de calidad
    $title = preg_replace('/\s*(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV).*$/i', '', $title);
    
    // Remover caracteres especiales y limpiar
    $title = preg_replace('/[^\w\s\-\.]/', ' ', $title);
    $title = preg_replace('/\s+/', ' ', $title);
    
    return trim($title);
}

// Función para detectar tipo de media
function detectMediaType($title) {
    // Buscar patrones de series
    if (preg_match('/[Ss]\d+[Ee]\d+|[Tt]emporada|[Ss]eason/i', $title)) {
        return 'tv';
    }
    
    // Por defecto, asumir película
    return 'movie';
}

// Función para buscar en TMDB con múltiples intentos
function searchTMDBWithFallback($title, $media_type = null) {
    $cleaned_title = cleanTitleForTMDB($title);
    
    // Intentar búsqueda específica por tipo si se conoce
    if ($media_type) {
        $endpoint = $media_type === 'tv' ? '/search/tv' : '/search/movie';
        $result = makeTMDBRequest($endpoint, ['query' => $cleaned_title]);
        if ($result && !empty($result['results'])) {
            return $result['results'][0];
        }
    }
    
    // Búsqueda general (multi)
    $result = searchTMDBContent($cleaned_title);
    if ($result && !empty($result['results'])) {
        return $result['results'][0];
    }
    
    // Intentar con título original sin limpiar
    $result = searchTMDBContent($title);
    if ($result && !empty($result['results'])) {
        return $result['results'][0];
    }
    
    return null;
}

// Función para analizar contenido M3U y enriquecerlo con TMDB
function analyzeM3UContentWithTMDB($list_id, $limit = 100) {
    global $pdo;

    try {
        // Obtener contenido sin TMDB ID (sin parámetro en LIMIT)
        $limit = (int)$limit; // Asegurar que sea entero
        $stmt = $pdo->prepare("
            SELECT id, title, media_type
            FROM m3u_content
            WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)
            LIMIT $limit
        ");
        $stmt->execute([$list_id]);
        $content = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $processed = 0;
        $found = 0;
        $errors = 0;

        foreach ($content as $item) {
            $processed++;

            try {
                // Detectar tipo de media si no está definido
                $media_type = $item['media_type'] ?: detectMediaType($item['title']);

                // Buscar en TMDB con manejo de errores
                $tmdb_result = searchTMDBWithFallback($item['title'], $media_type);

                if ($tmdb_result) {
                    $found++;

                    // Actualizar registro con información TMDB
                    $update_stmt = $pdo->prepare("
                        UPDATE m3u_content SET
                            tmdb_id = ?,
                            media_type = ?,
                            tmdb_title = ?,
                            tmdb_original_title = ?,
                            tmdb_poster_path = ?,
                            tmdb_overview = ?,
                            tmdb_language = ?,
                            tmdb_vote_average = ?,
                            tmdb_release_date = ?
                        WHERE id = ?
                    ");

                    $release_date = null;
                    if (isset($tmdb_result['release_date']) && $tmdb_result['release_date']) {
                        $release_date = $tmdb_result['release_date'];
                    } elseif (isset($tmdb_result['first_air_date']) && $tmdb_result['first_air_date']) {
                        $release_date = $tmdb_result['first_air_date'];
                    }

                    $update_stmt->execute([
                        $tmdb_result['id'],
                        $tmdb_result['media_type'] ?? $media_type,
                        $tmdb_result['title'] ?? $tmdb_result['name'],
                        $tmdb_result['original_title'] ?? $tmdb_result['original_name'],
                        $tmdb_result['poster_path'],
                        $tmdb_result['overview'],
                        $tmdb_result['original_language'],
                        $tmdb_result['vote_average'] ?? null,
                        $release_date,
                        $item['id']
                    ]);
                }

            } catch (Exception $e) {
                $errors++;
                error_log("Error procesando item {$item['id']}: " . $e->getMessage());
            }

            // Pequeña pausa para no sobrecargar la API
            usleep(200000); // 0.2 segundos

            // Mostrar progreso cada 10 elementos
            if ($processed % 10 == 0) {
                echo "<script>console.log('Procesados: $processed, Encontrados: $found');</script>";
                flush();
            }
        }

        return ['processed' => $processed, 'found' => $found, 'errors' => $errors];

    } catch (Exception $e) {
        error_log("Error en analyzeM3UContentWithTMDB: " . $e->getMessage());
        return ['processed' => 0, 'found' => 0, 'errors' => 1, 'error_message' => $e->getMessage()];
    }
}

// Procesar si se envía el formulario
$result = null;
if (isset($_POST['analyze_list'])) {
    $list_id = $_POST['list_id'];
    $limit = $_POST['limit'] ?? 100;
    $result = analyzeM3UContentWithTMDB($list_id, $limit);
}

// Obtener listas disponibles con información detallada
$stmt = $pdo->query("
    SELECT
        l.id,
        l.name,
        l.total_items,
        COUNT(c.id) as actual_items,
        COUNT(CASE WHEN c.tmdb_id IS NOT NULL AND c.tmdb_id > 0 THEN 1 END) as with_tmdb,
        COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as without_tmdb
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name, l.total_items
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb,
        COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = 0 THEN 1 END) as without_tmdb
    FROM m3u_content c
    LEFT JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
");
$stats = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Analizador TMDB - RogsMediaTV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent-color);
        }

        .stat-label {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }

        .form-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
        }

        .result-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--primary-color);
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color), #28a745);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-film"></i> Analizador TMDB</h1>
            <p>Enriquece el contenido M3U con información de TMDB</p>
        </div>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                <div class="stat-label">Total de Contenido</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['with_tmdb']); ?></div>
                <div class="stat-label">Con TMDB ID</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['without_tmdb']); ?></div>
                <div class="stat-label">Sin TMDB ID</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total'] > 0 ? round(($stats['with_tmdb']/$stats['total'])*100, 1) : 0; ?>%</div>
                <div class="stat-label">Completado</div>
            </div>
        </div>

        <!-- Formulario de análisis -->
        <div class="form-section">
            <h2>Analizar Lista M3U</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" class="form-select" required>
                        <option value="">Selecciona una lista...</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo number_format($list['actual_items']); ?> items,
                            <?php echo number_format($list['without_tmdb']); ?> sin TMDB,
                            <?php echo $list['actual_items'] > 0 ? round(($list['with_tmdb']/$list['actual_items'])*100, 1) : 0; ?>% completado)
                        </option>
                        <?php endforeach; ?>
                    </select>
                    <small style="color: var(--text-secondary); display: block; margin-top: 0.5rem;">
                        💡 Se muestran: Total de items, elementos sin TMDB ID, y porcentaje completado
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="limit">Límite de procesamiento:</label>
                    <select name="limit" id="limit" class="form-select">
                        <option value="25">25 elementos (Prueba rápida)</option>
                        <option value="50" selected>50 elementos (Recomendado)</option>
                        <option value="100">100 elementos</option>
                        <option value="200">200 elementos</option>
                        <option value="500">500 elementos</option>
                        <option value="1000">1000 elementos (Lento)</option>
                        <option value="2000">2000 elementos (Muy lento)</option>
                    </select>
                    <small style="color: var(--text-secondary); display: block; margin-top: 0.5rem;">
                        ⚠️ Límites altos pueden causar timeouts. Usa 50-100 para mejores resultados.
                    </small>
                </div>

                <button type="submit" name="analyze_list" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Analizar con TMDB
                </button>
            </form>
        </div>

        <!-- Resultados -->
        <?php if ($result): ?>
        <div class="result-section">
            <h2>Resultados del Análisis</h2>

            <?php if (isset($result['error_message'])): ?>
            <div style="background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; color: #ef4444; padding: 1rem; border-radius: 6px; margin-bottom: 1rem;">
                <i class="fas fa-exclamation-triangle"></i>
                Error durante el análisis: <?php echo htmlspecialchars($result['error_message']); ?>
            </div>
            <?php else: ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                Análisis completado exitosamente
            </div>
            <?php endif; ?>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['processed']; ?></div>
                    <div class="stat-label">Elementos Procesados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['found']; ?></div>
                    <div class="stat-label">TMDB IDs Encontrados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['processed'] > 0 ? round(($result['found']/$result['processed'])*100, 1) : 0; ?>%</div>
                    <div class="stat-label">Tasa de Éxito</div>
                </div>
                <?php if (isset($result['errors']) && $result['errors'] > 0): ?>
                <div class="stat-card">
                    <div class="stat-number" style="color: #ef4444;"><?php echo $result['errors']; ?></div>
                    <div class="stat-label">Errores</div>
                </div>
                <?php endif; ?>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo $result['processed'] > 0 ? ($result['found']/$result['processed'])*100 : 0; ?>%"></div>
            </div>

            <?php if ($result['processed'] > 0): ?>
            <div style="margin-top: 1rem; padding: 1rem; background: var(--primary-color); border-radius: 6px;">
                <h4>📊 Resumen:</h4>
                <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                    <li>✅ <strong><?php echo $result['found']; ?></strong> elementos enriquecidos con TMDB</li>
                    <li>❌ <strong><?php echo $result['processed'] - $result['found']; ?></strong> elementos sin coincidencias</li>
                    <?php if (isset($result['errors']) && $result['errors'] > 0): ?>
                    <li>⚠️ <strong><?php echo $result['errors']; ?></strong> errores durante el procesamiento</li>
                    <?php endif; ?>
                </ul>
                <p style="color: var(--text-secondary); margin-top: 1rem;">
                    💡 <strong>Tip:</strong> Puedes ejecutar el análisis varias veces para procesar más contenido.
                </p>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
