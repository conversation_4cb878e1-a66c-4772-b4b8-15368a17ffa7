<?php
// Analizador TMDB Básico - Versión ultra simple
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Conexión básica sin configuraciones especiales
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Verificar si tmdb_config.php existe
if (!file_exists('tmdb_config.php')) {
    die("Error: tmdb_config.php no encontrado");
}

require_once 'tmdb_config.php';

// Verificar configuración TMDB
if (!defined('TMDB_API_KEY') || !defined('TMDB_BASE_URL')) {
    die("Error: Configuración TMDB incompleta");
}

$message = '';
$error = '';

// Procesar solo si se envía el formulario
if (isset($_POST['analyze']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    
    try {
        // Obtener solo 5 elementos sin TMDB ID
        $stmt = $pdo->prepare("
            SELECT id, title 
            FROM m3u_content 
            WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)
            LIMIT 5
        ");
        $stmt->execute([$list_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $processed = 0;
        $found = 0;
        
        foreach ($items as $item) {
            $processed++;
            
            // Limpiar título básico
            $clean_title = trim(preg_replace('/\s*\(\d{4}\)/', '', $item['title']));
            
            // Hacer petición TMDB simple
            $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=es-MX&query=" . urlencode($clean_title);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            curl_close($ch);
            
            if ($response) {
                $data = json_decode($response, true);
                if ($data && isset($data['results']) && !empty($data['results'])) {
                    $tmdb_result = $data['results'][0];
                    $found++;
                    
                    // Actualizar solo TMDB ID
                    $update_stmt = $pdo->prepare("UPDATE m3u_content SET tmdb_id = ? WHERE id = ?");
                    $update_stmt->execute([$tmdb_result['id'], $item['id']]);
                }
            }
            
            // Pausa corta
            sleep(1);
        }
        
        // Verificar cuántos quedan pendientes
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM m3u_content WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)");
        $stmt->execute([$list_id]);
        $remaining = $stmt->fetchColumn();

        $message = "✅ Procesados: $processed elementos | 🎯 Encontrados: $found TMDB IDs | 📋 Pendientes: $remaining";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas básicas
try {
    $stmt = $pdo->query("
        SELECT 
            l.id, 
            l.name,
            COUNT(c.id) as total,
            COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as pending
        FROM m3u_lists l
        LEFT JOIN m3u_content c ON l.id = c.list_id
        WHERE l.is_active = 1
        GROUP BY l.id, l.name
        ORDER BY l.name
    ");
    $lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = "Error obteniendo listas: " . $e->getMessage();
    $lists = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Analizador TMDB Básico</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        button {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        button:hover {
            background: #3bc73c;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #46d347;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🎬 Analizador TMDB Básico</h1>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📋 Procesar Lista M3U</h2>
            
            <div class="info">
                <strong>ℹ️ Información:</strong> Este analizador procesa solo 5 elementos por vez para evitar timeouts. 
                Repite el proceso varias veces para completar listas grandes.
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['total']; ?> total, <?php echo $list['pending']; ?> pendientes)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="analyze">
                    🔍 Analizar 5 Elementos
                </button>
            </form>
        </div>
        
        <?php if (!empty($lists)): ?>
        <div class="card">
            <h2>📊 Estado de las Listas</h2>
            <div class="stats">
                <?php foreach ($lists as $list): ?>
                <div class="stat">
                    <div class="stat-number"><?php echo $list['pending']; ?></div>
                    <div class="stat-label"><?php echo htmlspecialchars($list['name']); ?><br>Pendientes</div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📖 Instrucciones</h2>
            <ol>
                <li>Selecciona una lista de la lista desplegable</li>
                <li>Haz clic en "Analizar 5 Elementos"</li>
                <li>Espera a que termine el procesamiento</li>
                <li>Repite hasta que no queden elementos pendientes</li>
                <li>Para listas grandes, esto puede tomar varios intentos</li>
            </ol>
        </div>
    </div>
</body>
</html>
