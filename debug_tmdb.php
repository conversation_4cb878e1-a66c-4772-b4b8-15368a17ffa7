<?php
// Diagnóstico para identificar problemas
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

echo "<h1>🔍 Diagnóstico TMDB</h1>";
echo "<style>
body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
.success { color: #10b981; }
.error { color: #ef4444; }
.warning { color: #f59e0b; }
.section { margin: 20px 0; padding: 15px; background: #2d2d2d; border-radius: 8px; }
</style>";

echo "<div class='section'>";
echo "<h2>1. 🔧 Configuración PHP</h2>";
echo "<ul>";
echo "<li>Versión PHP: <span class='success'>" . phpversion() . "</span></li>";
echo "<li>Memory Limit: <span class='success'>" . ini_get('memory_limit') . "</span></li>";
echo "<li>Max Execution Time: <span class='success'>" . ini_get('max_execution_time') . "</span></li>";
echo "<li>cURL disponible: " . (extension_loaded('curl') ? "<span class='success'>✅ SÍ</span>" : "<span class='error'>❌ NO</span>") . "</li>";
echo "<li>JSON disponible: " . (extension_loaded('json') ? "<span class='success'>✅ SÍ</span>" : "<span class='error'>❌ NO</span>") . "</li>";
echo "<li>PDO disponible: " . (extension_loaded('pdo') ? "<span class='success'>✅ SÍ</span>" : "<span class='error'>❌ NO</span>") . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>2. 📁 Archivos</h2>";
echo "<ul>";
echo "<li>tmdb_config.php: " . (file_exists('tmdb_config.php') ? "<span class='success'>✅ Existe</span>" : "<span class='error'>❌ No existe</span>") . "</li>";

if (file_exists('tmdb_config.php')) {
    try {
        require_once 'tmdb_config.php';
        echo "<li>tmdb_config.php cargado: <span class='success'>✅ OK</span></li>";
        echo "<li>TMDB_API_KEY definida: " . (defined('TMDB_API_KEY') ? "<span class='success'>✅ SÍ</span>" : "<span class='error'>❌ NO</span>") . "</li>";
        echo "<li>TMDB_BASE_URL definida: " . (defined('TMDB_BASE_URL') ? "<span class='success'>✅ SÍ</span>" : "<span class='error'>❌ NO</span>") . "</li>";
        echo "<li>TMDB_LANGUAGE definida: " . (defined('TMDB_LANGUAGE') ? "<span class='success'>✅ SÍ (" . TMDB_LANGUAGE . ")</span>" : "<span class='error'>❌ NO</span>") . "</li>";
    } catch (Exception $e) {
        echo "<li>Error cargando tmdb_config.php: <span class='error'>" . $e->getMessage() . "</span></li>";
    }
}
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>3. 🗄️ Base de Datos</h2>";
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<ul>";
    echo "<li>Conexión DB: <span class='success'>✅ OK</span></li>";
    
    // Verificar tablas
    $stmt = $pdo->query("SHOW TABLES LIKE 'm3u_lists'");
    echo "<li>Tabla m3u_lists: " . ($stmt->rowCount() > 0 ? "<span class='success'>✅ Existe</span>" : "<span class='error'>❌ No existe</span>") . "</li>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'm3u_content'");
    echo "<li>Tabla m3u_content: " . ($stmt->rowCount() > 0 ? "<span class='success'>✅ Existe</span>" : "<span class='error'>❌ No existe</span>") . "</li>";
    
    // Verificar columnas TMDB
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM m3u_content LIKE 'tmdb_id'");
        echo "<li>Columna tmdb_id: " . ($stmt->rowCount() > 0 ? "<span class='success'>✅ Existe</span>" : "<span class='warning'>⚠️ No existe</span>") . "</li>";
    } catch (Exception $e) {
        echo "<li>Error verificando columnas: <span class='error'>" . $e->getMessage() . "</span></li>";
    }
    
    // Contar listas
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE is_active = 1");
        $count = $stmt->fetchColumn();
        echo "<li>Listas activas: <span class='success'>$count</span></li>";
    } catch (Exception $e) {
        echo "<li>Error contando listas: <span class='error'>" . $e->getMessage() . "</span></li>";
    }
    
    echo "</ul>";
} catch (PDOException $e) {
    echo "<ul><li>Error DB: <span class='error'>" . $e->getMessage() . "</span></li></ul>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>4. 🌐 Conectividad TMDB</h2>";
if (defined('TMDB_API_KEY') && defined('TMDB_BASE_URL')) {
    $test_url = TMDB_BASE_URL . "/search/movie?api_key=" . TMDB_API_KEY . "&language=es-MX&query=test";
    
    echo "<ul>";
    echo "<li>URL de prueba: <code style='color: #10b981;'>" . substr($test_url, 0, 80) . "...</code></li>";
    
    if (extension_loaded('curl')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<li>HTTP Code: " . ($http_code == 200 ? "<span class='success'>200 OK</span>" : "<span class='error'>$http_code</span>") . "</li>";
        
        if ($error) {
            echo "<li>cURL Error: <span class='error'>$error</span></li>";
        } else {
            echo "<li>cURL: <span class='success'>✅ OK</span></li>";
        }
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data) {
                echo "<li>JSON Response: <span class='success'>✅ Válido</span></li>";
                if (isset($data['results'])) {
                    echo "<li>Resultados TMDB: <span class='success'>✅ Disponibles</span></li>";
                }
            } else {
                echo "<li>JSON Response: <span class='error'>❌ Inválido</span></li>";
            }
        }
    } else {
        echo "<li>cURL: <span class='error'>❌ No disponible</span></li>";
    }
    echo "</ul>";
} else {
    echo "<ul><li>Configuración TMDB: <span class='error'>❌ Incompleta</span></li></ul>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>5. 🧪 Prueba Simple</h2>";
if (defined('TMDB_API_KEY') && extension_loaded('curl')) {
    try {
        // Función de búsqueda simple
        function testTMDBSearch($query) {
            $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=es-MX&query=" . urlencode($query);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 && $response) {
                $data = json_decode($response, true);
                return $data && isset($data['results']) ? count($data['results']) : 0;
            }
            return false;
        }
        
        $test_result = testTMDBSearch("Avengers");
        echo "<ul>";
        echo "<li>Búsqueda 'Avengers': " . ($test_result !== false ? "<span class='success'>✅ $test_result resultados</span>" : "<span class='error'>❌ Error</span>") . "</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<ul><li>Error en prueba: <span class='error'>" . $e->getMessage() . "</span></li></ul>";
    }
} else {
    echo "<ul><li>Prueba: <span class='error'>❌ No se puede realizar</span></li></ul>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>6. 💡 Recomendaciones</h2>";
echo "<ul>";

if (!extension_loaded('curl')) {
    echo "<li><span class='error'>❌ Instalar extensión cURL de PHP</span></li>";
}

if (!defined('TMDB_API_KEY')) {
    echo "<li><span class='error'>❌ Configurar TMDB_API_KEY en tmdb_config.php</span></li>";
}

if (isset($pdo)) {
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM m3u_content LIKE 'tmdb_id'");
        if ($stmt->rowCount() == 0) {
            echo "<li><span class='warning'>⚠️ Ejecutar script SQL para agregar columnas TMDB</span></li>";
        }
    } catch (Exception $e) {
        // Ignorar error
    }
}

echo "<li><span class='success'>✅ Usar m3u_tmdb_basic.php para análisis simple</span></li>";
echo "</ul>";
echo "</div>";

echo "<br><a href='admin.php' style='color: #10b981;'>← Volver al admin</a>";
?>
