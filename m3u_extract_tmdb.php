<?php
// Extractor de TMDB IDs desde metadatos M3U
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para extraer TMDB ID de URL de poster
function extractTMDBIdFromPoster($poster_url) {
    // Buscar patrones como: /w600_and_h900_bestv2/bkIhygnbv9ydlDo4SEsOcqGgQD4.jpg
    if (preg_match('/image\.tmdb\.org.*?\/([a-zA-Z0-9]+)\.jpg/', $poster_url, $matches)) {
        return $matches[1]; // Este es el poster path, no el ID
    }
    return null;
}

// Función para obtener TMDB ID desde poster path
function getTMDBIdFromPosterPath($poster_path) {
    if (!$poster_path) return null;
    
    // Buscar en TMDB usando el poster path (esto es más complejo)
    // Por ahora, intentaremos extraer de la URL si tiene un patrón específico
    return null;
}

// Función para extraer año del título
function extractYearFromTitle($title) {
    if (preg_match('/\((\d{4})\)/', $title, $matches)) {
        return (int)$matches[1];
    }
    return null;
}

// Función para buscar TMDB ID por título y año
function searchTMDBByTitleAndYear($title, $year = null) {
    $clean_title = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    
    $query_params = [
        'api_key' => TMDB_API_KEY,
        'language' => 'es-MX',
        'query' => $clean_title
    ];
    
    if ($year) {
        $query_params['year'] = $year;
    }
    
    $url = TMDB_BASE_URL . "/search/multi?" . http_build_query($query_params);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 8);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code == 200 && $response) {
        $data = json_decode($response, true);
        if ($data && isset($data['results']) && !empty($data['results'])) {
            return $data['results'][0];
        }
    }
    
    return null;
}

// Procesar lista
$message = '';
$error = '';

if (isset($_POST['extract_tmdb']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    $batch_size = (int)($_POST['batch_size'] ?? 20);
    
    try {
        // Obtener elementos con logo_url pero sin tmdb_id
        $stmt = $pdo->prepare("
            SELECT id, title, logo_url 
            FROM m3u_content 
            WHERE list_id = ? 
            AND (tmdb_id IS NULL OR tmdb_id = 0)
            AND logo_url IS NOT NULL 
            AND logo_url != ''
            LIMIT $batch_size
        ");
        $stmt->execute([$list_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $processed = 0;
        $found_from_poster = 0;
        $found_from_search = 0;
        $errors = 0;
        
        foreach ($items as $item) {
            $processed++;
            
            try {
                $tmdb_result = null;
                $source = '';
                
                // 1. Intentar extraer de poster URL
                if (strpos($item['logo_url'], 'image.tmdb.org') !== false) {
                    $poster_path = extractTMDBIdFromPoster($item['logo_url']);
                    if ($poster_path) {
                        // El poster path no nos da el ID directamente, pero podemos usarlo
                        // Buscar por título y año, y verificar si el poster coincide
                        $year = extractYearFromTitle($item['title']);
                        $search_result = searchTMDBByTitleAndYear($item['title'], $year);
                        
                        if ($search_result && isset($search_result['poster_path']) && 
                            strpos($item['logo_url'], $search_result['poster_path']) !== false) {
                            $tmdb_result = $search_result;
                            $source = 'poster_match';
                            $found_from_poster++;
                        }
                    }
                }
                
                // 2. Si no se encontró por poster, buscar por título y año
                if (!$tmdb_result) {
                    $year = extractYearFromTitle($item['title']);
                    $tmdb_result = searchTMDBByTitleAndYear($item['title'], $year);
                    if ($tmdb_result) {
                        $source = 'title_search';
                        $found_from_search++;
                    }
                }
                
                // 3. Actualizar si se encontró
                if ($tmdb_result) {
                    $update_stmt = $pdo->prepare("
                        UPDATE m3u_content SET 
                            tmdb_id = ?,
                            tmdb_title = ?,
                            tmdb_poster_path = ?,
                            media_type = ?,
                            tmdb_year = ?
                        WHERE id = ?
                    ");
                    
                    $media_type = 'unknown';
                    if (isset($tmdb_result['media_type'])) {
                        $media_type = $tmdb_result['media_type'] === 'movie' ? 'movie' : 'tv';
                    }
                    
                    $tmdb_year = null;
                    if (isset($tmdb_result['release_date'])) {
                        $tmdb_year = date('Y', strtotime($tmdb_result['release_date']));
                    } elseif (isset($tmdb_result['first_air_date'])) {
                        $tmdb_year = date('Y', strtotime($tmdb_result['first_air_date']));
                    }
                    
                    $update_stmt->execute([
                        $tmdb_result['id'],
                        $tmdb_result['title'] ?? $tmdb_result['name'],
                        $tmdb_result['poster_path'],
                        $media_type,
                        $tmdb_year,
                        $item['id']
                    ]);
                }
                
            } catch (Exception $e) {
                $errors++;
            }
            
            // Pausa corta
            usleep(200000); // 0.2 segundos
        }
        
        // Contar pendientes
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM m3u_content WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)");
        $stmt->execute([$list_id]);
        $remaining = $stmt->fetchColumn();
        
        $total_found = $found_from_poster + $found_from_search;
        $message = "✅ Procesados: $processed | 🎯 Encontrados: $total_found (Poster: $found_from_poster, Búsqueda: $found_from_search) | ❌ Errores: $errors | 📋 Pendientes: $remaining";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas con información detallada
try {
    $stmt = $pdo->query("
        SELECT 
            l.id, 
            l.name,
            COUNT(c.id) as total,
            COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as pending,
            COUNT(CASE WHEN c.tmdb_id IS NOT NULL AND c.tmdb_id > 0 THEN 1 END) as completed,
            COUNT(CASE WHEN c.logo_url IS NOT NULL AND c.logo_url != '' AND c.logo_url LIKE '%tmdb%' THEN 1 END) as with_tmdb_poster
        FROM m3u_lists l
        LEFT JOIN m3u_content c ON l.id = c.list_id
        WHERE l.is_active = 1
        GROUP BY l.id, l.name
        ORDER BY with_tmdb_poster DESC, pending DESC, l.name
    ");
    $lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = "Error obteniendo listas: " . $e->getMessage();
    $lists = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Extractor TMDB desde M3U</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .batch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .batch-btn {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-align: center;
        }
        
        .batch-btn:hover {
            background: #3bc73c;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .list-item {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            border: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-info {
            flex-grow: 1;
        }
        
        .list-name {
            font-weight: bold;
            color: #46d347;
        }
        
        .list-stats {
            color: #b0b0b0;
            font-size: 0.9em;
        }
        
        .tmdb-indicator {
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        
        .info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🎬 Extractor TMDB desde M3U</h1>
        
        <div class="info">
            <strong>💡 Información:</strong> Esta herramienta extrae TMDB IDs desde los metadatos de las listas M3U. 
            Muchas listas ya contienen URLs de posters de TMDB que podemos usar para identificar el contenido.
        </div>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📋 Extraer TMDB desde Metadatos</h2>
            
            <form method="POST" id="extractForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" <?php echo (isset($_POST['list_id']) && $_POST['list_id'] == $list['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['pending']; ?> pendientes, <?php echo $list['with_tmdb_poster']; ?> con poster TMDB)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tamaño del Lote:</label>
                    <div class="batch-options">
                        <button type="submit" name="extract_tmdb" value="1" onclick="setBatchSize(10)" class="batch-btn">
                            10 elementos
                        </button>
                        <button type="submit" name="extract_tmdb" value="1" onclick="setBatchSize(20)" class="batch-btn">
                            20 elementos
                        </button>
                        <button type="submit" name="extract_tmdb" value="1" onclick="setBatchSize(50)" class="batch-btn">
                            50 elementos
                        </button>
                        <button type="submit" name="extract_tmdb" value="1" onclick="setBatchSize(100)" class="batch-btn">
                            100 elementos
                        </button>
                    </div>
                    <input type="hidden" name="batch_size" id="batch_size" value="20">
                </div>
            </form>
        </div>
        
        <?php if (!empty($lists)): ?>
        <div class="card">
            <h2>📊 Estado de las Listas</h2>
            <?php foreach ($lists as $list): ?>
            <div class="list-item">
                <div class="list-info">
                    <div class="list-name"><?php echo htmlspecialchars($list['name']); ?></div>
                    <div class="list-stats">
                        Total: <?php echo $list['total']; ?> | 
                        Completados: <?php echo $list['completed']; ?> | 
                        Pendientes: <?php echo $list['pending']; ?>
                    </div>
                </div>
                <?php if ($list['with_tmdb_poster'] > 0): ?>
                <div class="tmdb-indicator">
                    🎬 <?php echo $list['with_tmdb_poster']; ?> con poster TMDB
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📖 Cómo Funciona</h2>
            <ol>
                <li><strong>Extracción de Metadatos:</strong> Lee la información TMDB ya presente en las listas M3U</li>
                <li><strong>Verificación de Posters:</strong> Usa las URLs de posters de TMDB para identificar contenido</li>
                <li><strong>Búsqueda por Título:</strong> Para contenido sin poster, busca por título y año</li>
                <li><strong>Actualización Automática:</strong> Guarda TMDB IDs, títulos y metadatos</li>
            </ol>
            
            <h3>💡 Ventajas:</h3>
            <ul>
                <li>✅ <strong>Más rápido:</strong> Usa información ya disponible</li>
                <li>✅ <strong>Más preciso:</strong> Los posters garantizan coincidencia exacta</li>
                <li>✅ <strong>Menos peticiones API:</strong> Reduce carga en TMDB</li>
                <li>✅ <strong>Mejor para listas grandes:</strong> Procesa más elementos por lote</li>
            </ul>
        </div>
    </div>
    
    <script>
        function setBatchSize(size) {
            document.getElementById('batch_size').value = size;
        }
    </script>
</body>
</html>
