<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Reparando tabla ticket_responses</h1>";

try {
    // Verificar estructura actual de ticket_responses
    echo "<h2>🔍 Verificando estructura actual...</h2>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM ticket_responses");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Columnas actuales:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . htmlspecialchars($column['Field']) . "</strong> - " . htmlspecialchars($column['Type']) . "</li>";
    }
    echo "</ul>";
    
    $column_names = array_column($columns, 'Field');
    
    // Verificar qué columna de admin existe
    $has_is_admin = in_array('is_admin', $column_names);
    $has_is_admin_response = in_array('is_admin_response', $column_names);
    
    echo "<h3>Estado de columnas admin:</h3>";
    echo "<p>✅ is_admin: " . ($has_is_admin ? "SÍ existe" : "NO existe") . "</p>";
    echo "<p>✅ is_admin_response: " . ($has_is_admin_response ? "SÍ existe" : "NO existe") . "</p>";
    
    // Corregir la estructura
    if (!$has_is_admin_response && $has_is_admin) {
        echo "<h2>🔄 Agregando columna is_admin_response...</h2>";
        try {
            $pdo->exec("ALTER TABLE ticket_responses ADD COLUMN is_admin_response BOOLEAN DEFAULT FALSE AFTER message");
            
            // Copiar datos de is_admin a is_admin_response
            $pdo->exec("UPDATE ticket_responses SET is_admin_response = is_admin");
            
            $success_messages[] = "✅ Columna is_admin_response agregada y datos copiados";
        } catch (Exception $e) {
            $error_messages[] = "❌ Error agregando is_admin_response: " . $e->getMessage();
        }
    } elseif (!$has_is_admin && !$has_is_admin_response) {
        echo "<h2>🔄 Agregando columna is_admin_response...</h2>";
        try {
            $pdo->exec("ALTER TABLE ticket_responses ADD COLUMN is_admin_response BOOLEAN DEFAULT FALSE AFTER message");
            $success_messages[] = "✅ Columna is_admin_response agregada";
        } catch (Exception $e) {
            $error_messages[] = "❌ Error agregando is_admin_response: " . $e->getMessage();
        }
    } else {
        $success_messages[] = "✅ Estructura de ticket_responses está correcta";
    }
    
    // Insertar respuestas de ejemplo para el ticket 6
    echo "<h2>📝 Insertando respuestas de ejemplo...</h2>";
    try {
        // Verificar si el ticket 6 existe
        $stmt = $pdo->prepare("SELECT id FROM support_tickets WHERE id = 6");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            // Insertar respuestas de ejemplo
            $admin_column = $has_is_admin_response ? 'is_admin_response' : 'is_admin';
            
            $stmt = $pdo->prepare("INSERT IGNORE INTO ticket_responses (id, ticket_id, user_id, message, $admin_column) VALUES (?, ?, ?, ?, ?)");
            
            // Respuesta del usuario
            $stmt->execute([1, 6, 1, 'Hola, tengo problemas con la reproducción de los canales. Se cortan cada pocos minutos y tengo que reiniciar la aplicación.', 0]);
            
            // Respuesta del admin
            $stmt->execute([2, 6, 2, 'Hola, gracias por contactarnos. ¿Podrías decirnos qué dispositivo estás usando y qué velocidad de internet tienes?', 1]);
            
            // Otra respuesta del usuario
            $stmt->execute([3, 6, 1, 'Uso un Android TV Box y tengo 50 Mbps de internet. El problema ocurre principalmente en canales HD.', 0]);
            
            $success_messages[] = "✅ Respuestas de ejemplo insertadas para ticket #6";
        } else {
            $error_messages[] = "❌ El ticket #6 no existe, no se pueden insertar respuestas";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando respuestas: " . $e->getMessage();
    }
    
    // Verificar estructura final
    echo "<h2>🔍 Verificación final...</h2>";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM ticket_responses");
        $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Estructura final:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: #2a2a2a; color: white;'>";
        echo "<tr style='background: #333;'><th>Campo</th><th>Tipo</th><th>Null</th><th>Default</th></tr>";
        foreach ($final_columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar respuestas
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ticket_responses");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $success_messages[] = "✅ Total de respuestas en la tabla: $count";
        
    } catch (Exception $e) {
        $error_messages[] = "❌ Error en verificación final: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Reparación ticket_responses - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        h3 {
            color: #f59e0b;
            margin-top: 1.5rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        table {
            margin: 1rem 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #555;
        }
        ul {
            background: #0f172a;
            padding: 1rem;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Reparación ticket_responses Completada</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="ticket_detail.php?id=6" class="btn">🎫 Probar Ticket #6</a>
            <a href="tickets_admin.php" class="btn">📋 Ver Todos los Tickets</a>
            <a href="admin2.php" class="btn">🏠 Panel Admin</a>
        </div>
    </div>
</body>
</html>
