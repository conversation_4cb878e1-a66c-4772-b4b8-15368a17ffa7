<?php
// Archivo de debug para diagnosticar errores 500
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico del Sistema RGS</h1>";

// 1. Verificar PHP
echo "<h2>1. Información de PHP</h2>";
echo "Versión de PHP: " . phpversion() . "<br>";
echo "Extensiones requeridas:<br>";
$required_extensions = ['pdo', 'pdo_mysql', 'json', 'session'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? "✅" : "❌";
    echo "$status $ext<br>";
}

// 2. Verificar archivos
echo "<h2>2. Verificación de Archivos</h2>";
$required_files = [
    'config.php',
    'index2.php',
    'user_tickets.php',
    'user_chat.php',
    'user_apps.php',
    'user_help.php',
    'user_channels.php',
    'user_activation.php',
    'admin2.php',
    'admin_login.php'
];

foreach ($required_files as $file) {
    $status = file_exists($file) ? "✅" : "❌";
    echo "$status $file<br>";
}

// 3. Verificar config.php
echo "<h2>3. Verificación de Configuración</h2>";
if (file_exists('config.php')) {
    try {
        require_once 'config.php';
        echo "✅ config.php cargado correctamente<br>";
        
        // Verificar conexión a BD
        if (isset($pdo)) {
            echo "✅ Conexión a base de datos establecida<br>";
            
            // Verificar tablas
            $tables = [
                'users',
                'support_tickets',
                'ticket_responses',
                'chat_sessions',
                'chat_messages',
                'support_apps',
                'help_articles',
                'channel_requests',
                'activation_codes',
                'user_activations'
            ];
            
            echo "<h3>Tablas de Base de Datos:</h3>";
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                    $exists = $stmt->rowCount() > 0;
                    $status = $exists ? "✅" : "❌";
                    echo "$status $table<br>";
                } catch (Exception $e) {
                    echo "❌ $table (Error: " . $e->getMessage() . ")<br>";
                }
            }
            
        } else {
            echo "❌ No se pudo establecer conexión a la base de datos<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error al cargar config.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ config.php no encontrado<br>";
}

// 4. Verificar permisos
echo "<h2>4. Verificación de Permisos</h2>";
$upload_dir = 'uploads';
if (!file_exists($upload_dir)) {
    $created = mkdir($upload_dir, 0755, true);
    echo ($created ? "✅" : "❌") . " Directorio uploads creado<br>";
} else {
    echo "✅ Directorio uploads existe<br>";
}

$writable = is_writable($upload_dir);
echo ($writable ? "✅" : "❌") . " Directorio uploads escribible<br>";

// 5. Test de páginas
echo "<h2>5. Test de Páginas</h2>";
$pages_to_test = [
    'user_tickets.php',
    'user_chat.php',
    'user_apps.php',
    'user_help.php',
    'user_channels.php',
    'user_activation.php'
];

foreach ($pages_to_test as $page) {
    if (file_exists($page)) {
        echo "🔍 Probando $page...<br>";
        
        // Capturar errores
        ob_start();
        $error_occurred = false;
        
        try {
            // Simular inclusión sin ejecutar
            $content = file_get_contents($page);
            if (strpos($content, '<?php') === 0) {
                echo "✅ $page - Sintaxis PHP válida<br>";
            } else {
                echo "⚠️ $page - No es un archivo PHP válido<br>";
            }
        } catch (Exception $e) {
            echo "❌ $page - Error: " . $e->getMessage() . "<br>";
            $error_occurred = true;
        }
        
        ob_end_clean();
    } else {
        echo "❌ $page - Archivo no encontrado<br>";
    }
}

// 6. Soluciones recomendadas
echo "<h2>6. Soluciones Recomendadas</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Para solucionar los errores 500:</strong><br><br>";
echo "1. <a href='quick_setup.php'>🚀 Ejecutar Configuración Rápida</a> - Crea todas las tablas necesarias<br>";
echo "2. <a href='setup.php'>⚙️ Ejecutar Instalador Completo</a> - Configuración paso a paso<br>";
echo "3. Importar manualmente: <code>support_system_tables.sql</code><br>";
echo "4. Verificar permisos del directorio uploads/<br>";
echo "5. Verificar credenciales de base de datos en config.php<br>";
echo "</div>";

// 7. Información del servidor
echo "<h2>7. Información del Servidor</h2>";
echo "Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Documento Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Directorio actual: " . getcwd() . "<br>";
echo "Usuario PHP: " . get_current_user() . "<br>";

// 8. Variables de sesión
echo "<h2>8. Variables de Sesión</h2>";
session_start();
if (empty($_SESSION)) {
    echo "No hay variables de sesión activas<br>";
} else {
    foreach ($_SESSION as $key => $value) {
        echo "$key: " . (is_string($value) ? $value : json_encode($value)) . "<br>";
    }
}

echo "<hr>";
echo "<p><strong>Fecha del diagnóstico:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='index2.php'>🏠 Volver al Inicio</a></p>";
?>
