<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>📁 Configurando Directorio de Subidas</h1>";

try {
    // Crear directorio uploads si no existe
    $upload_base = 'uploads';
    if (!is_dir($upload_base)) {
        if (mkdir($upload_base, 0755, true)) {
            $success_messages[] = "✅ Directorio 'uploads' creado";
        } else {
            $error_messages[] = "❌ No se pudo crear el directorio 'uploads'";
        }
    } else {
        $success_messages[] = "✅ Directorio 'uploads' ya existe";
    }
    
    // Crear subdirectorio apps
    $upload_apps = 'uploads/apps';
    if (!is_dir($upload_apps)) {
        if (mkdir($upload_apps, 0755, true)) {
            $success_messages[] = "✅ Directorio 'uploads/apps' creado";
        } else {
            $error_messages[] = "❌ No se pudo crear el directorio 'uploads/apps'";
        }
    } else {
        $success_messages[] = "✅ Directorio 'uploads/apps' ya existe";
    }
    
    // Verificar permisos de escritura
    if (is_writable($upload_apps)) {
        $success_messages[] = "✅ Directorio 'uploads/apps' tiene permisos de escritura";
    } else {
        $error_messages[] = "❌ Directorio 'uploads/apps' NO tiene permisos de escritura";
        
        // Intentar corregir permisos
        if (chmod($upload_apps, 0755)) {
            $success_messages[] = "✅ Permisos corregidos para 'uploads/apps'";
        } else {
            $error_messages[] = "❌ No se pudieron corregir los permisos";
        }
    }
    
    // Crear archivo .htaccess para seguridad
    $htaccess_content = '# Protección del directorio uploads
# Permitir solo ciertos tipos de archivo
<FilesMatch "\.(apk|ipa|exe|dmg|deb|zip|msi)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Denegar acceso a archivos PHP
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Denegar listado de directorios
Options -Indexes

# Configurar tipos MIME
AddType application/vnd.android.package-archive .apk
AddType application/octet-stream .ipa
AddType application/x-msdownload .exe
AddType application/x-apple-diskimage .dmg
AddType application/x-debian-package .deb
AddType application/zip .zip
AddType application/x-msi .msi';

    $htaccess_file = $upload_apps . '/.htaccess';
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        $success_messages[] = "✅ Archivo .htaccess creado para seguridad";
    } else {
        $error_messages[] = "❌ No se pudo crear el archivo .htaccess";
    }
    
    // Crear archivo index.php para evitar listado
    $index_content = '<?php
// Archivo de protección - No eliminar
header("HTTP/1.0 403 Forbidden");
exit("Acceso denegado");
?>';
    
    $index_file = $upload_apps . '/index.php';
    if (file_put_contents($index_file, $index_content)) {
        $success_messages[] = "✅ Archivo index.php de protección creado";
    } else {
        $error_messages[] = "❌ No se pudo crear el archivo index.php";
    }
    
    // Verificar configuración PHP
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $memory_limit = ini_get('memory_limit');
    
    $success_messages[] = "📊 Configuración PHP:";
    $success_messages[] = "   - upload_max_filesize: $upload_max";
    $success_messages[] = "   - post_max_size: $post_max";
    $success_messages[] = "   - memory_limit: $memory_limit";
    
    // Convertir a bytes para comparar
    function convertToBytes($value) {
        $value = trim($value);
        $last = strtolower($value[strlen($value)-1]);
        $value = (int) $value;
        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }
        return $value;
    }
    
    $upload_bytes = convertToBytes($upload_max);
    $target_bytes = 100 * 1024 * 1024; // 100MB
    
    if ($upload_bytes >= $target_bytes) {
        $success_messages[] = "✅ Límite de subida suficiente para archivos de 100MB";
    } else {
        $error_messages[] = "⚠️ Límite de subida ($upload_max) menor a 100MB recomendado";
    }
    
    // Crear aplicación de prueba si no existe
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps WHERE name = 'App de Prueba'");
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO support_apps (name, description, version, platform, features, download_url, external_url, status, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                'App de Prueba',
                'Aplicación de demostración para probar el sistema de descargas',
                '1.0.0',
                'android',
                'Demo, Prueba, Testing',
                'https://play.google.com/store',
                'https://example.com',
                'active',
                1
            ]);
            $success_messages[] = "✅ Aplicación de prueba creada en la base de datos";
        } else {
            $success_messages[] = "✅ Aplicación de prueba ya existe";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando app de prueba: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📁 Configurar Uploads - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 Configuración de Directorio de Subidas</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Configuración Exitosa</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="apps_admin.php" class="btn">📱 Subir Aplicación</a>
            <a href="index2.php" class="btn">👀 Ver en Index2</a>
            <a href="user_apps.php" class="btn">📋 Lista de Apps</a>
        </div>
    </div>
</body>
</html>
