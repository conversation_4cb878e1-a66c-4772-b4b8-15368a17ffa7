<?php
session_start();

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

// Simular usuario logueado
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Usuario de prueba
    $_SESSION['username'] = 'test_user';
}

$user_id = $_SESSION['user_id'];

echo "<h1>Test de Funcionalidad de Pedidos</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } form { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Función para obtener IP real
    function getRealIP() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    // Procesar pedido si se envía el formulario
    if (isset($_POST['pedido_tmdb_id'])) {
        echo "<h2>Procesando Pedido...</h2>";
        
        $tmdb_id = $_POST['pedido_tmdb_id'];
        $title = $_POST['pedido_title'];
        $media_type = $_POST['pedido_media_type'];
        $year = $_POST['pedido_year'];
        
        echo "<p class='info'>Datos recibidos:</p>";
        echo "<ul>";
        echo "<li><strong>TMDB ID:</strong> $tmdb_id</li>";
        echo "<li><strong>Título:</strong> $title</li>";
        echo "<li><strong>Tipo:</strong> $media_type</li>";
        echo "<li><strong>Año:</strong> $year</li>";
        echo "<li><strong>Usuario ID:</strong> $user_id</li>";
        echo "</ul>";
        
        // Obtener información de geolocalización
        $ip = getRealIP();
        $country = 'Test Country';
        $city = 'Test City';
        
        echo "<p class='info'>Información adicional:</p>";
        echo "<ul>";
        echo "<li><strong>IP:</strong> $ip</li>";
        echo "<li><strong>País:</strong> $country</li>";
        echo "<li><strong>Ciudad:</strong> $city</li>";
        echo "</ul>";
        
        try {
            // Insertar pedido en la base de datos
            $stmt = $pdo->prepare("INSERT INTO orders (user_id, tmdb_id, title, media_type, year, country, city, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([
                $user_id,
                $tmdb_id,
                $title,
                $media_type,
                $year,
                $country,
                $city,
                $ip
            ]);
            
            if ($result) {
                $order_id = $pdo->lastInsertId();
                echo "<p class='success'>✓ Pedido insertado exitosamente con ID: $order_id</p>";
                
                // Verificar que se insertó correctamente
                $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
                $stmt->execute([$order_id]);
                $order = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($order) {
                    echo "<p class='success'>✓ Pedido verificado en la base de datos:</p>";
                    echo "<pre>" . print_r($order, true) . "</pre>";
                } else {
                    echo "<p class='error'>✗ Error: No se pudo verificar el pedido en la base de datos</p>";
                }
                
            } else {
                echo "<p class='error'>✗ Error: No se pudo insertar el pedido</p>";
            }
            
        } catch(PDOException $e) {
            echo "<p class='error'>✗ Error al insertar pedido: " . $e->getMessage() . "</p>";
            echo "<p class='error'>Código de error: " . $e->getCode() . "</p>";
        }
    }
    
    // Mostrar pedidos existentes
    echo "<h2>Pedidos Existentes</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p class='info'>Total de pedidos: " . $count['count'] . "</p>";
    
    if ($count['count'] > 0) {
        $stmt = $pdo->query("SELECT o.*, u.username FROM orders o LEFT JOIN users u ON o.user_id = u.id ORDER BY o.created_at DESC LIMIT 10");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr><th>ID</th><th>Usuario</th><th>Título</th><th>Tipo</th><th>Año</th><th>Estado</th><th>Fecha</th></tr>";
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . ($order['username'] ?? 'Usuario #' . $order['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($order['title']) . "</td>";
            echo "<td>" . $order['media_type'] . "</td>";
            echo "<td>" . $order['year'] . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch(PDOException $e) {
    echo "<p class='error'>✗ Error de conexión: " . $e->getMessage() . "</p>";
}
?>

<h2>Formulario de Prueba de Pedido</h2>
<form method="POST">
    <h3>Simular Pedido de Película/Serie</h3>
    
    <label for="pedido_tmdb_id">TMDB ID:</label><br>
    <input type="number" name="pedido_tmdb_id" id="pedido_tmdb_id" value="550" required><br><br>
    
    <label for="pedido_title">Título:</label><br>
    <input type="text" name="pedido_title" id="pedido_title" value="Fight Club" required><br><br>
    
    <label for="pedido_media_type">Tipo de Media:</label><br>
    <select name="pedido_media_type" id="pedido_media_type" required>
        <option value="movie">Película</option>
        <option value="tv">Serie</option>
    </select><br><br>
    
    <label for="pedido_year">Año:</label><br>
    <input type="text" name="pedido_year" id="pedido_year" value="1999" required><br><br>
    
    <button type="submit">Enviar Pedido de Prueba</button>
</form>

<hr>
<p><a href="index.php">← Volver al índice</a> | <a href="test_database.php">Ver test de base de datos</a></p>

<script>
// Simular la función JavaScript del index.php
function testOrderModal() {
    const tmdbId = document.getElementById('pedido_tmdb_id').value;
    const title = document.getElementById('pedido_title').value;
    const mediaType = document.getElementById('pedido_media_type').value;
    const year = document.getElementById('pedido_year').value;
    
    console.log('Test openOrderModal called with:', { tmdbId, title, mediaType, year });
    
    if (confirm('¿Deseas hacer el pedido de "' + title + '"?')) {
        console.log('User confirmed, submitting form...');
        document.querySelector('form').submit();
    } else {
        console.log('User cancelled the order');
    }
}
</script>

<button onclick="testOrderModal()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0;">
    Test JavaScript Function
</button>
