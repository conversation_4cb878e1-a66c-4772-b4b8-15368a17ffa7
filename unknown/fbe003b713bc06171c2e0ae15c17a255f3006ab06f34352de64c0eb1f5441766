<?php
session_start();
require_once 'config.php';

// Verificar si el usuario está logueado como admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_status':
                $ticket_id = (int)$_POST['ticket_id'];
                $new_status = $_POST['status'];
                $stmt = $pdo->prepare("UPDATE support_tickets SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$new_status, $ticket_id]);
                $success_message = "Estado del ticket actualizado correctamente";
                break;
                
            case 'assign_ticket':
                $ticket_id = (int)$_POST['ticket_id'];
                $admin_id = (int)$_POST['admin_id'];
                $stmt = $pdo->prepare("UPDATE support_tickets SET assigned_to = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$admin_id, $ticket_id]);
                $success_message = "Ticket asignado correctamente";
                break;
                
            case 'add_response':
                $ticket_id = (int)$_POST['ticket_id'];
                $message = $_POST['message'];
                $admin_id = $_SESSION['admin_id'] ?? 1;
                
                $stmt = $pdo->prepare("INSERT INTO ticket_responses (ticket_id, user_id, message, is_admin_response) VALUES (?, ?, ?, 1)");
                $stmt->execute([$ticket_id, $admin_id, $message]);
                
                // Actualizar timestamp del ticket
                $stmt = $pdo->prepare("UPDATE support_tickets SET updated_at = NOW() WHERE id = ?");
                $stmt->execute([$ticket_id]);
                
                $success_message = "Respuesta agregada correctamente";
                break;
        }
    }
}

// Obtener filtros
$status_filter = $_GET['status'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';
$category_filter = $_GET['category'] ?? 'all';

// Construir consulta
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "st.status = ?";
    $params[] = $status_filter;
}

if ($priority_filter !== 'all') {
    $where_conditions[] = "st.priority = ?";
    $params[] = $priority_filter;
}

if ($category_filter !== 'all') {
    $where_conditions[] = "st.category = ?";
    $params[] = $category_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Obtener tickets
$stmt = $pdo->prepare("
    SELECT st.*, u.username, 
           (SELECT COUNT(*) FROM ticket_responses tr WHERE tr.ticket_id = st.id) as response_count,
           (SELECT message FROM ticket_responses tr WHERE tr.ticket_id = st.id ORDER BY created_at DESC LIMIT 1) as last_response
    FROM support_tickets st 
    LEFT JOIN users u ON st.user_id = u.id 
    $where_clause
    ORDER BY st.created_at DESC
");
$stmt->execute($params);
$tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas
$stats_stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved,
        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent
    FROM support_tickets
");
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Gestión de Tickets - Admin Soporte</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --support-color: #e91e63;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header {
            background: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--support-color);
            text-decoration: none;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: transparent;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
        }

        .nav-btn.back-btn {
            background: var(--primary-color);
            color: white;
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .filters {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-select {
            padding: 0.75rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.9rem;
        }

        .tickets-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .ticket-item {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .ticket-item:hover {
            background: rgba(255,255,255,0.05);
        }

        .ticket-item:last-child {
            border-bottom: none;
        }

        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .ticket-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .ticket-meta {
            display: flex;
            gap: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-open { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .status-in_progress { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .status-resolved { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .status-closed { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

        .priority-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .priority-low { background: rgba(107, 114, 128, 0.2); color: #6b7280; }
        .priority-medium { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .priority-high { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .priority-urgent { background: rgba(239, 68, 68, 0.2); color: #ef4444; }

        .ticket-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }

        .success-message {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .ticket-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .ticket-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <a href="tickets_admin.php" class="logo">
                <i class="fas fa-ticket-alt"></i>
                <span>Gestión de Tickets</span>
            </a>
            
            <div class="nav-buttons">
                <a href="admin2.php" class="nav-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>Volver</span>
                </a>
                <a href="admin.php" class="nav-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-ticket-alt"></i>
                Gestión de Tickets de Soporte
            </h1>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">Total Tickets</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['open']; ?></div>
                <div class="stat-label">Abiertos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['in_progress']; ?></div>
                <div class="stat-label">En Progreso</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['resolved']; ?></div>
                <div class="stat-label">Resueltos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['urgent']; ?></div>
                <div class="stat-label">Urgentes</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select name="status" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                        <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Abiertos</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>En Progreso</option>
                        <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resueltos</option>
                        <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Cerrados</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Prioridad</label>
                    <select name="priority" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>Urgente</option>
                        <option value="high" <?php echo $priority_filter === 'high' ? 'selected' : ''; ?>>Alta</option>
                        <option value="medium" <?php echo $priority_filter === 'medium' ? 'selected' : ''; ?>>Media</option>
                        <option value="low" <?php echo $priority_filter === 'low' ? 'selected' : ''; ?>>Baja</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Categoría</label>
                    <select name="category" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php echo $category_filter === 'all' ? 'selected' : ''; ?>>Todas</option>
                        <option value="technical" <?php echo $category_filter === 'technical' ? 'selected' : ''; ?>>Técnico</option>
                        <option value="billing" <?php echo $category_filter === 'billing' ? 'selected' : ''; ?>>Facturación</option>
                        <option value="general" <?php echo $category_filter === 'general' ? 'selected' : ''; ?>>General</option>
                        <option value="bug_report" <?php echo $category_filter === 'bug_report' ? 'selected' : ''; ?>>Reporte de Bug</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Lista de Tickets -->
        <div class="tickets-container">
            <?php if (empty($tickets)): ?>
            <div class="ticket-item" style="text-align: center; color: var(--text-secondary);">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No hay tickets que coincidan con los filtros seleccionados</p>
            </div>
            <?php else: ?>
                <?php foreach ($tickets as $ticket): ?>
                <div class="ticket-item">
                    <div class="ticket-header">
                        <div>
                            <div class="ticket-title">
                                #<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['title']); ?>
                            </div>
                            <div class="ticket-meta">
                                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($ticket['username'] ?? 'Usuario desconocido'); ?></span>
                                <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></span>
                                <span><i class="fas fa-comments"></i> <?php echo $ticket['response_count']; ?> respuestas</span>
                            </div>
                        </div>
                        <div class="ticket-actions">
                            <span class="status-badge status-<?php echo $ticket['status']; ?>">
                                <?php
                                $status_labels = [
                                    'open' => 'Abierto',
                                    'in_progress' => 'En Progreso',
                                    'waiting_user' => 'Esperando Usuario',
                                    'resolved' => 'Resuelto',
                                    'closed' => 'Cerrado'
                                ];
                                echo $status_labels[$ticket['status']] ?? $ticket['status'];
                                ?>
                            </span>
                            <span class="priority-badge priority-<?php echo $ticket['priority']; ?>">
                                <?php
                                $priority_labels = [
                                    'low' => 'Baja',
                                    'medium' => 'Media',
                                    'high' => 'Alta',
                                    'urgent' => 'Urgente'
                                ];
                                echo $priority_labels[$ticket['priority']] ?? $ticket['priority'];
                                ?>
                            </span>
                        </div>
                    </div>

                    <div style="margin-bottom: 1rem; color: var(--text-secondary);">
                        <strong>Categoría:</strong> <?php echo ucfirst($ticket['category']); ?>
                    </div>

                    <div style="margin-bottom: 1rem; color: var(--text-secondary);">
                        <?php echo nl2br(htmlspecialchars(substr($ticket['description'], 0, 200))); ?>
                        <?php if (strlen($ticket['description']) > 200): ?>...<?php endif; ?>
                    </div>

                    <?php if ($ticket['last_response']): ?>
                    <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
                        <strong>Última respuesta:</strong><br>
                        <?php echo nl2br(htmlspecialchars(substr($ticket['last_response'], 0, 150))); ?>
                        <?php if (strlen($ticket['last_response']) > 150): ?>...<?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="ticket-actions">
                        <a href="ticket_detail.php?id=<?php echo $ticket['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            Ver Detalles
                        </a>

                        <?php if ($ticket['status'] !== 'resolved' && $ticket['status'] !== 'closed'): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                            <input type="hidden" name="status" value="in_progress">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-play"></i>
                                En Progreso
                            </button>
                        </form>

                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                            <input type="hidden" name="status" value="resolved">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i>
                                Resolver
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <script>
        // Auto-refresh cada 30 segundos
        setInterval(function() {
            if (document.hidden) return; // No actualizar si la pestaña no está visible

            // Solo actualizar si no hay formularios siendo editados
            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                location.reload();
            }
        }, 30000);

        // Confirmación para acciones críticas
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const action = this.querySelector('input[name="action"]')?.value;
                if (action === 'update_status') {
                    const status = this.querySelector('input[name="status"]')?.value;
                    if (status === 'resolved' || status === 'closed') {
                        if (!confirm('¿Estás seguro de que quieres cambiar el estado de este ticket?')) {
                            e.preventDefault();
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
