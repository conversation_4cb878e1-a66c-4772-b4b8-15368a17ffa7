<?php
// Script para probar el tracking de notificaciones del cliente
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>🧪 Test de Tracking de Notificaciones</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } pre { background: #333; padding: 10px; border-radius: 5px; } table { width: 100%; border-collapse: collapse; margin: 1rem 0; } th, td { border: 1px solid #444; padding: 8px; text-align: left; } th { background: #333; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar si las columnas de tracking existen
    echo "<h2>🔍 Verificar Columnas de Tracking:</h2>";
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_client_notif_seen = false;
    $has_client_notif_seen_at = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'client_notif_seen') {
            $has_client_notif_seen = true;
            echo "<p class='success'>✅ Columna 'client_notif_seen' existe</p>";
        }
        if ($column['Field'] === 'client_notif_seen_at') {
            $has_client_notif_seen_at = true;
            echo "<p class='success'>✅ Columna 'client_notif_seen_at' existe</p>";
        }
    }
    
    if (!$has_client_notif_seen || !$has_client_notif_seen_at) {
        echo "<p class='error'>❌ Faltan columnas de tracking. Ejecuta 'add_notification_tracking.php' primero.</p>";
        echo "<p><a href='add_notification_tracking.php' style='color: #ffc107;'>🔧 Agregar columnas de tracking</a></p>";
        exit;
    }
    
    // Mostrar estado actual de los pedidos
    echo "<h2>📊 Estado Actual de Pedidos (con tracking):</h2>";
    $stmt = $pdo->query("
        SELECT o.id, o.title, o.status, o.user_id, u.username,
               o.notif_seen as admin_seen,
               o.status_notif_seen as old_client_seen,
               o.client_notif_seen as new_client_seen,
               o.client_notif_seen_at,
               o.created_at, o.updated_at
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.updated_at DESC
        LIMIT 10
    ");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orders)) {
        echo "<table>";
        echo "<tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Título</th>
                <th>Estado</th>
                <th>Admin Visto</th>
                <th>Cliente Visto (Viejo)</th>
                <th>Cliente Visto (Nuevo)</th>
                <th>Visto En</th>
                <th>Actualizado</th>
              </tr>";
        
        foreach ($orders as $order) {
            $admin_seen = $order['admin_seen'] ? '✅' : '❌';
            $old_client_seen = $order['old_client_seen'] ? '✅' : '❌';
            $new_client_seen = $order['new_client_seen'] ? '✅' : '❌';
            $seen_at = $order['client_notif_seen_at'] ? date('d/m H:i', strtotime($order['client_notif_seen_at'])) : '-';
            
            echo "<tr>
                    <td>#{$order['id']}</td>
                    <td>{$order['username']}</td>
                    <td>" . htmlspecialchars(substr($order['title'], 0, 30)) . "...</td>
                    <td>{$order['status']}</td>
                    <td>$admin_seen</td>
                    <td>$old_client_seen</td>
                    <td>$new_client_seen</td>
                    <td>$seen_at</td>
                    <td>" . date('d/m H:i', strtotime($order['updated_at'])) . "</td>
                  </tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>No hay pedidos para mostrar</p>";
    }
    
    // Estadísticas de tracking
    echo "<h2>📈 Estadísticas de Tracking:</h2>";
    
    $stats = [];
    
    // Total de pedidos
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $stats['total'] = $stmt->fetchColumn();
    
    // Pedidos vistos por admin
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE notif_seen = 1");
    $stats['admin_seen'] = $stmt->fetchColumn();
    
    // Pedidos vistos por cliente (nuevo sistema)
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE client_notif_seen = 1");
    $stats['client_seen'] = $stmt->fetchColumn();
    
    // Pedidos no vistos por cliente
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE client_notif_seen = 0 OR client_notif_seen IS NULL");
    $stats['client_not_seen'] = $stmt->fetchColumn();
    
    echo "<table>";
    echo "<tr><th>Métrica</th><th>Cantidad</th><th>Porcentaje</th></tr>";
    echo "<tr><td>Total de pedidos</td><td>{$stats['total']}</td><td>100%</td></tr>";
    echo "<tr><td>Vistos por admin</td><td>{$stats['admin_seen']}</td><td>" . round(($stats['admin_seen']/$stats['total'])*100, 1) . "%</td></tr>";
    echo "<tr><td>Vistos por cliente</td><td>{$stats['client_seen']}</td><td>" . round(($stats['client_seen']/$stats['total'])*100, 1) . "%</td></tr>";
    echo "<tr><td>No vistos por cliente</td><td>{$stats['client_not_seen']}</td><td>" . round(($stats['client_not_seen']/$stats['total'])*100, 1) . "%</td></tr>";
    echo "</table>";
    
    // Acciones de prueba
    echo "<h2>🧪 Acciones de Prueba:</h2>";
    
    if (isset($_POST['simulate_client_view'])) {
        $user_id = $_POST['test_user_id'];
        if ($user_id) {
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET client_notif_seen = 1, client_notif_seen_at = NOW() 
                WHERE user_id = ? AND client_notif_seen = 0
            ");
            $affected = $stmt->execute([$user_id]);
            echo "<p class='success'>✅ Simulado: Cliente ID $user_id vio sus notificaciones</p>";
        }
    }
    
    if (isset($_POST['reset_tracking'])) {
        $pdo->exec("UPDATE orders SET client_notif_seen = 0, client_notif_seen_at = NULL");
        echo "<p class='warning'>⚠️ Reset: Todas las notificaciones marcadas como no vistas</p>";
    }
    
    // Obtener usuarios para pruebas
    $stmt = $pdo->query("SELECT DISTINCT user_id, username FROM orders o LEFT JOIN users u ON o.user_id = u.id ORDER BY username");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<form method='POST' style='margin: 1rem 0;'>";
    echo "<label>Simular que un cliente ve sus notificaciones:</label><br>";
    echo "<select name='test_user_id' required>";
    echo "<option value=''>Seleccionar usuario...</option>";
    foreach ($users as $user) {
        echo "<option value='{$user['user_id']}'>ID {$user['user_id']} - {$user['username']}</option>";
    }
    echo "</select>";
    echo "<button type='submit' name='simulate_client_view' style='margin-left: 10px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px;'>👁️ Simular Vista</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 1rem 0;'>";
    echo "<button type='submit' name='reset_tracking' onclick='return confirm(\"¿Resetear todo el tracking?\")' style='padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px;'>🔄 Reset Tracking</button>";
    echo "</form>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='admin.php'>📊 Panel Admin</a> | <a href='add_notification_tracking.php'>🔧 Agregar tracking</a> | <a href='index.php'>🏠 Inicio</a></p>";
?>
