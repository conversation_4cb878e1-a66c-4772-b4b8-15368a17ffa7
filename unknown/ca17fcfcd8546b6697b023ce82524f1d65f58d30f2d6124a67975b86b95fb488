<?php
// Script para probar acceso a listas M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🧪 Test de Acceso M3U</h1>";
echo "<style>body{font-family:Arial;margin:20px;background:#141414;color:white;} .success{color:#28a745;} .error{color:#dc3545;} .info{color:#17a2b8;} .test{background:#222;padding:15px;margin:10px 0;border-radius:8px;}</style>";

// Función para probar URL
function testM3uUrl($url, $username = null, $password = null) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    if ($username && $password) {
        $auth = base64_encode("$username:$password");
        $context = stream_context_create([
            'http' => [
                'header' => "Authorization: Basic $auth\r\n",
                'timeout' => 10,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
    }
    
    $start_time = microtime(true);
    $content = @file_get_contents($url, false, $context);
    $end_time = microtime(true);
    
    $result = [
        'success' => $content !== false,
        'size' => $content ? strlen($content) : 0,
        'time' => round(($end_time - $start_time) * 1000, 2),
        'content_preview' => $content ? substr($content, 0, 200) : null,
        'error' => $content === false ? error_get_last() : null,
        'is_m3u' => $content ? (strpos($content, '#EXTM3U') !== false || strpos($content, '#EXTINF') !== false) : false,
        'line_count' => $content ? substr_count($content, "\n") : 0,
        'extinf_count' => $content ? substr_count($content, '#EXTINF') : 0
    ];
    
    return $result;
}

// Obtener todas las listas
$stmt = $pdo->query("SELECT * FROM m3u_lists ORDER BY name");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p class='info'>📊 Total de listas configuradas: " . count($lists) . "</p>";

if (isset($_POST['test_list'])) {
    $list_id = $_POST['list_id'];
    
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$list_id]);
    $list = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($list) {
        echo "<div class='test'>";
        echo "<h2>🧪 Probando: " . htmlspecialchars($list['name']) . "</h2>";
        echo "<p><strong>URL:</strong> " . htmlspecialchars($list['url']) . "</p>";
        
        $result = testM3uUrl($list['url'], $list['username'], $list['password']);
        
        if ($result['success']) {
            echo "<p class='success'>✅ Acceso exitoso</p>";
            echo "<p><strong>Tamaño:</strong> " . number_format($result['size']) . " bytes</p>";
            echo "<p><strong>Tiempo:</strong> " . $result['time'] . " ms</p>";
            echo "<p><strong>Líneas:</strong> " . number_format($result['line_count']) . "</p>";
            echo "<p><strong>Elementos EXTINF:</strong> " . number_format($result['extinf_count']) . "</p>";
            echo "<p><strong>Es M3U válido:</strong> " . ($result['is_m3u'] ? '✅ Sí' : '❌ No') . "</p>";
            
            if ($result['content_preview']) {
                echo "<p><strong>Vista previa:</strong></p>";
                echo "<pre style='background:#1a1a1a;padding:10px;border-radius:5px;font-size:12px;overflow-x:auto;'>" . htmlspecialchars($result['content_preview']) . "...</pre>";
            }
        } else {
            echo "<p class='error'>❌ Error de acceso</p>";
            if ($result['error']) {
                echo "<p class='error'>Error: " . htmlspecialchars($result['error']['message']) . "</p>";
            }
        }
        echo "</div>";
    }
}

echo "<div class='test'>";
echo "<h2>📋 Listas Disponibles para Probar</h2>";

if (empty($lists)) {
    echo "<p>No hay listas configuradas.</p>";
    echo "<p><a href='m3u_manager.php' style='color:#46d347;'>➕ Agregar Lista</a></p>";
} else {
    foreach ($lists as $list) {
        echo "<div style='background:#1a1a1a;padding:10px;margin:10px 0;border-radius:5px;'>";
        echo "<h3>" . htmlspecialchars($list['name']) . "</h3>";
        echo "<p><strong>URL:</strong> " . htmlspecialchars(substr($list['url'], 0, 80)) . "...</p>";
        echo "<p><strong>Tipo:</strong> " . ($list['list_type'] ?? 'direct_m3u') . "</p>";
        echo "<p><strong>Usuario:</strong> " . ($list['username'] ? 'Configurado' : 'No') . "</p>";
        echo "<p><strong>Última actualización:</strong> " . ($list['last_updated'] ? date('d/m/Y H:i', strtotime($list['last_updated'])) : 'Nunca') . "</p>";
        
        echo "<form method='POST' style='display:inline;'>";
        echo "<input type='hidden' name='list_id' value='" . $list['id'] . "'>";
        echo "<button type='submit' name='test_list' style='background:#46d347;color:#1a1a1a;border:none;padding:8px 15px;border-radius:5px;cursor:pointer;'>🧪 Probar Acceso</button>";
        echo "</form>";
        
        echo "<a href='m3u_analyzer_debug.php?list_id=" . $list['id'] . "' style='background:#17a2b8;color:white;text-decoration:none;padding:8px 15px;border-radius:5px;margin-left:10px;display:inline-block;'>🔍 Analizar</a>";
        echo "</div>";
    }
}
echo "</div>";

echo "<div class='test'>";
echo "<h2>🛠️ Herramientas</h2>";
echo "<p><a href='m3u_manager.php' style='color:#46d347;'>📡 Gestor M3U</a></p>";
echo "<p><a href='m3u_analyzer_debug.php' style='color:#46d347;'>🔍 Analizador Debug</a></p>";
echo "<p><a href='admin.php' style='color:#46d347;'>🏠 Panel Admin</a></p>";
echo "</div>";

echo "<div class='test'>";
echo "<h2>💡 Consejos de Troubleshooting</h2>";
echo "<ul>";
echo "<li><strong>Error de timeout:</strong> La URL puede estar lenta o inaccesible</li>";
echo "<li><strong>Error 401/403:</strong> Credenciales incorrectas</li>";
echo "<li><strong>No es M3U válido:</strong> La URL no devuelve un archivo M3U</li>";
echo "<li><strong>Tamaño 0:</strong> El archivo está vacío o la URL es incorrecta</li>";
echo "<li><strong>Para Xtream Codes:</strong> Asegúrate de que la URL del servidor sea correcta</li>";
echo "</ul>";
echo "</div>";
?>
