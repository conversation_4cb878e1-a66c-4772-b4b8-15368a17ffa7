<?php
// Script simple para probar el acceso al admin
session_start();

echo "<h1>Test de Acceso Admin</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";

// Verificar sesión
echo "<h2>1. Estado de la Sesión:</h2>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    echo "<p class='success'>✅ Sesión de admin activa</p>";
    echo "<p class='info'>Usuario admin: " . ($_SESSION['admin_username'] ?? 'N/A') . "</p>";
} else {
    echo "<p class='error'>❌ No hay sesión de admin activa</p>";
    echo "<p class='info'>Necesitas hacer login como admin primero</p>";
}

// Verificar conexión a BD
echo "<h2>2. Conexión a Base de Datos:</h2>";
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Verificar tabla orders
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
    $count = $stmt->fetchColumn();
    echo "<p class='info'>📊 Total de pedidos: $count</p>";
    
    // Verificar tabla users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();
    echo "<p class='info'>👥 Total de usuarios: $count</p>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error de BD: " . $e->getMessage() . "</p>";
}

// Verificar estructura de tabla users
echo "<h2>3. Estructura de Tabla Users:</h2>";
try {
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p class='info'>Columnas existentes:</p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
    
    // Verificar si existen las nuevas columnas
    $new_columns = ['is_infest84', 'is_rogsmediatv', 'is_saul'];
    echo "<p class='info'>Estado de nuevas columnas:</p>";
    echo "<ul>";
    foreach ($new_columns as $col) {
        $exists = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $col) {
                $exists = true;
                break;
            }
        }
        echo "<li>$col: " . ($exists ? "✅ Existe" : "❌ No existe") . "</li>";
    }
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error al verificar estructura: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Enlaces de Prueba:</h2>";
echo "<ul>";
echo "<li><a href='admin_login.php'>🔐 Login de Admin</a></li>";
echo "<li><a href='admin.php'>📊 Panel de Admin</a> (requiere login)</li>";
echo "<li><a href='index.php'>🏠 Página Principal</a></li>";
echo "<li><a href='check_logs.php'>📋 Verificar Logs</a></li>";
echo "</ul>";

echo "<h2>5. Información del Servidor:</h2>";
echo "<p class='info'>PHP Version: " . phpversion() . "</p>";
echo "<p class='info'>Servidor: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido') . "</p>";
echo "<p class='info'>Fecha/Hora: " . date('Y-m-d H:i:s') . "</p>";

?>
