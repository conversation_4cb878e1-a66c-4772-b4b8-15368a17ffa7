<?php
// Debug para verificar datos de pedidos en admin
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener todos los pedidos para debug
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    ORDER BY o.created_at DESC
    LIMIT 10
");
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Admin Orders</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .order-card {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 1rem;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #404040;
        }
        
        .order-id {
            color: #46d347;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .order-status {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .order-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .detail-label {
            color: #888;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .detail-value {
            color: white;
            font-size: 1rem;
        }
        
        .url-test {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #46d347;
            margin-top: 1rem;
        }
        
        .url-test h4 {
            color: #46d347;
            margin-bottom: 0.5rem;
        }
        
        .url-test a {
            color: #3b82f6;
            text-decoration: none;
            word-break: break-all;
        }
        
        .url-test a:hover {
            text-decoration: underline;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .debug-info {
            background: #0f1419;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #f59e0b;
            margin-top: 1rem;
        }
        
        .debug-info h4 {
            color: #f59e0b;
            margin-bottom: 0.5rem;
        }
        
        .debug-info pre {
            color: #cbd5e1;
            font-size: 0.9rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🔍 Debug: Datos de Pedidos</h1>
            <p>Verificación de datos para enlaces de coincidencias</p>
        </div>
        
        <?php if (empty($orders)): ?>
        <div class="order-card">
            <p style="text-align: center; color: #888;">No hay pedidos para mostrar</p>
        </div>
        <?php else: ?>
        
        <?php foreach ($orders as $order): ?>
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">Pedido #<?php echo $order['id']; ?></div>
                <div class="order-status"><?php echo htmlspecialchars($order['status']); ?></div>
            </div>
            
            <div class="order-details">
                <div class="detail-item">
                    <div class="detail-label">Usuario:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($order['username'] ?? 'Usuario #' . $order['user_id']); ?></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Título:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($order['title']); ?></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Tipo:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($order['media_type'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Año:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($order['year'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">TMDB ID:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($order['tmdb_id'] ?? 'N/A'); ?></div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Fecha:</div>
                    <div class="detail-value"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></div>
                </div>
            </div>
            
            <div class="url-test">
                <h4>🔗 URL de Coincidencias Generada:</h4>
                <?php 
                $search_url = 'm3u_search_tmdb.php?q=' . urlencode($order['title']) . '&auto_search=1';
                ?>
                <a href="<?php echo $search_url; ?>" target="_blank"><?php echo htmlspecialchars($search_url); ?></a>
                
                <div style="margin-top: 1rem;">
                    <strong>Parámetros decodificados:</strong><br>
                    <code>q = "<?php echo htmlspecialchars($order['title']); ?>"</code><br>
                    <code>auto_search = 1</code>
                </div>
            </div>
            
            <div class="debug-info">
                <h4>📋 Datos Completos del Pedido:</h4>
                <pre><?php echo htmlspecialchars(json_encode($order, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            </div>
        </div>
        <?php endforeach; ?>
        
        <?php endif; ?>
        
        <div style="background: #2d2d2d; padding: 2rem; border-radius: 8px; border: 1px solid #404040; margin-top: 2rem;">
            <h3 style="color: #46d347; margin-bottom: 1rem;">🎯 Instrucciones de Prueba:</h3>
            <ol style="color: #cbd5e1; line-height: 1.6;">
                <li><strong>Verifica el título:</strong> Asegúrate de que el título del pedido sea correcto</li>
                <li><strong>Prueba la URL:</strong> Haz clic en el enlace generado para verificar que funciona</li>
                <li><strong>Revisa la codificación:</strong> Verifica que no hay caracteres especiales problemáticos</li>
                <li><strong>Compara con admin:</strong> Verifica que el enlace en admin.php sea idéntico</li>
            </ol>
            
            <div style="margin-top: 1.5rem; padding: 1rem; background: #1a1a1a; border-radius: 4px;">
                <strong style="color: #f59e0b;">⚠️ Si el problema persiste:</strong><br>
                <span style="color: #cbd5e1;">
                    1. Verifica que el pedido tenga un título válido en la base de datos<br>
                    2. Comprueba que no hay caracteres especiales que causen problemas<br>
                    3. Asegúrate de que la codificación UTF-8 esté funcionando correctamente
                </span>
            </div>
        </div>
    </div>
</body>
</html>
