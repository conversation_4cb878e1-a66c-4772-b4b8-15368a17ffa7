<?php
// Diagnóstico de listas M3U y contenido
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🔍 Diagnóstico de Listas M3U</h1>";
echo "<style>
body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
table { border-collapse: collapse; width: 100%; margin: 20px 0; }
th, td { border: 1px solid #444; padding: 10px; text-align: left; }
th { background: #333; }
.active { color: #10b981; }
.inactive { color: #ef4444; }
.warning { color: #f59e0b; }
</style>";

// 1. Información de listas M3U
echo "<h2>📋 Listas M3U Registradas</h2>";
$stmt = $pdo->query("
    SELECT 
        id, name, url, list_type, is_active, 
        total_items, last_updated, last_download,
        username, password, server_url
    FROM m3u_lists 
    ORDER BY is_active DESC, name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table>";
echo "<tr><th>ID</th><th>Nombre</th><th>Tipo</th><th>Estado</th><th>Items</th><th>Última Act.</th><th>URL</th></tr>";
foreach ($lists as $list) {
    $status_class = $list['is_active'] ? 'active' : 'inactive';
    $status_text = $list['is_active'] ? '✅ Activa' : '❌ Inactiva';
    
    echo "<tr>";
    echo "<td>{$list['id']}</td>";
    echo "<td>{$list['name']}</td>";
    echo "<td>{$list['list_type']}</td>";
    echo "<td class='$status_class'>$status_text</td>";
    echo "<td>" . ($list['total_items'] ?: '0') . "</td>";
    echo "<td>" . ($list['last_updated'] ?: 'Nunca') . "</td>";
    echo "<td style='max-width: 300px; overflow: hidden; text-overflow: ellipsis;'>" . substr($list['url'], 0, 50) . "...</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Estadísticas de contenido
echo "<h2>📊 Estadísticas de Contenido</h2>";

// Total de contenido
$stmt = $pdo->query("SELECT COUNT(*) FROM m3u_content");
$total_content = $stmt->fetchColumn();

// Contenido en listas activas
$stmt = $pdo->query("
    SELECT COUNT(*) 
    FROM m3u_content c 
    LEFT JOIN m3u_lists l ON c.list_id = l.id 
    WHERE l.is_active = 1
");
$active_content = $stmt->fetchColumn();

// Contenido en listas inactivas
$stmt = $pdo->query("
    SELECT COUNT(*) 
    FROM m3u_content c 
    LEFT JOIN m3u_lists l ON c.list_id = l.id 
    WHERE l.is_active = 0 OR l.is_active IS NULL
");
$inactive_content = $stmt->fetchColumn();

echo "<table>";
echo "<tr><th>Métrica</th><th>Cantidad</th><th>Porcentaje</th></tr>";
echo "<tr><td>Total de contenido</td><td>$total_content</td><td>100%</td></tr>";
echo "<tr><td class='active'>Contenido en listas activas</td><td>$active_content</td><td>" . round(($active_content/$total_content)*100, 1) . "%</td></tr>";
echo "<tr><td class='inactive'>Contenido en listas inactivas</td><td>$inactive_content</td><td>" . round(($inactive_content/$total_content)*100, 1) . "%</td></tr>";
echo "</table>";

// 3. Contenido por lista
echo "<h2>📺 Contenido por Lista</h2>";
$stmt = $pdo->query("
    SELECT 
        l.id, l.name, l.is_active,
        COUNT(c.id) as content_count,
        COUNT(CASE WHEN c.media_type = 'movie' THEN 1 END) as movies,
        COUNT(CASE WHEN c.media_type = 'tv' THEN 1 END) as series,
        COUNT(CASE WHEN c.media_type IS NULL OR c.media_type = '' THEN 1 END) as unknown
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    GROUP BY l.id, l.name, l.is_active
    ORDER BY content_count DESC
");
$content_by_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table>";
echo "<tr><th>Lista</th><th>Estado</th><th>Total</th><th>Películas</th><th>Series</th><th>Desconocido</th></tr>";
foreach ($content_by_list as $list) {
    $status_class = $list['is_active'] ? 'active' : 'inactive';
    $status_text = $list['is_active'] ? '✅' : '❌';
    
    echo "<tr>";
    echo "<td>{$list['name']}</td>";
    echo "<td class='$status_class'>$status_text</td>";
    echo "<td>{$list['content_count']}</td>";
    echo "<td>{$list['movies']}</td>";
    echo "<td>{$list['series']}</td>";
    echo "<td>{$list['unknown']}</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Problemas detectados
echo "<h2>⚠️ Problemas Detectados</h2>";
$problems = [];

if ($inactive_content > 0) {
    $problems[] = "Hay $inactive_content elementos de contenido en listas inactivas que no se muestran en las búsquedas";
}

$stmt = $pdo->query("SELECT COUNT(*) FROM m3u_lists WHERE is_active = 0");
$inactive_lists = $stmt->fetchColumn();
if ($inactive_lists > 0) {
    $problems[] = "Hay $inactive_lists listas marcadas como inactivas";
}

$stmt = $pdo->query("SELECT COUNT(*) FROM m3u_content WHERE list_id IS NULL");
$orphan_content = $stmt->fetchColumn();
if ($orphan_content > 0) {
    $problems[] = "Hay $orphan_content elementos de contenido sin lista asociada";
}

if (empty($problems)) {
    echo "<p class='active'>✅ No se detectaron problemas</p>";
} else {
    echo "<ul>";
    foreach ($problems as $problem) {
        echo "<li class='warning'>⚠️ $problem</li>";
    }
    echo "</ul>";
}

// 5. Acciones recomendadas
echo "<h2>🔧 Acciones Recomendadas</h2>";
echo "<ul>";
if ($inactive_lists > 0) {
    echo "<li><a href='m3u_manager.php' style='color: #10b981;'>Activar listas inactivas</a> para mostrar más contenido</li>";
}
echo "<li><a href='m3u_search_tmdb.php' style='color: #10b981;'>Probar búsquedas</a> para verificar que se muestren todos los resultados</li>";
echo "<li><a href='m3u_content_viewer.php' style='color: #10b981;'>Ver todo el contenido</a> disponible</li>";
echo "</ul>";

echo "<br><br>";
echo "<a href='admin.php' style='color: #10b981;'>← Volver al admin</a>";
?>
