<?php
// Buscador M3U con integración TMDB
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Incluir configuración TMDB
require_once 'tmdb_config.php';

// Debug: Verificar que la configuración se cargó correctamente
error_log("TMDB Config loaded - Language defined: " . (defined('TMDB_LANGUAGE') ? 'YES' : 'NO'));
if (defined('TMDB_LANGUAGE')) {
    error_log("TMDB_LANGUAGE value: " . TMDB_LANGUAGE);
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para limpiar título para comparación
function cleanTitle($title) {
    $clean = strtolower($title);
    $clean = preg_replace('/[^\w\s]/', ' ', $clean);
    $clean = preg_replace('/\s+/', ' ', $clean);
    return trim($clean);
}

// Función para generar archivo M3U de serie por lista específica
function generateSeriesM3UByList($pdo, $series_title, $list_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c
            LEFT JOIN m3u_lists l ON c.list_id = l.id
            WHERE c.list_id = ?
            AND l.is_active = 1
            AND (c.title LIKE ? OR c.title LIKE ?)
            ORDER BY c.title
        ");

        $search_pattern1 = "%$series_title%";
        $search_pattern2 = "%" . preg_replace('/s\d+e\d+.*$/i', '', $series_title) . "%";

        $stmt->execute([$list_id, $search_pattern1, $search_pattern2]);
        $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($episodes)) {
            return null;
        }

        // Generar contenido M3U
        $list_name = $episodes[0]['list_name'] ?? 'Lista';
        $m3u_content = "#EXTM3U\n";
        $m3u_content .= "#EXTINF:-1,Serie: " . $series_title . "\n";
        $m3u_content .= "# Generado por RGS TOOL - " . date('Y-m-d H:i:s') . "\n";
        $m3u_content .= "# Lista origen: " . $list_name . "\n";
        $m3u_content .= "# Total de episodios: " . count($episodes) . "\n\n";

        foreach ($episodes as $episode) {
            $m3u_content .= "#EXTINF:-1," . $episode['title'] . "\n";
            $m3u_content .= $episode['url'] . "\n\n";
        }

        return [
            'content' => $m3u_content,
            'filename' => preg_replace('/[^a-zA-Z0-9_-]/', '_', $series_title) . '_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $list_name) . '.m3u',
            'episodes_count' => count($episodes),
            'list_name' => $list_name
        ];

    } catch (Exception $e) {
        return null;
    }
}

// Procesar descarga de serie por lista
if (isset($_GET['download_series']) && isset($_GET['series_title']) && isset($_GET['list_id'])) {
    $series_title = $_GET['series_title'];
    $list_id = (int)$_GET['list_id'];

    if (!empty($series_title) && $list_id > 0) {
        $series_data = generateSeriesM3UByList($pdo, $series_title, $list_id);

        if ($series_data) {
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $series_data['filename'] . '"');
            header('Content-Length: ' . strlen($series_data['content']));
            echo $series_data['content'];
            exit;
        }
    }

    $error_message = "No se encontraron episodios para la serie: $series_title en la lista especificada";
}

// Función para encontrar el mejor match TMDB
function findBestTMDBMatch($m3u_title) {
    if (!isTMDBConfigured()) {
        error_log("TMDB not configured!");
        return null;
    }

    // Limpiar título para búsqueda
    $search_title = preg_replace('/s\d+e\d+.*$/i', '', $m3u_title);
    $search_title = preg_replace('/\(\d{4}\)/', '', $search_title);
    $search_title = trim($search_title);

    // Debug: Verificar idioma configurado
    error_log("=== TMDB DEBUG START ===");
    error_log("TMDB_LANGUAGE constant: " . (defined('TMDB_LANGUAGE') ? TMDB_LANGUAGE : 'NOT_DEFINED'));
    error_log("Search query: " . $search_title);

    // Hacer petición manual para debug
    $params = [
        'api_key' => TMDB_API_KEY,
        'language' => TMDB_LANGUAGE,
        'query' => $search_title
    ];

    $debug_url = TMDB_BASE_URL . "/search/multi?" . http_build_query($params);
    error_log("Full URL: " . $debug_url);

    $tmdb_data = searchTMDBContent($search_title);

    // Debug: Verificar respuesta de TMDB
    error_log("TMDB Response: " . ($tmdb_data ? "SUCCESS" : "FAILED"));
    if ($tmdb_data) {
        error_log("TMDB Results count: " . (isset($tmdb_data['results']) ? count($tmdb_data['results']) : 0));
        if (isset($tmdb_data['results'][0])) {
            $first_result = $tmdb_data['results'][0];
            error_log("First result title: " . ($first_result['title'] ?? $first_result['name'] ?? 'NO_TITLE'));
            error_log("First result overview: " . substr($first_result['overview'] ?? 'NO_OVERVIEW', 0, 100));
        }
    } else {
        error_log("TMDB search returned null or empty");
    }
    error_log("=== TMDB DEBUG END ===");

    if (!$tmdb_data || empty($tmdb_data['results'])) {
        return null;
    }

    $clean_m3u = cleanTitle($search_title);
    $best_match = null;
    $best_score = 0;

    foreach ($tmdb_data['results'] as $result) {
        $tmdb_title = $result['title'] ?? $result['name'] ?? '';
        $clean_tmdb = cleanTitle($tmdb_title);

        // Calcular similitud
        similar_text($clean_m3u, $clean_tmdb, $score);

        if ($score > $best_score && $score > 70) {
            $best_score = $score;
            $best_match = $result;
        }
    }

    return $best_match;
}

$search_query = $_GET['q'] ?? $_GET['search'] ?? '';
$auto_search = isset($_GET['auto_search']) && $_GET['auto_search'] == '1';
$results = [];
$total_content = 0;
$error_message = '';

// Debug para verificar parámetros recibidos
error_log("M3U Search TMDB - Parámetros recibidos:");
error_log("search_query: " . $search_query);
error_log("auto_search: " . ($auto_search ? 'true' : 'false'));
error_log("GET params: " . print_r($_GET, true));

// Obtener estadísticas generales
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM m3u_content c LEFT JOIN m3u_lists l ON c.list_id = l.id WHERE l.is_active = 1");
    $total_content = $stmt->fetchColumn();
} catch (Exception $e) {
    $total_content = 0;
}

// Realizar búsqueda si hay query
if ($search_query && strlen($search_query) >= 2) {
    try {
        // Detectar si es búsqueda por TMDB ID
        if (is_numeric($search_query)) {
            $tmdb_id = (int)$search_query;
            $stmt = $pdo->prepare("
                SELECT c.*, l.name as list_name, l.folder_name
                FROM m3u_content c
                LEFT JOIN m3u_lists l ON c.list_id = l.id
                WHERE l.is_active = 1 AND c.tmdb_id = ?
                ORDER BY c.title
                LIMIT 100
            ");
            $stmt->execute([$tmdb_id]);
            $raw_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // Búsqueda inteligente con múltiples términos
            $search_terms = [$search_query];

            // Si viene del admin, intentar obtener información TMDB adicional
            if ($auto_search) {
                // Buscar en orders si hay información TMDB adicional
                $order_stmt = $pdo->prepare("
                    SELECT tmdb_title, tmdb_original_title, title, tmdb_id
                    FROM orders
                    WHERE title = ? OR tmdb_title = ? OR tmdb_original_title = ?
                    ORDER BY created_at DESC
                    LIMIT 1
                ");
                $order_stmt->execute([$search_query, $search_query, $search_query]);
                $order_info = $order_stmt->fetch(PDO::FETCH_ASSOC);

                if ($order_info) {
                    // Agregar términos adicionales de búsqueda
                    if ($order_info['tmdb_title'] && $order_info['tmdb_title'] !== $search_query) {
                        $search_terms[] = $order_info['tmdb_title'];
                    }
                    if ($order_info['tmdb_original_title'] && $order_info['tmdb_original_title'] !== $search_query) {
                        $search_terms[] = $order_info['tmdb_original_title'];
                    }

                    // Log para debug
                    error_log("Búsqueda inteligente para '$search_query' - Términos adicionales: " . implode(', ', array_slice($search_terms, 1)));

                    // Si tenemos TMDB ID, buscar por ID primero
                    if ($order_info['tmdb_id']) {
                        $tmdb_stmt = $pdo->prepare("
                            SELECT c.*, l.name as list_name, l.folder_name
                            FROM m3u_content c
                            LEFT JOIN m3u_lists l ON c.list_id = l.id
                            WHERE l.is_active = 1 AND c.tmdb_id = ?
                            ORDER BY c.title
                            LIMIT 100
                        ");
                        $tmdb_stmt->execute([$order_info['tmdb_id']]);
                        $tmdb_results = $tmdb_stmt->fetchAll(PDO::FETCH_ASSOC);

                        if (!empty($tmdb_results)) {
                            $raw_results = $tmdb_results;
                            error_log("Búsqueda por TMDB ID {$order_info['tmdb_id']} encontró " . count($tmdb_results) . " resultados");
                        }
                    }
                }
            }

            // Si no encontramos por TMDB ID, usar búsqueda por etapas con relevancia
            if (empty($raw_results)) {
                $all_results = [];
                $processed_ids = [];

                // Etapa 1: Búsqueda por títulos TMDB (más precisa)
                foreach ($search_terms as $term) {
                    $stmt = $pdo->prepare("
                        SELECT c.*, l.name as list_name, l.folder_name, 'tmdb_title' as match_type, 95 as relevance
                        FROM m3u_content c
                        LEFT JOIN m3u_lists l ON c.list_id = l.id
                        WHERE l.is_active = 1 AND (
                            c.tmdb_title LIKE ? OR
                            c.tmdb_original_title LIKE ?
                        )
                        ORDER BY c.title
                        LIMIT 50
                    ");
                    $search_term = "%$term%";
                    $stmt->execute([$search_term, $search_term]);
                    $tmdb_results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    foreach ($tmdb_results as $result) {
                        if (!in_array($result['id'], $processed_ids)) {
                            $all_results[] = $result;
                            $processed_ids[] = $result['id'];
                        }
                    }
                }

                // Etapa 2: Búsqueda por título original (solo si no encontramos suficientes)
                if (count($all_results) < 10) {
                    foreach ($search_terms as $term) {
                        $processed_ids_str = empty($processed_ids) ? '0' : implode(',', $processed_ids);
                        $stmt = $pdo->prepare("
                            SELECT c.*, l.name as list_name, l.folder_name, 'original_title' as match_type, 80 as relevance
                            FROM m3u_content c
                            LEFT JOIN m3u_lists l ON c.list_id = l.id
                            WHERE l.is_active = 1
                            AND c.title LIKE ?
                            AND c.id NOT IN ($processed_ids_str)
                            ORDER BY
                                CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
                                c.title
                            LIMIT 30
                        ");
                        $search_term = "%$term%";
                        $stmt->execute([$search_term]);
                        $title_results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        foreach ($title_results as $result) {
                            if (!in_array($result['id'], $processed_ids)) {
                                $all_results[] = $result;
                                $processed_ids[] = $result['id'];
                            }
                        }
                    }
                }

                // Etapa 3: Búsqueda por clean_title (solo si aún no encontramos suficientes)
                if (count($all_results) < 5) {
                    foreach ($search_terms as $term) {
                        $processed_ids_str = empty($processed_ids) ? '0' : implode(',', $processed_ids);
                        $stmt = $pdo->prepare("
                            SELECT c.*, l.name as list_name, l.folder_name, 'clean_title' as match_type, 70 as relevance
                            FROM m3u_content c
                            LEFT JOIN m3u_lists l ON c.list_id = l.id
                            WHERE l.is_active = 1
                            AND c.clean_title LIKE ?
                            AND c.id NOT IN ($processed_ids_str)
                            ORDER BY
                                CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
                                c.title
                            LIMIT 20
                        ");
                        $search_term = "%$term%";
                        $stmt->execute([$search_term]);
                        $clean_results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        foreach ($clean_results as $result) {
                            if (!in_array($result['id'], $processed_ids)) {
                                $all_results[] = $result;
                                $processed_ids[] = $result['id'];
                            }
                        }
                    }
                }

                // Ordenar por relevancia y limitar resultados
                usort($all_results, function($a, $b) {
                    $relevance_diff = ($b['relevance'] ?? 0) - ($a['relevance'] ?? 0);
                    if ($relevance_diff !== 0) return $relevance_diff;

                    // Si tienen la misma relevancia, priorizar los que tienen TMDB ID
                    $tmdb_diff = (isset($b['tmdb_id']) && $b['tmdb_id'] ? 1 : 0) - (isset($a['tmdb_id']) && $a['tmdb_id'] ? 1 : 0);
                    if ($tmdb_diff !== 0) return $tmdb_diff;

                    return strcmp($a['title'], $b['title']);
                });

                $raw_results = array_slice($all_results, 0, 100);

                error_log("Búsqueda por etapas con términos: " . implode(', ', $search_terms) . " encontró " . count($raw_results) . " resultados relevantes");
            }
        }
        
        // Funciones auxiliares para detección de series (copiadas de order_matches.php)
        function isSeriesEpisode($title) {
            $patterns = [
                '/s\d+\s*e\d+/i',           // S01E01, S1E1, S01 E01, S1 E1
                '/s\d+\s*ep\d+/i',          // S01EP01, S1 EP1
                '/\d+x\d+/i',               // 1x01, 1x1
                '/season\s*\d+/i',          // Season 1, Season 01
                '/temporada\s*\d+/i',       // Temporada 1
                '/cap[ií]tulo\s*\d+/i',     // Capítulo 1
                '/ep\s*\d+/i',              // Ep 1, Ep01
                '/episode\s*\d+/i',         // Episode 1
                '/\s\d+\s*-\s*\d+/i',       // " 1 - 01", " 01-01"
                '/\[\d+x\d+\]/i',           // [1x01]
                '/\(\d+x\d+\)/i',           // (1x01)
                '/\s\d{1,2}x\d{1,2}\s/i',   // " 1x01 "
                '/\s\d{1,2}\.\d{1,2}\s/i',  // " 1.01 "
                '/\sT\d+\s*E\d+/i',         // T01E01, T1 E1
                '/\sT\d+\s*Cap\d+/i'        // T01Cap01, T1 Cap1
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $title)) {
                    return true;
                }
            }
            return false;
        }

        function extractSeriesBaseName($title) {
            $clean_title = $title;
            $patterns = [
                '/s\d+\s*e\d+.*$/i',           // S01E01, S01 E01 y todo lo que sigue
                '/s\d+\s*ep\d+.*$/i',          // S01EP01, S01 EP01 y todo lo que sigue
                '/\d+x\d+.*$/i',               // 1x01 y todo lo que sigue
                '/season\s*\d+.*$/i',          // Season 1 y todo lo que sigue
                '/temporada\s*\d+.*$/i',       // Temporada 1 y todo lo que sigue
                '/cap[ií]tulo\s*\d+.*$/i',     // Capítulo 1 y todo lo que sigue
                '/ep\s*\d+.*$/i',              // Ep 1 y todo lo que sigue
                '/episode\s*\d+.*$/i',         // Episode 1 y todo lo que sigue
                '/\s*-\s*\d+.*$/i',            // - 01 y todo lo que sigue
                '/\s*\(\d+\).*$/i',            // (01) y todo lo que sigue
                '/\s*\[\d+\].*$/i',            // [01] y todo lo que sigue
                '/\s\d+\s*-\s*\d+.*$/i',       // " 1 - 01" y todo lo que sigue
                '/\[\d+x\d+\].*$/i',           // [1x01] y todo lo que sigue
                '/\(\d+x\d+\).*$/i',           // (1x01) y todo lo que sigue
                '/\s\d{1,2}x\d{1,2}.*$/i',     // " 1x01" y todo lo que sigue
                '/\s\d{1,2}\.\d{1,2}.*$/i',    // " 1.01" y todo lo que sigue
                '/\sT\d+\s*E\d+.*$/i',         // T01E01, T1 E1 y todo lo que sigue
                '/\sT\d+\s*Cap\d+.*$/i',       // T01Cap01, T1 Cap1 y todo lo que sigue
                '/\s*\d{4}\s*$/i',             // Año al final (ej: " 2023")
                '/\s*\(\d{4}\)\s*$/i'          // Año entre paréntesis al final
            ];

            foreach ($patterns as $pattern) {
                $clean_title = preg_replace($pattern, '', $clean_title);
            }

            $clean_title = trim($clean_title);
            $clean_title = rtrim($clean_title, ' -_.');
            return trim($clean_title);
        }

        // Agrupar por serie y agregar info TMDB
        $grouped_results = [];
        foreach ($raw_results as $item) {
            // Verificar si es una serie o película
            if (isSeriesEpisode($item['title'])) {
                // Es una serie, extraer nombre base
                $base_title = extractSeriesBaseName($item['title']);
            } else {
                // Es una película, usar título completo
                $base_title = $item['title'];
            }

            $base_title = trim($base_title);

            $key = strtolower($base_title);

            if (!isset($grouped_results[$key])) {
                $grouped_results[$key] = [
                    'base_title' => $base_title,
                    'items' => [],
                    'tmdb_info' => null,
                    'media_type' => $item['media_type'],
                    'year' => $item['year'],
                    'lists' => [] // Cambiar para agrupar por listas
                ];

                // Buscar info TMDB solo una vez por serie
                $grouped_results[$key]['tmdb_info'] = findBestTMDBMatch($base_title);
            }

            // Agrupar por lista
            $list_id = $item['list_id'];
            $list_name = $item['list_name'];

            if (!isset($grouped_results[$key]['lists'][$list_id])) {
                $grouped_results[$key]['lists'][$list_id] = [
                    'list_name' => $list_name,
                    'list_id' => $list_id,
                    'items' => []
                ];
            }

            $grouped_results[$key]['lists'][$list_id]['items'][] = $item;
            $grouped_results[$key]['items'][] = $item; // Mantener para compatibilidad
        }
        
        $results = array_values($grouped_results);
        
    } catch (Exception $e) {
        $error_message = "Error en la búsqueda: " . $e->getMessage();
    }
}

// Obtener pedidos recientes para sugerencias
$recent_orders = [];
try {
    $stmt = $pdo->query("
        SELECT DISTINCT title 
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND title IS NOT NULL 
        AND title != ''
        ORDER BY created_at DESC 
        LIMIT 15
    ");
    $recent_orders = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    // Silenciar error de pedidos
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Búsqueda M3U con TMDB - Admin Panel</title>
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .back-link {
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: var(--accent-dark);
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stats {
            display: flex;
            gap: 2rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            min-width: 150px;
        }

        .stat-number {
            font-size: 2rem;
            color: var(--accent-color);
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .search-box {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .search-form {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .search-btn {
            padding: 1rem 2rem;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: bold;
            font-size: 1rem;
            transition: var(--transition);
        }

        .search-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .suggestions {
            margin-top: 1.5rem;
        }

        .suggestions h4 {
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .suggestion-tag {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .suggestion-tag:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }

        .results {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .result-item {
            background: var(--dark-bg);
            border-radius: var(--border-radius-lg);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .result-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
        }

        .result-content {
            display: flex;
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .tmdb-poster {
            width: 120px;
            height: 180px;
            border-radius: 8px;
            object-fit: cover;
            background: var(--secondary-color);
            flex-shrink: 0;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            color: var(--accent-color);
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .tmdb-title {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 1rem;
            font-style: italic;
        }

        .result-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .tmdb-overview {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            max-height: 4.5em;
            overflow: hidden;
        }

        .episodes-count {
            background: var(--accent-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .download-section {
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .download-section h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .list-download-btn {
            position: relative;
            overflow: hidden;
        }

        .list-download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .episode-badge {
            background: rgba(0, 0, 0, 0.4);
            padding: 0.1rem 0.4rem;
            border-radius: 10px;
            margin-left: 0.3rem;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .result-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-copy {
            background: var(--success-color);
            color: white;
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .no-results {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .error {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .success {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        @media (max-width: 768px) {
            .result-content {
                flex-direction: column;
            }
            
            .tmdb-poster {
                width: 100%;
                height: 200px;
                align-self: center;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navegación Admin -->
        <div style="margin-bottom: 2rem; display: flex; gap: 1rem; flex-wrap: wrap;">
            <a href="admin.php" style="color: #46d347; text-decoration: none; padding: 0.5rem 1rem; background: rgba(70, 211, 71, 0.1); border: 1px solid #46d347; border-radius: 6px;">
                <i class="fas fa-arrow-left"></i> Volver al Admin
            </a>
            <a href="m3u_search_tmdb_enhanced.php" style="color: #3b82f6; text-decoration: none; padding: 0.5rem 1rem; background: rgba(59, 130, 246, 0.1); border: 1px solid #3b82f6; border-radius: 6px;">
                <i class="fas fa-search-plus"></i> Búsqueda Avanzada
            </a>
            <a href="m3u_smart_search_v2.php" style="color: #f59e0b; text-decoration: none; padding: 0.5rem 1rem; background: rgba(245, 158, 11, 0.1); border: 1px solid #f59e0b; border-radius: 6px;">
                <i class="fas fa-brain"></i> Análisis Inteligente
            </a>
            <a href="m3u_content_filter.php" style="color: #8b5cf6; text-decoration: none; padding: 0.5rem 1rem; background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 6px;">
                <i class="fas fa-filter"></i> Filtro de Contenido
            </a>
        </div>

        <div class="header">
            <h1><i class="fas fa-search"></i> Búsqueda M3U con TMDB</h1>
            <p>Encuentra series y películas con información TMDB mejorada - Panel Administrativo</p>

            <?php if (!isTMDBConfigured()): ?>
            <div style="background: rgba(255, 193, 7, 0.2); border: 1px solid #ffc107; border-radius: 8px; padding: 1rem; margin: 1rem 0; font-size: 0.9rem;">
                <strong>⚠️ TMDB no configurado:</strong> Para ver posters y información adicional, configura tu API key en
                <code>tmdb_config.php</code>. Es gratis en
                <a href="https://www.themoviedb.org/" target="_blank" style="color: #ffc107;">themoviedb.org</a>
            </div>
            <?php endif; ?>
        </div>

        <!-- Estadísticas -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number"><?php echo number_format($total_content); ?></div>
                <div class="stat-label">Contenido Total</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo count($results); ?></div>
                <div class="stat-label">Series/Películas</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo isTMDBConfigured() ? '✅' : '❌'; ?></div>
                <div class="stat-label">TMDB API</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo defined('TMDB_LANGUAGE') ? TMDB_LANGUAGE : 'N/A'; ?></div>
                <div class="stat-label">Idioma TMDB</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo date('H:i:s'); ?></div>
                <div class="stat-label">Última carga</div>
            </div>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="search-box">
            <?php if ($auto_search && $search_query): ?>
            <div class="admin-search-alert" style="background: linear-gradient(135deg, rgba(70, 211, 71, 0.15), rgba(40, 167, 69, 0.1)); border: 2px solid var(--accent-color); border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 4px 12px rgba(70, 211, 71, 0.2);">
                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                    <div style="width: 40px; height: 40px; background: var(--accent-color); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-search" style="color: white; font-size: 1.2rem;"></i>
                    </div>
                    <div>
                        <h3 style="color: var(--accent-color); margin: 0; font-size: 1.2rem;">🎯 Búsqueda desde Panel Admin</h3>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">Búsqueda automática ejecutada</p>
                    </div>
                </div>
                <div style="background: rgba(0,0,0,0.2); padding: 1rem; border-radius: 8px; border-left: 4px solid var(--accent-color);">
                    <strong style="color: var(--text-primary);">Búsqueda inteligente para:</strong>
                    <div style="font-size: 1.1rem; color: var(--accent-color); font-weight: bold; margin-top: 0.5rem;">
                        "<?php echo htmlspecialchars($search_query); ?>"
                    </div>
                    <?php if ($auto_search): ?>
                    <div style="font-size: 0.9rem; color: var(--text-secondary); margin-top: 0.5rem;">
                        <i class="fas fa-brain"></i> Búsqueda inteligente activada: incluye título original, TMDB ID y variaciones
                    </div>
                    <?php endif; ?>
                </div>
                <?php if (!empty($results)): ?>
                <div style="margin-top: 1rem; padding: 0.75rem; background: rgba(40, 167, 69, 0.1); border-radius: 6px;">
                    <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                    <strong style="color: var(--success-color);">¡Coincidencias encontradas!</strong>
                    Se encontraron <?php echo count($results); ?> resultado(s) relevantes en las listas M3U.

                    <?php
                    // Mostrar estadísticas de calidad
                    $with_tmdb = 0;
                    $match_types = [];
                    foreach ($results as $result) {
                        if (isset($result['tmdb_id']) && $result['tmdb_id']) $with_tmdb++;
                        $match_type = $result['match_type'] ?? 'unknown';
                        $match_types[$match_type] = ($match_types[$match_type] ?? 0) + 1;
                    }
                    ?>

                    <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--text-secondary);">
                        <strong>Calidad:</strong>
                        <?php echo $with_tmdb; ?> con TMDB ID |
                        <?php if (isset($match_types['tmdb_title'])): ?>
                            <?php echo $match_types['tmdb_title']; ?> coincidencias TMDB exactas |
                        <?php endif; ?>
                        Búsqueda por etapas aplicada
                    </div>
                </div>
                <?php else: ?>
                <div style="margin-top: 1rem; padding: 0.75rem; background: rgba(255, 193, 7, 0.1); border-radius: 6px;">
                    <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                    <strong style="color: var(--warning-color);">Sin coincidencias relevantes</strong>
                    No se encontraron resultados de calidad para este título en las listas M3U activas.
                    <div style="margin-top: 0.5rem; font-size: 0.9rem;">
                        💡 Intenta buscar por TMDB ID o título original en inglés para mejores resultados.
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <form method="GET" class="search-form">
                <input type="text" 
                       name="q" 
                       class="search-input" 
                       value="<?php echo htmlspecialchars($search_query); ?>" 
                       placeholder="Buscar por título o TMDB ID (ej: Gone Girl, 210577, Breaking Bad)"
                       autofocus>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    Buscar
                </button>
            </form>

            <div style="background: rgba(6, 182, 212, 0.1); border: 1px solid #06b6d4; color: #06b6d4; padding: 1rem; border-radius: 6px; margin-top: 1rem; font-size: 0.9rem;">
                <strong>💡 Búsqueda TMDB Mejorada:</strong>
                <ul style="margin: 0.5rem 0 0 1.5rem;">
                    <li><strong>Por título:</strong> "Gone Girl", "Breaking Bad", "Avengers"</li>
                    <li><strong>Por TMDB ID:</strong> "210577" (más preciso, encuentra todas las versiones)</li>
                    <li><strong>Prioridad:</strong> Resultados con TMDB ID aparecen primero</li>
                    <li><strong>Información:</strong> Muestra posters, años y títulos oficiales cuando disponible</li>
                </ul>
            </div>

            <?php if (!empty($recent_orders)): ?>
            <div class="suggestions">
                <h4>Pedidos recientes (haz clic para buscar):</h4>
                <div class="suggestion-tags">
                    <?php foreach ($recent_orders as $order_title): ?>
                    <span class="suggestion-tag" onclick="searchFor('<?php echo addslashes($order_title); ?>')">
                        <?php echo htmlspecialchars($order_title); ?>
                    </span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Resultados -->
        <?php if (isset($error_message)): ?>
        <div class="error"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <?php if ($search_query): ?>
        <div class="results">
            <h2>Resultados para: "<?php echo htmlspecialchars($search_query); ?>"</h2>
            
            <?php if (empty($results)): ?>
            <div class="no-results">
                <i class="fas fa-search-minus"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con términos más generales o verifica que tengas listas M3U analizadas</p>
                <div style="margin-top: 1.5rem; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;">
                    <h4 style="color: var(--accent-color); margin-bottom: 1rem;">💡 Consejos de búsqueda:</h4>
                    <ul style="color: var(--text-secondary); line-height: 1.8;">
                        <li>Usa solo el nombre principal (ej: "Breaking" en lugar de "Breaking Bad S01E01")</li>
                        <li>Evita caracteres especiales y números de temporada</li>
                        <li>Prueba con nombres en inglés si no encuentras en español</li>
                    </ul>
                </div>
                <p style="margin-top: 2rem;">
                    <a href="m3u_content_viewer.php" class="btn btn-info">
                        <i class="fas fa-tv"></i>
                        Ver Todo el Contenido
                    </a>
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($results as $result): ?>
            <div class="result-item">
                <div class="result-content">
                    <?php
                    $poster_url = null;
                    if ($result['tmdb_info'] && isset($result['tmdb_info']['poster_path'])) {
                        $poster_url = getTMDBImageUrl($result['tmdb_info']['poster_path'], 'w200');
                    }
                    ?>
                    <?php if ($poster_url): ?>
                    <img src="<?php echo $poster_url; ?>"
                         alt="Poster"
                         class="tmdb-poster"
                         onerror="this.style.display='none'">
                    <?php else: ?>
                    <div class="tmdb-poster" style="display: flex; align-items: center; justify-content: center; background: var(--secondary-color); color: var(--text-secondary);">
                        <i class="fas fa-image" style="font-size: 2rem;"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="result-info">
                        <div class="result-title"><?php echo htmlspecialchars($result['base_title']); ?></div>
                        
                        <?php if ($result['tmdb_info']): ?>
                        <div class="tmdb-title">
                            📺 <?php echo htmlspecialchars($result['tmdb_info']['title'] ?? $result['tmdb_info']['name'] ?? ''); ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="result-meta">
                            <div class="meta-item">
                                <i class="fas fa-list"></i>
                                Disponible en <?php echo count($result['lists']); ?> lista(s)
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-film"></i>
                                <?php echo $result['media_type'] === 'movie' ? '🎬 Película' : ($result['media_type'] === 'tv' ? '📺 Serie' : '❓ Desconocido'); ?>
                            </div>
                            <?php if ($result['tmdb_info'] && isset($result['tmdb_info']['release_date'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('Y', strtotime($result['tmdb_info']['release_date'])); ?>
                            </div>
                            <?php elseif ($result['tmdb_info'] && isset($result['tmdb_info']['first_air_date'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo date('Y', strtotime($result['tmdb_info']['first_air_date'])); ?>
                            </div>
                            <?php elseif ($result['year']): ?>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <?php echo $result['year']; ?>
                            </div>
                            <?php endif; ?>
                            <?php if ($result['tmdb_info'] && isset($result['tmdb_info']['vote_average'])): ?>
                            <div class="meta-item">
                                <i class="fas fa-star" style="color: #ffc107;"></i>
                                <?php echo number_format($result['tmdb_info']['vote_average'], 1); ?>/10
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($result['tmdb_info'] && !empty($result['tmdb_info']['overview'])): ?>
                        <div class="tmdb-overview">
                            <?php echo htmlspecialchars(substr($result['tmdb_info']['overview'], 0, 200)); ?>
                            <?php if (strlen($result['tmdb_info']['overview']) > 200): ?>...<?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="episodes-count">
                            📺 <?php echo count($result['items']); ?> episodio(s) disponible(s)
                        </div>

                        <!-- Opciones de descarga por lista -->
                        <div class="download-section">
                            <h4>
                                <i class="fas fa-download"></i>
                                Descargar desde:
                            </h4>
                            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                <?php foreach ($result['lists'] as $list): ?>
                                <a href="?download_series=1&series_title=<?php echo urlencode($result['base_title']); ?>&list_id=<?php echo $list['list_id']; ?>"
                                   class="btn btn-warning list-download-btn"
                                   style="font-size: 0.8rem; padding: 0.4rem 0.8rem;"
                                   title="Descargar <?php echo count($list['items']); ?> episodios de <?php echo htmlspecialchars($list['list_name']); ?>">
                                    <i class="fas fa-satellite-dish"></i>
                                    <?php echo htmlspecialchars($list['list_name']); ?>
                                    <span class="episode-badge">
                                        <?php echo count($list['items']); ?> eps
                                    </span>
                                </a>
                                <?php endforeach; ?>
                            </div>

                            <?php if (count($result['lists']) > 1): ?>
                            <div style="margin-top: 0.5rem; font-size: 0.8rem; color: var(--text-secondary);">
                                <i class="fas fa-info-circle"></i>
                                Cada botón descarga solo los episodios de esa lista específica
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="result-actions">
                            <button class="btn btn-copy" onclick="copyFirstUrl('<?php echo addslashes($result['items'][0]['url']); ?>')">
                                <i class="fas fa-copy"></i>
                                Copiar Primera URL
                            </button>

                            <?php if (count($result['lists']) === 1): ?>
                            <?php $single_list = array_values($result['lists'])[0]; ?>
                            <a href="?download_series=1&series_title=<?php echo urlencode($result['base_title']); ?>&list_id=<?php echo $single_list['list_id']; ?>"
                               class="btn btn-info">
                                <i class="fas fa-download"></i>
                                Descargar Todo (<?php echo count($single_list['items']); ?> eps)
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php if (count($results) >= 500): ?>
            <div class="success">
                <i class="fas fa-info-circle"></i>
                Se muestran los primeros 500 resultados agrupados. Usa términos más específicos para refinar la búsqueda.
            </div>
            <?php endif; ?>

            <div style="background: rgba(70, 211, 71, 0.1); border: 1px solid var(--accent-color); border-radius: 8px; padding: 1.5rem; margin-top: 2rem;">
                <h3 style="color: var(--accent-color); margin-bottom: 1rem;">
                    <i class="fas fa-download"></i>
                    Sobre las Descargas
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; color: var(--text-secondary);">
                    <div>
                        <strong style="color: var(--text-primary);">📁 Por Lista:</strong><br>
                        Cada botón descarga solo los episodios de esa lista específica
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">📺 Formato M3U:</strong><br>
                        Archivos compatibles con VLC, IPTV players y reproductores
                    </div>
                    <div>
                        <strong style="color: var(--text-primary);">🎯 Contenido:</strong><br>
                        Solo episodios que coinciden con tu búsqueda
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div style="margin-top: 2rem; text-align: center; color: var(--text-secondary);">
            <p>
                <a href="m3u_manager.php" style="color: var(--accent-color);">
                    <i class="fas fa-cog"></i> Gestionar Listas
                </a> | 
                <a href="m3u_content_viewer.php" style="color: var(--accent-color);">
                    <i class="fas fa-tv"></i> Ver Todo
                </a> | 
                <a href="admin.php" style="color: var(--accent-color);">
                    <i class="fas fa-home"></i> Admin
                </a>
            </p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function searchFor(title) {
            document.querySelector('input[name="q"]').value = title;
            document.querySelector('form').submit();
        }

        function copyFirstUrl(url) {
            copyToClipboard(url);
        }

        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    showMessage('✅ URL copiada al portapapeles');
                }).catch(function(err) {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showMessage('✅ URL copiada al portapapeles');
            } catch (err) {
                showMessage('❌ Error al copiar. Copia manualmente la URL.');
            }
            
            document.body.removeChild(textArea);
        }



        function showMessage(message) {
            const div = document.createElement('div');
            div.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            div.textContent = message;
            document.body.appendChild(div);
            
            setTimeout(() => {
                div.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => document.body.removeChild(div), 300);
            }, 3000);
        }

        // Auto-focus en el campo de búsqueda y scroll automático para búsquedas desde admin
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="q"]');
            const urlParams = new URLSearchParams(window.location.search);
            const autoSearch = urlParams.get('auto_search');

            if (autoSearch === '1' && searchInput && searchInput.value) {
                // Si es búsqueda automática desde admin, hacer scroll hacia los resultados
                console.log('🎯 Búsqueda automática desde admin detectada:', searchInput.value);

                // Mostrar notificación de búsqueda automática
                setTimeout(() => {
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #10b981;
                        color: white;
                        padding: 1rem 1.5rem;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                        z-index: 10000;
                        font-weight: 500;
                        animation: slideIn 0.3s ease-out;
                    `;
                    notification.innerHTML = '<i class="fas fa-search"></i> Búsqueda automática desde Admin ejecutada';
                    document.body.appendChild(notification);

                    setTimeout(() => {
                        notification.style.animation = 'slideOut 0.3s ease-in forwards';
                        setTimeout(() => notification.remove(), 300);
                    }, 3000);
                }, 200);

                setTimeout(() => {
                    const resultsSection = document.querySelector('.results');
                    if (resultsSection) {
                        resultsSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 800);
            } else if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });

        // Agregar estilos para animaciones
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
