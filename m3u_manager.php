<?php
// Gestor de listas M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

$message = '';
$message_type = '';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para crear carpeta de lista
function createListFolder($listName, $listId) {
    $folderName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $listName) . '_' . $listId;
    $folderPath = 'uploads/m3u_files/' . $folderName;

    if (!file_exists($folderPath)) {
        mkdir($folderPath, 0755, true);
    }

    return $folderName;
}

// Función para generar URL de Xtream Codes
function generateXtreamM3uUrl($serverUrl, $username, $password) {
    $serverUrl = rtrim($serverUrl, '/');
    return $serverUrl . '/get.php?username=' . urlencode($username) . '&password=' . urlencode($password) . '&type=m3u_plus&output=ts';
}

// Procesar formularios
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_list'])) {
        // Agregar nueva lista
        $name = trim($_POST['name']);
        $list_type = $_POST['list_type'];
        $username = trim($_POST['username']) ?: null;
        $password = trim($_POST['password']) ?: null;

        if ($list_type === 'xtream_codes') {
            $server_url = trim($_POST['server_url']);
            $url = generateXtreamM3uUrl($server_url, $username, $password);
        } else {
            $url = trim($_POST['url']);
            $server_url = null;
        }

        if ($name && (($list_type === 'direct_m3u' && $url) || ($list_type === 'xtream_codes' && $server_url && $username && $password))) {
            try {
                $stmt = $pdo->prepare("INSERT INTO m3u_lists (name, url, username, password, server_url, list_type) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $url, $username, $password, $server_url, $list_type]);

                $listId = $pdo->lastInsertId();
                $folderName = createListFolder($name, $listId);

                // Actualizar con el nombre de carpeta
                $stmt = $pdo->prepare("UPDATE m3u_lists SET folder_name = ? WHERE id = ?");
                $stmt->execute([$folderName, $listId]);

                $message = "Lista M3U agregada exitosamente. Carpeta creada: $folderName";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error al agregar lista: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Por favor completa todos los campos requeridos";
            $message_type = "error";
        }
    }
    
    if (isset($_POST['toggle_status'])) {
        // Cambiar estado de lista
        $list_id = $_POST['list_id'];
        $new_status = $_POST['current_status'] == 1 ? 0 : 1;
        
        try {
            $stmt = $pdo->prepare("UPDATE m3u_lists SET is_active = ? WHERE id = ?");
            $stmt->execute([$new_status, $list_id]);
            $message = "Estado de lista actualizado";
            $message_type = "success";
        } catch (PDOException $e) {
            $message = "Error al actualizar estado: " . $e->getMessage();
            $message_type = "error";
        }
    }
    
    if (isset($_POST['edit_list'])) {
        // Editar lista existente
        $list_id = $_POST['list_id'];
        $name = trim($_POST['name']);
        $list_type = $_POST['list_type'];
        $username = trim($_POST['username']) ?: null;
        $password = trim($_POST['password']) ?: null;

        if ($list_type === 'xtream_codes') {
            $server_url = trim($_POST['server_url']);
            $url = generateXtreamM3uUrl($server_url, $username, $password);
        } else {
            $url = trim($_POST['url']);
            $server_url = null;
        }

        if ($name && (($list_type === 'direct_m3u' && $url) || ($list_type === 'xtream_codes' && $server_url && $username && $password))) {
            try {
                $stmt = $pdo->prepare("
                    UPDATE m3u_lists
                    SET name = ?, url = ?, username = ?, password = ?, server_url = ?, list_type = ?, last_updated = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$name, $url, $username, $password, $server_url, $list_type, $list_id]);

                $message = "Lista actualizada exitosamente";
                $message_type = "success";
            } catch (PDOException $e) {
                $message = "Error al actualizar lista: " . $e->getMessage();
                $message_type = "error";
            }
        } else {
            $message = "Por favor completa todos los campos requeridos";
            $message_type = "error";
        }
    }

    if (isset($_POST['delete_list'])) {
        // Eliminar lista
        $list_id = $_POST['list_id'];

        try {
            $stmt = $pdo->prepare("DELETE FROM m3u_lists WHERE id = ?");
            $stmt->execute([$list_id]);
            $message = "Lista eliminada exitosamente";
            $message_type = "success";
        } catch (PDOException $e) {
            $message = "Error al eliminar lista: " . $e->getMessage();
            $message_type = "error";
        }
    }
}

// Obtener lista para editar si se solicita
$edit_list = null;
if (isset($_GET['edit_id'])) {
    $edit_id = $_GET['edit_id'];
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_list = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Obtener todas las listas con estadísticas TMDB
$stmt = $pdo->query("
    SELECT l.*,
           COUNT(c.id) as content_count,
           COUNT(CASE WHEN c.tmdb_id IS NOT NULL AND c.tmdb_id > 0 THEN 1 END) as tmdb_count,
           COUNT(CASE WHEN c.media_type = 'movie' THEN 1 END) as movies_count,
           COUNT(CASE WHEN c.media_type = 'tv' THEN 1 END) as series_count,
           MAX(c.updated_at) as last_content_update
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    GROUP BY l.id
    ORDER BY l.created_at DESC
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener estadísticas globales TMDB
$stmt = $pdo->query("
    SELECT
        COUNT(*) as total_content,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as total_tmdb,
        COUNT(CASE WHEN media_type = 'movie' THEN 1 END) as total_movies,
        COUNT(CASE WHEN media_type = 'tv' THEN 1 END) as total_series
    FROM m3u_content c
    JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
");
$global_stats = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📡 Gestor de Listas M3U - RGS TOOL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .message.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .form-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .form-section h2 {
            margin-bottom: 1.5rem;
            color: var(--accent-color);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: var(--primary-color);
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .lists-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .list-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .list-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .list-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .list-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .list-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(40, 167, 69, 0.2);
            color: var(--success-color);
        }

        .status-inactive {
            background: rgba(220, 53, 69, 0.2);
            color: var(--error-color);
        }

        .list-info {
            margin-bottom: 1rem;
        }

        .list-info p {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .list-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .list-actions .btn {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-satellite-dish"></i> Gestor de Listas M3U</h1>
            <p>Administra tus listas IPTV y su contenido</p>

            <div style="margin-top: 1.5rem; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <a href="admin_tmdb_hub.php" style="color: #01b4e4; text-decoration: none; font-weight: 500; background: rgba(1, 180, 228, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid #01b4e4; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-film"></i>
                    Hub TMDB
                </a>
                <a href="m3u_analyzer_modal.php" style="color: var(--accent-color); text-decoration: none; font-weight: 500; background: rgba(70, 211, 71, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid var(--accent-color); display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-search"></i>
                    Analizador Modal
                </a>
                <a href="m3u_search_tmdb.php" style="color: #17a2b8; text-decoration: none; font-weight: 500; background: rgba(23, 162, 184, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid #17a2b8; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-search"></i>
                    Buscar con TMDB
                </a>
                <a href="m3u_smart_search_v2.php" style="color: #f59e0b; text-decoration: none; font-weight: 500; background: rgba(245, 158, 11, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid #f59e0b; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-brain"></i>
                    Análisis Inteligente
                </a>
                <a href="m3u_content_filter.php" style="color: #8b5cf6; text-decoration: none; font-weight: 500; background: rgba(139, 92, 246, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid #8b5cf6; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-filter"></i>
                    Filtro de Contenido
                </a>
                <a href="m3u_content_viewer.php" style="color: #ffc107; text-decoration: none; font-weight: 500; background: rgba(255, 193, 7, 0.1); padding: 0.5rem 1rem; border-radius: 6px; border: 1px solid #ffc107; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-tv"></i>
                    Ver Contenido
                </a>
            </div>
        </div>

        <?php if ($message): ?>
        <div class="message <?php echo $message_type; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($lists); ?></div>
                <div class="stat-label">Listas Totales</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count(array_filter($lists, fn($l) => $l['is_active'])); ?></div>
                <div class="stat-label">Listas Activas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($global_stats['total_content']); ?></div>
                <div class="stat-label">Contenido Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($global_stats['total_tmdb']); ?></div>
                <div class="stat-label">Con TMDB ID</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($global_stats['total_movies']); ?></div>
                <div class="stat-label">Películas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($global_stats['total_series']); ?></div>
                <div class="stat-label">Series</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $global_stats['total_content'] > 0 ? round(($global_stats['total_tmdb']/$global_stats['total_content'])*100, 1) : 0; ?>%</div>
                <div class="stat-label">% con TMDB</div>
            </div>
        </div>

        <!-- Formulario para agregar nueva lista -->
        <div class="form-section">
            <h2><i class="fas fa-plus"></i> Agregar Nueva Lista M3U</h2>
            <form method="POST" id="addListForm">
                <div class="form-group">
                    <label for="list_type">Tipo de Lista *</label>
                    <select id="list_type" name="list_type" class="form-input" required onchange="toggleFormFields()">
                        <option value="">Seleccionar tipo...</option>
                        <option value="direct_m3u">📄 URL Directa M3U</option>
                        <option value="xtream_codes">🔐 Xtream Codes</option>
                    </select>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Nombre de la Lista *</label>
                        <input type="text" id="name" name="name" class="form-input" required placeholder="Ej: Lista Premium IPTV">
                    </div>

                    <!-- Campos para URL directa -->
                    <div class="form-group" id="direct_url_group" style="display: none;">
                        <label for="url">URL de la Lista M3U *</label>
                        <input type="url" id="url" name="url" class="form-input" placeholder="http://ejemplo.com/lista.m3u">
                    </div>

                    <!-- Campos para Xtream Codes -->
                    <div class="form-group" id="server_url_group" style="display: none;">
                        <label for="server_url">URL del Servidor *</label>
                        <input type="url" id="server_url" name="server_url" class="form-input" placeholder="http://servidor.com:8080">
                    </div>

                    <div class="form-group" id="username_group" style="display: none;">
                        <label for="username">Usuario *</label>
                        <input type="text" id="username" name="username" class="form-input" placeholder="tu_usuario">
                    </div>

                    <div class="form-group" id="password_group" style="display: none;">
                        <label for="password">Contraseña *</label>
                        <input type="password" id="password" name="password" class="form-input" placeholder="tu_contraseña">
                    </div>
                </div>

                <div id="xtream_info" style="display: none; background: rgba(23, 162, 184, 0.1); padding: 1rem; border-radius: 8px; margin: 1rem 0; border-left: 4px solid var(--info-color);">
                    <p style="margin: 0; color: var(--info-color); font-size: 0.9rem;">
                        <i class="fas fa-info-circle"></i>
                        <strong>Xtream Codes:</strong> Se generará automáticamente la URL M3U usando tus credenciales.
                        Se creará una carpeta específica para esta lista.
                    </p>
                </div>

                <button type="submit" name="add_list" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Agregar Lista
                </button>
            </form>
        </div>

        <!-- Formulario de edición -->
        <?php if ($edit_list): ?>
        <div class="form-section">
            <h2><i class="fas fa-edit"></i> Editar Lista M3U: <?php echo htmlspecialchars($edit_list['name']); ?></h2>
            <form method="POST" id="editListForm">
                <input type="hidden" name="list_id" value="<?php echo $edit_list['id']; ?>">

                <div class="form-group">
                    <label for="edit_list_type">Tipo de Lista *</label>
                    <select id="edit_list_type" name="list_type" class="form-input" required onchange="toggleEditFormFields()">
                        <option value="direct_m3u" <?php echo ($edit_list['list_type'] ?? 'direct_m3u') === 'direct_m3u' ? 'selected' : ''; ?>>📄 URL Directa M3U</option>
                        <option value="xtream_codes" <?php echo ($edit_list['list_type'] ?? '') === 'xtream_codes' ? 'selected' : ''; ?>>🔐 Xtream Codes</option>
                    </select>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="edit_name">Nombre de la Lista *</label>
                        <input type="text" id="edit_name" name="name" class="form-input" required value="<?php echo htmlspecialchars($edit_list['name']); ?>">
                    </div>

                    <!-- Campos para URL directa -->
                    <div class="form-group" id="edit_direct_url_group">
                        <label for="edit_url">URL de la Lista M3U *</label>
                        <input type="url" id="edit_url" name="url" class="form-input" value="<?php echo htmlspecialchars($edit_list['url']); ?>">
                    </div>

                    <!-- Campos para Xtream Codes -->
                    <div class="form-group" id="edit_server_url_group">
                        <label for="edit_server_url">URL del Servidor *</label>
                        <input type="url" id="edit_server_url" name="server_url" class="form-input" value="<?php echo htmlspecialchars($edit_list['server_url'] ?? ''); ?>">
                    </div>

                    <div class="form-group" id="edit_username_group">
                        <label for="edit_username">Usuario *</label>
                        <input type="text" id="edit_username" name="username" class="form-input" value="<?php echo htmlspecialchars($edit_list['username'] ?? ''); ?>">
                    </div>

                    <div class="form-group" id="edit_password_group">
                        <label for="edit_password">Contraseña *</label>
                        <input type="password" id="edit_password" name="password" class="form-input" value="<?php echo htmlspecialchars($edit_list['password'] ?? ''); ?>">
                    </div>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" name="edit_list" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Guardar Cambios
                    </button>
                    <a href="m3u_manager.php" class="btn" style="background: #666; color: white; text-decoration: none;">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                </div>
            </form>
        </div>
        <?php endif; ?>

        <!-- Lista de listas M3U -->
        <div class="form-section">
            <h2><i class="fas fa-list"></i> Listas M3U Configuradas</h2>
            
            <?php if (empty($lists)): ?>
            <p style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i><br>
                No hay listas M3U configuradas aún
            </p>
            <?php else: ?>
            <div class="lists-grid">
                <?php foreach ($lists as $list): ?>
                <div class="list-card">
                    <div class="list-header">
                        <div>
                            <div class="list-title"><?php echo htmlspecialchars($list['name']); ?></div>
                            <span class="list-status <?php echo $list['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                <?php echo $list['is_active'] ? 'Activa' : 'Inactiva'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="list-info">
                        <p><i class="fas fa-link"></i> <strong>URL:</strong> <?php echo htmlspecialchars(substr($list['url'], 0, 50)) . '...'; ?></p>
                        <p><i class="fas fa-film"></i> <strong>Contenido:</strong> <?php echo number_format($list['content_count']); ?> elementos</p>
                        <?php if ($list['tmdb_count'] > 0): ?>
                        <p><i class="fas fa-database" style="color: #01b4e4;"></i> <strong>Con TMDB:</strong> <?php echo number_format($list['tmdb_count']); ?> (<?php echo $list['content_count'] > 0 ? round(($list['tmdb_count']/$list['content_count'])*100, 1) : 0; ?>%)</p>
                        <?php endif; ?>
                        <?php if ($list['movies_count'] > 0 || $list['series_count'] > 0): ?>
                        <p><i class="fas fa-chart-pie"></i> <strong>Tipo:</strong>
                            <?php if ($list['movies_count'] > 0): ?>🎬 <?php echo number_format($list['movies_count']); ?> películas<?php endif; ?>
                            <?php if ($list['movies_count'] > 0 && $list['series_count'] > 0): ?> | <?php endif; ?>
                            <?php if ($list['series_count'] > 0): ?>📺 <?php echo number_format($list['series_count']); ?> series<?php endif; ?>
                        </p>
                        <?php endif; ?>
                        <p><i class="fas fa-clock"></i> <strong>Creada:</strong> <?php echo date('d/m/Y H:i', strtotime($list['created_at'])); ?></p>
                        <?php if ($list['last_scan']): ?>
                        <p><i class="fas fa-search"></i> <strong>Último escaneo:</strong> <?php echo date('d/m/Y H:i', strtotime($list['last_scan'])); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="list-actions">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="list_id" value="<?php echo $list['id']; ?>">
                            <input type="hidden" name="current_status" value="<?php echo $list['is_active']; ?>">
                            <button type="submit" name="toggle_status" class="btn <?php echo $list['is_active'] ? 'btn-warning' : 'btn-info'; ?>">
                                <i class="fas fa-<?php echo $list['is_active'] ? 'pause' : 'play'; ?>"></i>
                                <?php echo $list['is_active'] ? 'Desactivar' : 'Activar'; ?>
                            </button>
                        </form>

                        <a href="m3u_analyzer_modal.php" class="btn btn-info">
                            <i class="fas fa-search"></i>
                            Analizar
                        </a>

                        <a href="?edit_id=<?php echo $list['id']; ?>" class="btn" style="background: #6f42c1; color: white;">
                            <i class="fas fa-edit"></i>
                            Editar
                        </a>

                        <form method="POST" style="display: inline;" onsubmit="return confirm('¿Estás seguro de eliminar esta lista?')">
                            <input type="hidden" name="list_id" value="<?php echo $list['id']; ?>">
                            <button type="submit" name="delete_list" class="btn btn-danger">
                                <i class="fas fa-trash"></i>
                                Eliminar
                            </button>
                        </form>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleFormFields() {
            const listType = document.getElementById('list_type').value;

            // Ocultar todos los campos
            document.getElementById('direct_url_group').style.display = 'none';
            document.getElementById('server_url_group').style.display = 'none';
            document.getElementById('username_group').style.display = 'none';
            document.getElementById('password_group').style.display = 'none';
            document.getElementById('xtream_info').style.display = 'none';

            // Limpiar campos
            document.getElementById('url').value = '';
            document.getElementById('server_url').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';

            // Remover required de todos los campos
            document.getElementById('url').removeAttribute('required');
            document.getElementById('server_url').removeAttribute('required');
            document.getElementById('username').removeAttribute('required');
            document.getElementById('password').removeAttribute('required');

            if (listType === 'direct_m3u') {
                document.getElementById('direct_url_group').style.display = 'block';
                document.getElementById('url').setAttribute('required', 'required');
            } else if (listType === 'xtream_codes') {
                document.getElementById('server_url_group').style.display = 'block';
                document.getElementById('username_group').style.display = 'block';
                document.getElementById('password_group').style.display = 'block';
                document.getElementById('xtream_info').style.display = 'block';

                document.getElementById('server_url').setAttribute('required', 'required');
                document.getElementById('username').setAttribute('required', 'required');
                document.getElementById('password').setAttribute('required', 'required');
            }
        }

        // Validación del formulario
        document.getElementById('addListForm').addEventListener('submit', function(e) {
            const listType = document.getElementById('list_type').value;
            const name = document.getElementById('name').value.trim();

            if (!listType) {
                alert('Por favor selecciona el tipo de lista');
                e.preventDefault();
                return;
            }

            if (!name) {
                alert('Por favor ingresa un nombre para la lista');
                e.preventDefault();
                return;
            }

            if (listType === 'direct_m3u') {
                const url = document.getElementById('url').value.trim();
                if (!url) {
                    alert('Por favor ingresa la URL de la lista M3U');
                    e.preventDefault();
                    return;
                }
            } else if (listType === 'xtream_codes') {
                const serverUrl = document.getElementById('server_url').value.trim();
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!serverUrl || !username || !password) {
                    alert('Por favor completa todos los campos de Xtream Codes');
                    e.preventDefault();
                    return;
                }
            }
        });

        // Función para el formulario de edición
        function toggleEditFormFields() {
            const listType = document.getElementById('edit_list_type').value;

            // Ocultar todos los campos
            document.getElementById('edit_direct_url_group').style.display = 'none';
            document.getElementById('edit_server_url_group').style.display = 'none';
            document.getElementById('edit_username_group').style.display = 'none';
            document.getElementById('edit_password_group').style.display = 'none';

            // Remover required de todos los campos
            document.getElementById('edit_url').removeAttribute('required');
            document.getElementById('edit_server_url').removeAttribute('required');
            document.getElementById('edit_username').removeAttribute('required');
            document.getElementById('edit_password').removeAttribute('required');

            if (listType === 'direct_m3u') {
                document.getElementById('edit_direct_url_group').style.display = 'block';
                document.getElementById('edit_url').setAttribute('required', 'required');
            } else if (listType === 'xtream_codes') {
                document.getElementById('edit_server_url_group').style.display = 'block';
                document.getElementById('edit_username_group').style.display = 'block';
                document.getElementById('edit_password_group').style.display = 'block';

                document.getElementById('edit_server_url').setAttribute('required', 'required');
                document.getElementById('edit_username').setAttribute('required', 'required');
                document.getElementById('edit_password').setAttribute('required', 'required');
            }
        }

        // Inicializar formulario de edición si existe
        if (document.getElementById('edit_list_type')) {
            toggleEditFormFields();
        }
    </script>
</body>
</html>
