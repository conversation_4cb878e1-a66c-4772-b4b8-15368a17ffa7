<?php
// Hub central de herramientas TMDB para admin
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener estadísticas rápidas
$stats = [];

// Estadísticas de contenido
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb,
        COUNT(CASE WHEN media_type = 'movie' THEN 1 END) as movies,
        COUNT(CASE WHEN media_type = 'tv' THEN 1 END) as series
    FROM m3u_content c
    JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
");
$stats['content'] = $stmt->fetch(PDO::FETCH_ASSOC);

// Estadísticas de listas
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_lists,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_lists
    FROM m3u_lists
");
$stats['lists'] = $stmt->fetch(PDO::FETCH_ASSOC);

// Estadísticas de pedidos recientes
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb
    FROM orders
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
");
$stats['orders'] = $stmt->fetch(PDO::FETCH_ASSOC);

// Herramientas disponibles
$tools = [
    'search' => [
        'title' => 'Herramientas de Búsqueda',
        'icon' => 'fas fa-search',
        'color' => '#3b82f6',
        'items' => [
            [
                'name' => 'Búsqueda M3U con TMDB',
                'file' => 'm3u_search_tmdb.php',
                'description' => 'Búsqueda básica con información TMDB',
                'icon' => 'fas fa-search'
            ],
            [
                'name' => 'Búsqueda TMDB Avanzada',
                'file' => 'm3u_search_tmdb_enhanced.php',
                'description' => 'Búsqueda avanzada con filtros y estadísticas',
                'icon' => 'fas fa-search-plus'
            ],
            [
                'name' => 'Búsqueda M3U Mejorada',
                'file' => 'm3u_search_enhanced.php',
                'description' => 'Búsqueda tradicional mejorada',
                'icon' => 'fas fa-list'
            ]
        ]
    ],
    'analysis' => [
        'title' => 'Herramientas de Análisis',
        'icon' => 'fas fa-brain',
        'color' => '#f59e0b',
        'items' => [
            [
                'name' => 'Análisis Inteligente v2',
                'file' => 'm3u_smart_search_v2.php',
                'description' => 'Procesamiento con validación de similitud',
                'icon' => 'fas fa-brain'
            ],
            [
                'name' => 'Búsqueda Inteligente v1',
                'file' => 'm3u_smart_search.php',
                'description' => 'Procesamiento básico inteligente',
                'icon' => 'fas fa-robot'
            ],
            [
                'name' => 'Analizador Mejorado',
                'file' => 'm3u_tmdb_improved.php',
                'description' => 'Análisis con múltiples variaciones',
                'icon' => 'fas fa-cogs'
            ],
            [
                'name' => 'Analizador Básico',
                'file' => 'm3u_tmdb_basic.php',
                'description' => 'Procesamiento simple y seguro',
                'icon' => 'fas fa-play'
            ]
        ]
    ],
    'management' => [
        'title' => 'Gestión de Contenido',
        'icon' => 'fas fa-cogs',
        'color' => '#8b5cf6',
        'items' => [
            [
                'name' => 'Filtro de Contenido',
                'file' => 'm3u_content_filter.php',
                'description' => 'Separar películas/series de canales TV',
                'icon' => 'fas fa-filter'
            ],
            [
                'name' => 'Extractor TMDB',
                'file' => 'm3u_extract_tmdb.php',
                'description' => 'Extraer TMDB desde metadatos M3U',
                'icon' => 'fas fa-download'
            ],
            [
                'name' => 'Analizador de Metadatos',
                'file' => 'm3u_metadata_analyzer.php',
                'description' => 'Analizar estructura de listas M3U',
                'icon' => 'fas fa-microscope'
            ]
        ]
    ],
    'diagnostics' => [
        'title' => 'Diagnóstico y Debug',
        'icon' => 'fas fa-bug',
        'color' => '#ef4444',
        'items' => [
            [
                'name' => 'Debug Búsquedas TMDB',
                'file' => 'tmdb_debug_search.php',
                'description' => 'Diagnosticar problemas de búsqueda',
                'icon' => 'fas fa-bug'
            ],
            [
                'name' => 'Visor de Datos Crudos',
                'file' => 'm3u_raw_data_viewer.php',
                'description' => 'Ver estructura de datos M3U',
                'icon' => 'fas fa-database'
            ],
            [
                'name' => 'Diagnóstico TMDB',
                'file' => 'debug_tmdb.php',
                'description' => 'Verificar configuración TMDB',
                'icon' => 'fas fa-stethoscope'
            ]
        ]
    ],
    'config' => [
        'title' => 'Configuración',
        'icon' => 'fas fa-wrench',
        'color' => '#10b981',
        'items' => [
            [
                'name' => 'Verificar Estructura BD',
                'file' => 'check_database_structure.php',
                'description' => 'Verificar columnas y estructura',
                'icon' => 'fas fa-database'
            ],
            [
                'name' => 'Agregar Columnas TMDB',
                'file' => 'add_tmdb_columns_orders.php',
                'description' => 'Configurar base de datos',
                'icon' => 'fas fa-plus'
            ],
            [
                'name' => 'Resumen Implementación',
                'file' => 'tmdb_implementation_summary.php',
                'description' => 'Estado completo del sistema',
                'icon' => 'fas fa-chart-pie'
            ]
        ]
    ]
];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Hub TMDB - Panel Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --border-color: #334155;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark-bg) 0%, #020617 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--success-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .tool-category {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .category-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .tool-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .tool-item:last-child {
            border-bottom: none;
        }

        .tool-item:hover {
            background: rgba(37, 99, 235, 0.05);
        }

        .tool-link {
            color: var(--text-primary);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .tool-icon {
            width: 32px;
            height: 32px;
            background: var(--dark-bg);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
        }

        .tool-info h4 {
            margin-bottom: 0.25rem;
        }

        .tool-description {
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        .back-link {
            color: var(--success-color);
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--border-color);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-color);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-film"></i> Hub de Herramientas TMDB</h1>
            <p>Centro de control para todas las funcionalidades TMDB del sistema</p>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['content']['total']); ?></div>
                <div class="stat-label">Total de Contenido</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['content']['with_tmdb']); ?></div>
                <div class="stat-label">Con TMDB ID</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $stats['content']['total'] > 0 ? ($stats['content']['with_tmdb']/$stats['content']['total'])*100 : 0; ?>%"></div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['content']['movies']); ?></div>
                <div class="stat-label">Películas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['content']['series']); ?></div>
                <div class="stat-label">Series</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['lists']['active_lists']; ?></div>
                <div class="stat-label">Listas Activas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['orders']['with_tmdb']; ?>/<?php echo $stats['orders']['total']; ?></div>
                <div class="stat-label">Pedidos con TMDB (7d)</div>
            </div>
        </div>

        <!-- Herramientas por Categoría -->
        <div class="tools-grid">
            <?php foreach ($tools as $category_key => $category): ?>
            <div class="tool-category">
                <div class="category-header">
                    <div class="category-icon" style="background: <?php echo $category['color']; ?>20; color: <?php echo $category['color']; ?>;">
                        <i class="<?php echo $category['icon']; ?>"></i>
                    </div>
                    <div>
                        <h3><?php echo $category['title']; ?></h3>
                        <p style="color: var(--text-secondary); font-size: 0.9rem; margin: 0;">
                            <?php echo count($category['items']); ?> herramientas disponibles
                        </p>
                    </div>
                </div>
                
                <?php foreach ($category['items'] as $tool): ?>
                <div class="tool-item">
                    <a href="<?php echo $tool['file']; ?>" class="tool-link">
                        <div class="tool-icon">
                            <i class="<?php echo $tool['icon']; ?>"></i>
                        </div>
                        <div class="tool-info">
                            <h4><?php echo $tool['name']; ?></h4>
                            <p class="tool-description"><?php echo $tool['description']; ?></p>
                        </div>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endforeach; ?>
        </div>

        <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: rgba(37, 99, 235, 0.1); border: 1px solid var(--primary-color); border-radius: var(--border-radius);">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem;">🎯 Sistema TMDB Completamente Implementado</h3>
            <p style="color: var(--text-secondary);">
                Todas las herramientas están conectadas y funcionando. 
                Progreso actual: <?php echo $stats['content']['total'] > 0 ? round(($stats['content']['with_tmdb']/$stats['content']['total'])*100, 1) : 0; ?>% del contenido procesado.
            </p>
        </div>
    </div>
</body>
</html>
