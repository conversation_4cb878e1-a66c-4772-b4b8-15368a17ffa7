-- Script para agregar campos TMDB a la tabla orders
-- Ejecutar en phpMyAdmin o consola MySQL

-- Verificar y agregar campos TMDB a la tabla orders (solo si no existen)

-- Agregar media_type si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'media_type') > 0,
    'SELECT "Column media_type already exists" as message',
    'ALTER TABLE orders ADD COLUMN media_type ENUM(\'movie\', \'tv\', \'person\') NULL AFTER tmdb_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_title si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_title') > 0,
    'SELECT "Column tmdb_title already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER media_type'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_original_title si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_original_title') > 0,
    'SELECT "Column tmdb_original_title already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_year si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_year') > 0,
    'SELECT "Column tmdb_year already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_year INT NULL AFTER tmdb_original_title'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_poster_path si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_poster_path') > 0,
    'SELECT "Column tmdb_poster_path already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_year'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_overview si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_overview') > 0,
    'SELECT "Column tmdb_overview already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_language si no existe
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'orders'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_language') > 0,
    'SELECT "Column tmdb_language already exists" as message',
    'ALTER TABLE orders ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar índices para mejorar rendimiento
ALTER TABLE orders 
ADD INDEX idx_tmdb_id (tmdb_id),
ADD INDEX idx_media_type (media_type),
ADD INDEX idx_tmdb_year (tmdb_year);

-- Verificar y agregar campos TMDB a la tabla m3u_content (solo si no existen)

-- Agregar tmdb_id si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_id') > 0,
    'SELECT "Column tmdb_id already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_id INT NULL AFTER year'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_title si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_title') > 0,
    'SELECT "Column tmdb_title already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER tmdb_id'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_original_title si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_original_title') > 0,
    'SELECT "Column tmdb_original_title already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_poster_path si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_poster_path') > 0,
    'SELECT "Column tmdb_poster_path already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_original_title'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_overview si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_overview') > 0,
    'SELECT "Column tmdb_overview already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_language si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_language') > 0,
    'SELECT "Column tmdb_language already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_vote_average si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_vote_average') > 0,
    'SELECT "Column tmdb_vote_average already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_vote_average DECIMAL(3,1) NULL AFTER tmdb_language'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar tmdb_release_date si no existe en m3u_content
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'm3u_content'
     AND table_schema = DATABASE()
     AND column_name = 'tmdb_release_date') > 0,
    'SELECT "Column tmdb_release_date already exists in m3u_content" as message',
    'ALTER TABLE m3u_content ADD COLUMN tmdb_release_date DATE NULL AFTER tmdb_vote_average'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Agregar índices para m3u_content
ALTER TABLE m3u_content 
ADD INDEX idx_content_tmdb_id (tmdb_id),
ADD INDEX idx_content_tmdb_title (tmdb_title),
ADD INDEX idx_content_tmdb_year (tmdb_release_date);

-- Crear tabla para mapeo de TMDB IDs similares/relacionados
CREATE TABLE IF NOT EXISTS tmdb_similar_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT NOT NULL,
    similar_tmdb_id INT NOT NULL,
    similarity_score DECIMAL(5,2) DEFAULT 0.00,
    media_type ENUM('movie', 'tv') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tmdb_mapping (tmdb_id),
    INDEX idx_similar_mapping (similar_tmdb_id),
    UNIQUE KEY unique_mapping (tmdb_id, similar_tmdb_id)
);

-- Crear tabla para cache de búsquedas TMDB
CREATE TABLE IF NOT EXISTS tmdb_search_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_query VARCHAR(500) NOT NULL,
    search_hash VARCHAR(64) NOT NULL,
    tmdb_response JSON,
    language VARCHAR(10) DEFAULT 'es-MX',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
    INDEX idx_search_hash (search_hash),
    INDEX idx_expires (expires_at)
);
