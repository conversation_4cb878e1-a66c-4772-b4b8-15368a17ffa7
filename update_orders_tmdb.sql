-- <PERSON><PERSON>t para agregar campos TMDB a la tabla orders
-- Ejecutar en phpMyAdmin o consola MySQL

-- Agregar campos TMDB a la tabla orders
ALTER TABLE orders 
ADD COLUMN tmdb_id INT NULL AFTER title,
ADD COLUMN media_type ENUM('movie', 'tv', 'person') NULL AFTER tmdb_id,
ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER media_type,
ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title,
ADD COLUMN tmdb_year INT NULL AFTER tmdb_original_title,
ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_year,
ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path,
ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview;

-- Agregar índices para mejorar rendimiento
ALTER TABLE orders 
ADD INDEX idx_tmdb_id (tmdb_id),
ADD INDEX idx_media_type (media_type),
ADD INDEX idx_tmdb_year (tmdb_year);

-- Agregar campos TMDB a la tabla m3u_content también
ALTER TABLE m3u_content 
ADD COLUMN tmdb_id INT NULL AFTER year,
ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER tmdb_id,
ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title,
ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_original_title,
ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path,
ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview,
ADD COLUMN tmdb_vote_average DECIMAL(3,1) NULL AFTER tmdb_language,
ADD COLUMN tmdb_release_date DATE NULL AFTER tmdb_vote_average;

-- Agregar índices para m3u_content
ALTER TABLE m3u_content 
ADD INDEX idx_content_tmdb_id (tmdb_id),
ADD INDEX idx_content_tmdb_title (tmdb_title),
ADD INDEX idx_content_tmdb_year (tmdb_release_date);

-- Crear tabla para mapeo de TMDB IDs similares/relacionados
CREATE TABLE IF NOT EXISTS tmdb_similar_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT NOT NULL,
    similar_tmdb_id INT NOT NULL,
    similarity_score DECIMAL(5,2) DEFAULT 0.00,
    media_type ENUM('movie', 'tv') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tmdb_mapping (tmdb_id),
    INDEX idx_similar_mapping (similar_tmdb_id),
    UNIQUE KEY unique_mapping (tmdb_id, similar_tmdb_id)
);

-- Crear tabla para cache de búsquedas TMDB
CREATE TABLE IF NOT EXISTS tmdb_search_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_query VARCHAR(500) NOT NULL,
    search_hash VARCHAR(64) NOT NULL,
    tmdb_response JSON,
    language VARCHAR(10) DEFAULT 'es-MX',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
    INDEX idx_search_hash (search_hash),
    INDEX idx_expires (expires_at)
);
