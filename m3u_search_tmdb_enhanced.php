<?php
// Búsqueda mejorada con TMDB IDs
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$search_results = [];
$search_query = '';
$search_type = 'all';

if (isset($_GET['query']) && !empty($_GET['query'])) {
    $search_query = trim($_GET['query']);
    $search_type = $_GET['type'] ?? 'all';
    
    // Construir consulta SQL base
    $base_sql = "
        SELECT 
            c.id,
            c.title,
            c.clean_title,
            c.media_type,
            c.year,
            c.url,
            c.group_title,
            c.logo_url,
            c.tmdb_id,
            c.tmdb_title,
            c.tmdb_poster_path,
            l.name as list_name,
            l.id as list_id
        FROM m3u_content c
        JOIN m3u_lists l ON c.list_id = l.id
        WHERE l.is_active = 1
    ";
    
    $params = [];
    $conditions = [];
    
    // Filtrar por tipo de contenido
    if ($search_type === 'movie') {
        $conditions[] = "c.media_type = 'movie'";
    } elseif ($search_type === 'tv') {
        $conditions[] = "c.media_type = 'tv'";
    } elseif ($search_type === 'valid') {
        $conditions[] = "(c.media_type IN ('movie', 'tv') OR c.url REGEXP '/movie/' OR c.url REGEXP '/series/')";
    }
    
    // Detectar si es búsqueda por TMDB ID
    if (is_numeric($search_query)) {
        $tmdb_id = (int)$search_query;
        $conditions[] = "c.tmdb_id = ?";
        $params[] = $tmdb_id;
    } else {
        // Búsqueda por texto
        $search_terms = explode(' ', $search_query);
        $search_conditions = [];
        
        foreach ($search_terms as $term) {
            if (strlen($term) >= 2) {
                $search_conditions[] = "(
                    c.title LIKE ? OR 
                    c.clean_title LIKE ? OR 
                    c.tmdb_title LIKE ?
                )";
                $like_term = '%' . $term . '%';
                $params[] = $like_term;
                $params[] = $like_term;
                $params[] = $like_term;
            }
        }
        
        if (!empty($search_conditions)) {
            $conditions[] = '(' . implode(' AND ', $search_conditions) . ')';
        }
    }
    
    // Agregar condiciones a la consulta
    if (!empty($conditions)) {
        $base_sql .= " AND " . implode(' AND ', $conditions);
    }
    
    // Ordenar resultados (priorizar los que tienen TMDB ID)
    $base_sql .= " ORDER BY 
        CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
        CASE WHEN c.media_type IN ('movie', 'tv') THEN 0 ELSE 1 END,
        c.title ASC
        LIMIT 100";
    
    try {
        $stmt = $pdo->prepare($base_sql);
        $stmt->execute($params);
        $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error_message = "Error en la búsqueda: " . $e->getMessage();
    }
}

// Obtener estadísticas de contenido
$stats_sql = "
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL THEN 1 END) as with_tmdb,
        COUNT(CASE WHEN media_type = 'movie' THEN 1 END) as movies,
        COUNT(CASE WHEN media_type = 'tv' THEN 1 END) as series,
        COUNT(CASE WHEN media_type IN ('movie', 'tv') OR url REGEXP '/movie/' OR url REGEXP '/series/' THEN 1 END) as valid_content
    FROM m3u_content c
    JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
";

$stats = $pdo->query($stats_sql)->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Búsqueda TMDB Mejorada</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            margin-bottom: 0.5rem;
        }
        
        .search-container {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid #404040;
        }
        
        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 6px;
            color: white;
            font-size: 1rem;
        }
        
        .search-select {
            padding: 0.75rem;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 6px;
            color: white;
            min-width: 150px;
        }
        
        .search-btn {
            padding: 0.75rem 1.5rem;
            background: #46d347;
            color: #1a1a1a;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: #3bc73c;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-card {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #46d347;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9rem;
        }
        
        .results-container {
            background: #2d2d2d;
            border-radius: 12px;
            border: 1px solid #404040;
            overflow: hidden;
        }
        
        .results-header {
            background: #1a1a1a;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #404040;
        }
        
        .result-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #404040;
            transition: all 0.3s ease;
        }
        
        .result-item:hover {
            background: #333;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-poster {
            width: 60px;
            height: 90px;
            background: #404040;
            border-radius: 4px;
            margin-right: 1rem;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .result-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .result-info {
            flex: 1;
        }
        
        .result-title {
            font-weight: bold;
            color: #46d347;
            margin-bottom: 0.25rem;
        }
        
        .result-meta {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .result-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }
        
        .tag {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .tag-tmdb {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid #10b981;
        }
        
        .tag-movie {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }
        
        .tag-tv {
            background: rgba(168, 85, 247, 0.2);
            color: #a855f7;
            border: 1px solid #a855f7;
        }
        
        .tag-list {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #b0b0b0;
        }
        
        .search-tips {
            background: rgba(6, 182, 212, 0.1);
            border: 1px solid #06b6d4;
            color: #06b6d4;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
        }
        
        .search-tips h4 {
            margin-bottom: 0.5rem;
        }
        
        .search-tips ul {
            margin: 0;
            padding-left: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🔍 Búsqueda TMDB Mejorada</h1>
            <p>Busca contenido por título o TMDB ID con precisión mejorada</p>
        </div>
        
        <div class="search-container">
            <form method="GET" class="search-form">
                <input 
                    type="text" 
                    name="query" 
                    class="search-input" 
                    placeholder="Buscar por título o TMDB ID (ej: Gone Girl, 210577)..." 
                    value="<?php echo htmlspecialchars($search_query); ?>"
                    required
                >
                <select name="type" class="search-select">
                    <option value="all" <?php echo $search_type === 'all' ? 'selected' : ''; ?>>Todo</option>
                    <option value="valid" <?php echo $search_type === 'valid' ? 'selected' : ''; ?>>Solo Películas/Series</option>
                    <option value="movie" <?php echo $search_type === 'movie' ? 'selected' : ''; ?>>Solo Películas</option>
                    <option value="tv" <?php echo $search_type === 'tv' ? 'selected' : ''; ?>>Solo Series</option>
                </select>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i> Buscar
                </button>
            </form>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                    <div class="stat-label">Total Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['with_tmdb']); ?></div>
                    <div class="stat-label">Con TMDB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['movies']); ?></div>
                    <div class="stat-label">Películas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['series']); ?></div>
                    <div class="stat-label">Series</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total'] > 0 ? round(($stats['with_tmdb']/$stats['total'])*100, 1) : 0; ?>%</div>
                    <div class="stat-label">% con TMDB</div>
                </div>
            </div>
            
            <div class="search-tips">
                <h4>💡 Consejos de Búsqueda:</h4>
                <ul>
                    <li><strong>TMDB ID:</strong> Busca por ID numérico (ej: 210577) para resultados exactos</li>
                    <li><strong>Título:</strong> Busca por nombre de película/serie</li>
                    <li><strong>Filtros:</strong> Usa los filtros para limitar a películas o series</li>
                    <li><strong>Precisión:</strong> Los elementos con TMDB ID aparecen primero</li>
                </ul>
            </div>
        </div>
        
        <?php if (!empty($search_query)): ?>
        <div class="results-container">
            <div class="results-header">
                <h2>Resultados para: "<?php echo htmlspecialchars($search_query); ?>"</h2>
                <p><?php echo count($search_results); ?> resultados encontrados</p>
            </div>
            
            <?php if (empty($search_results)): ?>
            <div class="no-results">
                <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con términos diferentes o verifica la ortografía</p>
            </div>
            <?php else: ?>
            <?php foreach ($search_results as $result): ?>
            <div class="result-item">
                <div class="result-poster">
                    <?php if ($result['tmdb_poster_path']): ?>
                        <img src="https://image.tmdb.org/t/p/w200<?php echo $result['tmdb_poster_path']; ?>" 
                             alt="<?php echo htmlspecialchars($result['title']); ?>"
                             onerror="this.style.display='none'">
                    <?php elseif ($result['logo_url']): ?>
                        <img src="<?php echo htmlspecialchars($result['logo_url']); ?>" 
                             alt="<?php echo htmlspecialchars($result['title']); ?>"
                             onerror="this.style.display='none'">
                    <?php else: ?>
                        <i class="fas fa-film" style="color: #666; font-size: 1.5rem;"></i>
                    <?php endif; ?>
                </div>
                
                <div class="result-info">
                    <div class="result-title">
                        <?php echo htmlspecialchars($result['title']); ?>
                    </div>
                    
                    <?php if ($result['tmdb_title'] && $result['tmdb_title'] !== $result['title']): ?>
                    <div class="result-meta">
                        <strong>TMDB:</strong> <?php echo htmlspecialchars($result['tmdb_title']); ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="result-meta">
                        <strong>Lista:</strong> <?php echo htmlspecialchars($result['list_name']); ?>
                        <?php if ($result['year']): ?>
                        | <strong>Año:</strong> <?php echo $result['year']; ?>
                        <?php endif; ?>
                        <?php if ($result['group_title']): ?>
                        | <strong>Categoría:</strong> <?php echo htmlspecialchars($result['group_title']); ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="result-tags">
                        <?php if ($result['tmdb_id']): ?>
                        <span class="tag tag-tmdb">TMDB: <?php echo $result['tmdb_id']; ?></span>
                        <?php endif; ?>
                        
                        <?php if ($result['media_type'] === 'movie'): ?>
                        <span class="tag tag-movie">Película</span>
                        <?php elseif ($result['media_type'] === 'tv'): ?>
                        <span class="tag tag-tv">Serie</span>
                        <?php endif; ?>
                        
                        <span class="tag tag-list"><?php echo htmlspecialchars($result['list_name']); ?></span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
