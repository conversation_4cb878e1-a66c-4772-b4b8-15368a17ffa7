<?php
// Componente para mostrar aplicaciones en index2.php
require_once 'config.php';

// Obtener aplicaciones activas
try {
    $stmt = $pdo->prepare("
        SELECT sa.*, 
               (SELECT COUNT(*) FROM app_downloads ad WHERE ad.app_id = sa.id) as total_downloads
        FROM support_apps sa 
        WHERE sa.is_active = 1 AND (sa.status = 'active' OR sa.status IS NULL)
        ORDER BY sa.created_at DESC
    ");
    $stmt->execute();
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $apps = [];
}

// Función para registrar descarga
function registerDownload($app_id, $pdo) {
    try {
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = get_real_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $stmt = $pdo->prepare("INSERT INTO app_downloads (app_id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)");
        $stmt->execute([$app_id, $user_id, $ip_address, $user_agent]);
        
        // Actualizar contador en la app
        $stmt = $pdo->prepare("UPDATE support_apps SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$app_id]);
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Este componente ya no maneja descargas directamente
// Las descargas se procesan a través de download_app.php
?>

<style>
.apps-section {
    margin: 2rem 0;
}

.apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.app-card {
    background: var(--secondary-color, #1e293b);
    border-radius: 12px;
    border: 1px solid var(--border-color, #334155);
    overflow: hidden;
    transition: all 0.3s ease;
}

.app-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.6);
}

.app-header {
    padding: 1.5rem;
    background: var(--dark-bg, #0f172a);
    border-bottom: 1px solid var(--border-color, #334155);
}

.app-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary, #f8fafc);
    margin-bottom: 0.5rem;
}

.app-version {
    color: var(--text-secondary, #cbd5e1);
    font-size: 0.9rem;
}

.app-content {
    padding: 1.5rem;
}

.app-description {
    color: var(--text-secondary, #cbd5e1);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.app-features {
    margin-bottom: 1.5rem;
}

.app-features h4 {
    color: var(--text-primary, #f8fafc);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.app-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255,255,255,0.05);
    border-radius: 8px;
}

.app-stat {
    text-align: center;
}

.app-stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-color, #10b981);
}

.app-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary, #cbd5e1);
}

.app-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.app-download-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    background: var(--primary-color, #2563eb);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.app-download-btn:hover {
    background: var(--primary-dark, #1d4ed8);
    transform: translateY(-2px);
}

.app-external-btn {
    padding: 0.75rem 1rem;
    background: transparent;
    color: var(--text-secondary, #cbd5e1);
    text-decoration: none;
    border: 1px solid var(--border-color, #334155);
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.app-external-btn:hover {
    background: rgba(255,255,255,0.1);
    color: var(--text-primary, #f8fafc);
}

.platform-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
    display: inline-block;
}

.platform-android { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
.platform-ios { background: rgba(33, 150, 243, 0.2); color: #2196f3; }
.platform-windows { background: rgba(96, 125, 139, 0.2); color: #607d8b; }
.platform-mac { background: rgba(158, 158, 158, 0.2); color: #9e9e9e; }
.platform-linux { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
.platform-web { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }

.no-apps {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary, #cbd5e1);
}

.no-apps i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .apps-grid {
        grid-template-columns: 1fr;
    }
    
    .app-actions {
        flex-direction: column;
    }
}
</style>

<div class="apps-section">
    <h2 style="color: var(--text-primary, #f8fafc); margin-bottom: 1rem;">
        <i class="fas fa-mobile-alt"></i>
        Aplicaciones Disponibles
    </h2>
    
    <?php if (empty($apps)): ?>
    <div class="no-apps">
        <i class="fas fa-mobile-alt"></i>
        <p>No hay aplicaciones disponibles en este momento</p>
    </div>
    <?php else: ?>
    <div class="apps-grid">
        <?php foreach ($apps as $app): ?>
        <div class="app-card">
            <div class="app-header">
                <div class="app-title"><?php echo htmlspecialchars($app['name']); ?></div>
                <div class="app-version">Versión <?php echo htmlspecialchars($app['version']); ?></div>
            </div>
            
            <div class="app-content">
                <span class="platform-badge platform-<?php echo $app['platform']; ?>">
                    <?php echo ucfirst($app['platform']); ?>
                </span>
                
                <?php if ($app['description']): ?>
                <div class="app-description">
                    <?php echo nl2br(htmlspecialchars($app['description'])); ?>
                </div>
                <?php endif; ?>
                
                <?php if ($app['features']): ?>
                <div class="app-features">
                    <h4>Características:</h4>
                    <div class="features-list">
                        <?php 
                        $features = explode(',', $app['features']);
                        foreach ($features as $feature): 
                            $feature = trim($feature);
                            if (!empty($feature)):
                        ?>
                        <span class="feature-tag"><?php echo htmlspecialchars($feature); ?></span>
                        <?php 
                            endif;
                        endforeach; 
                        ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="app-stats">
                    <div class="app-stat">
                        <div class="app-stat-number"><?php echo number_format($app['total_downloads']); ?></div>
                        <div class="app-stat-label">Descargas</div>
                    </div>
                    <div class="app-stat">
                        <div class="app-stat-number"><?php echo date('d/m/Y', strtotime($app['created_at'])); ?></div>
                        <div class="app-stat-label">Publicado</div>
                    </div>
                </div>
                
                <div class="app-actions">
                    <?php if (!empty($app['file_path']) && file_exists($app['file_path'])): ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=download"
                       class="app-download-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'file')">
                        <i class="fas fa-download"></i>
                        Descargar APK
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($app['download_url'])): ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=store"
                       class="app-download-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'store')"
                       style="background: #10b981;">
                        <i class="fab fa-google-play"></i>
                        Play Store
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($app['external_url'])): ?>
                    <a href="download_app.php?id=<?php echo $app['id']; ?>&action=external"
                       class="app-external-btn"
                       onclick="trackDownload(<?php echo $app['id']; ?>, 'external')">
                        <i class="fas fa-external-link-alt"></i>
                        Sitio Web
                    </a>
                    <?php endif; ?>

                    <?php if (empty($app['file_path']) && empty($app['download_url']) && empty($app['external_url'])): ?>
                    <div style="color: var(--text-secondary); font-style: italic; text-align: center; padding: 1rem;">
                        <i class="fas fa-info-circle"></i>
                        Próximamente disponible
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</div>

<script>
function trackDownload(appId, type) {
    // Tracking de descargas
    if (typeof gtag !== 'undefined') {
        gtag('event', 'download', {
            'event_category': 'app',
            'event_label': 'app_' + appId + '_' + type,
            'value': 1
        });
    }

    // Mostrar mensaje de descarga iniciada
    showDownloadMessage(type);
}

function showDownloadMessage(type) {
    const messages = {
        'file': '📱 Descarga iniciada. El archivo APK se descargará automáticamente.',
        'store': '🏪 Redirigiendo a la tienda de aplicaciones...',
        'external': '🌐 Abriendo sitio web oficial...'
    };

    const message = messages[type] || '⬇️ Procesando descarga...';

    // Crear notificación temporal
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.4);
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-download"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 4000);
}

// Estilos para animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
