<?php
// Búsqueda mejorada con TMDB IDs
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función de búsqueda mejorada con TMDB
function enhancedSearch($query, $pdo) {
    $results = [];
    
    // 1. Búsqueda directa por TMDB ID si el query es numérico
    if (is_numeric($query)) {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name, 'tmdb_id_exact' as match_type, 100 as relevance
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 AND c.tmdb_id = ?
            ORDER BY c.title
        ");
        $stmt->execute([$query]);
        $results = array_merge($results, $stmt->fetchAll(PDO::FETCH_ASSOC));
    }
    
    // 2. Búsqueda por título TMDB (más precisa)
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 'tmdb_title' as match_type, 95 as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 AND (
            c.tmdb_title LIKE ? OR 
            c.tmdb_original_title LIKE ?
        )
        ORDER BY c.title
        LIMIT 200
    ");
    $search_term = '%' . $query . '%';
    $stmt->execute([$search_term, $search_term]);
    $results = array_merge($results, $stmt->fetchAll(PDO::FETCH_ASSOC));
    
    // 3. Búsqueda por título original (fallback)
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 'original_title' as match_type, 80 as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 AND c.title LIKE ?
        AND c.id NOT IN (
            SELECT DISTINCT c2.id FROM m3u_content c2 
            LEFT JOIN m3u_lists l2 ON c2.list_id = l2.id 
            WHERE l2.is_active = 1 AND (
                c2.tmdb_title LIKE ? OR 
                c2.tmdb_original_title LIKE ?
            )
        )
        ORDER BY c.title
        LIMIT 300
    ");
    $stmt->execute([$search_term, $search_term, $search_term]);
    $results = array_merge($results, $stmt->fetchAll(PDO::FETCH_ASSOC));
    
    // 4. Buscar contenido relacionado por TMDB ID si encontramos coincidencias exactas
    $tmdb_ids = [];
    foreach ($results as $result) {
        if ($result['tmdb_id'] && !in_array($result['tmdb_id'], $tmdb_ids)) {
            $tmdb_ids[] = $result['tmdb_id'];
        }
    }
    
    if (!empty($tmdb_ids)) {
        $placeholders = str_repeat('?,', count($tmdb_ids) - 1) . '?';
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name, 'related_tmdb' as match_type, 90 as relevance
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 AND c.tmdb_id IN ($placeholders)
            AND c.id NOT IN (
                SELECT DISTINCT id FROM m3u_content WHERE id IN (" . 
                implode(',', array_column($results, 'id')) . ")
            )
            ORDER BY c.title
        ");
        $stmt->execute($tmdb_ids);
        $related = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $results = array_merge($results, $related);
    }
    
    // Eliminar duplicados y ordenar por relevancia
    $unique_results = [];
    $seen_ids = [];
    
    foreach ($results as $result) {
        if (!in_array($result['id'], $seen_ids)) {
            $unique_results[] = $result;
            $seen_ids[] = $result['id'];
        }
    }
    
    // Ordenar por relevancia y luego por título
    usort($unique_results, function($a, $b) {
        if ($a['relevance'] == $b['relevance']) {
            return strcmp($a['title'], $b['title']);
        }
        return $b['relevance'] - $a['relevance'];
    });
    
    return $unique_results;
}

// Función para buscar en TMDB y obtener contenido relacionado
function searchTMDBAndFindLocal($query, $pdo) {
    $tmdb_results = searchTMDBContent($query);
    $local_matches = [];
    
    if ($tmdb_results && !empty($tmdb_results['results'])) {
        $tmdb_ids = array_column($tmdb_results['results'], 'id');
        
        if (!empty($tmdb_ids)) {
            $placeholders = str_repeat('?,', count($tmdb_ids) - 1) . '?';
            $stmt = $pdo->prepare("
                SELECT c.*, l.name as list_name, 'tmdb_search_match' as match_type, 85 as relevance
                FROM m3u_content c 
                LEFT JOIN m3u_lists l ON c.list_id = l.id 
                WHERE l.is_active = 1 AND c.tmdb_id IN ($placeholders)
                ORDER BY c.title
            ");
            $stmt->execute($tmdb_ids);
            $local_matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
    return [
        'tmdb_results' => $tmdb_results['results'] ?? [],
        'local_matches' => $local_matches
    ];
}

// Procesar búsqueda
$search_query = $_GET['q'] ?? '';
$search_results = [];
$tmdb_search = null;

if ($search_query) {
    // Búsqueda local mejorada
    $search_results = enhancedSearch($search_query, $pdo);
    
    // Búsqueda en TMDB para contenido no disponible
    $tmdb_search = searchTMDBAndFindLocal($search_query, $pdo);
}

// Obtener estadísticas
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_content,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb
    FROM m3u_content c
    LEFT JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
");
$stats = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Búsqueda Mejorada - RogsMediaTV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--accent-color), #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .search-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
        }

        .stats {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat {
            background: var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .results-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--accent-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }

        .result-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .result-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .result-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .meta-tag {
            background: var(--primary-color);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .match-type {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .match-tmdb_id_exact { background: rgba(40, 167, 69, 0.2); color: var(--success-color); }
        .match-tmdb_title { background: rgba(70, 211, 105, 0.2); color: #46d347; }
        .match-related_tmdb { background: rgba(23, 162, 184, 0.2); color: var(--info-color); }
        .match-original_title { background: rgba(255, 193, 7, 0.2); color: var(--warning-color); }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .back-link:hover {
            color: #3bc73c;
        }

        .tmdb-section {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .tmdb-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .tmdb-card {
            background: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .tmdb-poster {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }

        .no-poster {
            width: 100%;
            height: 200px;
            background: var(--border-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-search"></i> Búsqueda Mejorada con TMDB</h1>
            <p>Búsqueda inteligente usando TMDB IDs para resultados más precisos</p>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <input type="text" 
                       name="q" 
                       class="search-input" 
                       value="<?php echo htmlspecialchars($search_query); ?>" 
                       placeholder="Buscar por título, TMDB ID, o palabras clave..."
                       required>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    Buscar
                </button>
            </form>

            <div class="stats">
                <div class="stat">
                    <div class="stat-number"><?php echo number_format($stats['total_content']); ?></div>
                    <div class="stat-label">Total Contenido</div>
                </div>
                <div class="stat">
                    <div class="stat-number"><?php echo number_format($stats['with_tmdb']); ?></div>
                    <div class="stat-label">Con TMDB ID</div>
                </div>
                <div class="stat">
                    <div class="stat-number"><?php echo $stats['total_content'] > 0 ? round(($stats['with_tmdb']/$stats['total_content'])*100, 1) : 0; ?>%</div>
                    <div class="stat-label">Completado</div>
                </div>
            </div>
        </div>

        <?php if ($search_query): ?>
        <!-- Resultados locales -->
        <?php if (!empty($search_results)): ?>
        <div class="results-section">
            <h2 class="section-title">
                <i class="fas fa-tv"></i>
                Contenido Disponible (<?php echo count($search_results); ?> resultados)
            </h2>
            <div class="results-grid">
                <?php foreach ($search_results as $result): ?>
                <div class="result-card">
                    <div class="result-title"><?php echo htmlspecialchars($result['title']); ?></div>
                    <div class="result-meta">
                        <span class="meta-tag">
                            <i class="fas fa-list"></i>
                            <?php echo htmlspecialchars($result['list_name']); ?>
                        </span>
                        <?php if ($result['tmdb_id']): ?>
                        <span class="meta-tag">
                            <i class="fas fa-film"></i>
                            TMDB: <?php echo $result['tmdb_id']; ?>
                        </span>
                        <?php endif; ?>
                        <?php if ($result['media_type']): ?>
                        <span class="meta-tag">
                            <i class="fas fa-tag"></i>
                            <?php echo $result['media_type'] === 'movie' ? 'Película' : 'Serie'; ?>
                        </span>
                        <?php endif; ?>
                    </div>
                    <div class="match-type match-<?php echo $result['match_type']; ?>">
                        <?php 
                        $match_labels = [
                            'tmdb_id_exact' => 'Coincidencia exacta TMDB ID',
                            'tmdb_title' => 'Título TMDB',
                            'related_tmdb' => 'Contenido relacionado',
                            'original_title' => 'Título original'
                        ];
                        echo $match_labels[$result['match_type']] ?? $result['match_type'];
                        ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Resultados TMDB -->
        <?php if ($tmdb_search && !empty($tmdb_search['tmdb_results'])): ?>
        <div class="tmdb-section">
            <h2 class="section-title">
                <i class="fas fa-globe"></i>
                Resultados de TMDB (<?php echo count($tmdb_search['tmdb_results']); ?> encontrados)
            </h2>
            <div class="tmdb-grid">
                <?php foreach (array_slice($tmdb_search['tmdb_results'], 0, 12) as $tmdb_item): ?>
                <div class="tmdb-card">
                    <?php if ($tmdb_item['poster_path']): ?>
                    <img src="https://image.tmdb.org/t/p/w300<?php echo $tmdb_item['poster_path']; ?>" 
                         alt="<?php echo htmlspecialchars($tmdb_item['title'] ?? $tmdb_item['name']); ?>"
                         class="tmdb-poster">
                    <?php else: ?>
                    <div class="no-poster">
                        <i class="fas fa-image"></i>
                    </div>
                    <?php endif; ?>
                    <div class="result-title"><?php echo htmlspecialchars($tmdb_item['title'] ?? $tmdb_item['name']); ?></div>
                    <div class="result-meta">
                        <span class="meta-tag">ID: <?php echo $tmdb_item['id']; ?></span>
                        <span class="meta-tag"><?php echo $tmdb_item['media_type'] === 'movie' ? 'Película' : 'Serie'; ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($search_query && empty($search_results) && (!$tmdb_search || empty($tmdb_search['tmdb_results']))): ?>
        <div class="results-section">
            <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                <i class="fas fa-search-minus" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con términos diferentes o verifica la ortografía</p>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
