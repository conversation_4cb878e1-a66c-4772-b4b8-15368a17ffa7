<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Simple Upload - RGS</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --border-color: #334155;
            --border-radius: 12px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }

        .file-input {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            background: var(--dark-bg);
            transition: all 0.3s ease;
            cursor: pointer;
            user-select: none;
            position: relative;
            margin: 2rem 0;
        }

        .file-input:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
        }

        .file-input.dragover {
            border-color: var(--primary-color) !important;
            background: rgba(37, 99, 235, 0.15) !important;
            transform: scale(1.02);
        }

        .file-input input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            cursor: pointer;
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .file-input:hover .upload-icon {
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .upload-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .upload-info {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin: 0.5rem 0;
        }

        .upload-tip {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 0.75rem;
            margin-top: 1rem;
            font-size: 0.85rem;
            color: var(--accent-color);
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }

        .status.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--accent-color);
            color: var(--accent-color);
        }

        .status.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🧪 Test Simple de Upload</h1>
        
        <p style="text-align: center; color: var(--text-secondary); margin-bottom: 2rem;">
            Esta versión simplificada replica exactamente la funcionalidad de apps_admin.php
        </p>

        <div class="file-input" id="fileInputContainer">
            <input type="file" accept=".apk,.ipa,.exe,.dmg,.deb,.zip,.msi" id="appFileInput">
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">
                Haz clic para seleccionar el archivo o arrastra aquí
            </div>
            <div class="upload-info">
                📋 <strong>Formatos soportados:</strong> APK, IPA, EXE, DMG, DEB, ZIP, MSI
            </div>
            <div class="upload-info" style="color: var(--warning-color);">
                ⚠️ <strong>Tamaño máximo:</strong> 100MB
            </div>
            <div class="upload-tip">
                💡 <strong>Tip:</strong> Puedes arrastrar archivos directamente desde tu explorador
            </div>
        </div>

        <div id="status"></div>

        <div style="text-align: center;">
            <button class="btn" onclick="resetFileInput()">
                <i class="fas fa-redo"></i> Resetear
            </button>
            <button class="btn" onclick="testClick()">
                <i class="fas fa-mouse-pointer"></i> Test Click
            </button>
            <a href="apps_admin.php" class="btn">
                <i class="fas fa-arrow-left"></i> Volver a Apps Admin
            </a>
        </div>
    </div>

    <script>
        // Variable para evitar múltiples inicializaciones
        let fileInputInitialized = false;
        
        // Configurar input de archivo (misma función que apps_admin.php)
        function setupFileInput() {
            if (fileInputInitialized) return;
            
            const fileInput = document.querySelector('#appFileInput');
            const fileInputContainer = document.getElementById('fileInputContainer');
            
            if (fileInput && fileInputContainer) {
                // Hacer clic en el contenedor abre el diálogo de archivos
                fileInputContainer.addEventListener('click', function(e) {
                    if (!e.target.closest('button') && e.target.type !== 'file') {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Click detectado, abriendo diálogo...');
                        fileInput.click();
                    }
                });
                
                // Manejar cambio de archivo
                fileInput.addEventListener('change', handleFileSelect);
                
                // Configurar drag & drop
                setupDragAndDrop(fileInputContainer, fileInput);
                
                fileInputInitialized = true;
                console.log('File input inicializado correctamente');
            }
        }

        // Configurar drag & drop
        function setupDragAndDrop(container, fileInput) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, preventDefaults, false);
            });

            container.addEventListener('dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                container.classList.add('dragover');
            });

            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                container.classList.add('dragover');
            });

            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (!container.contains(e.relatedTarget)) {
                    container.classList.remove('dragover');
                }
            });

            container.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                container.classList.remove('dragover');
                
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(files[0]);
                        fileInput.files = dataTransfer.files;
                        
                        const changeEvent = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(changeEvent);
                    } catch (error) {
                        console.error('Error handling dropped file:', error);
                        alert('Error al procesar el archivo. Por favor, inténtalo de nuevo.');
                    }
                }
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (!file) return;

            const allowedTypes = ['apk', 'ipa', 'exe', 'dmg', 'deb', 'zip', 'msi'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            const maxSize = 100 * 1024 * 1024;

            const statusDiv = document.getElementById('status');
            
            if (!allowedTypes.includes(fileExtension)) {
                statusDiv.innerHTML = `
                    <div class="status error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Error:</strong> Tipo de archivo no permitido. Solo se permiten: ${allowedTypes.join(', ').toUpperCase()}
                    </div>
                `;
                e.target.value = '';
                return;
            }

            if (file.size > maxSize) {
                statusDiv.innerHTML = `
                    <div class="status error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Error:</strong> El archivo es demasiado grande. Tamaño máximo: 100MB
                    </div>
                `;
                e.target.value = '';
                return;
            }

            statusDiv.innerHTML = `
                <div class="status success">
                    <i class="fas fa-check-circle"></i>
                    <strong>¡Éxito!</strong> Archivo seleccionado correctamente
                    <br><br>
                    <strong>Nombre:</strong> ${file.name}<br>
                    <strong>Tamaño:</strong> ${formatBytes(file.size)}<br>
                    <strong>Tipo:</strong> ${fileExtension.toUpperCase()}
                </div>
            `;
        }

        function resetFileInput() {
            const fileInput = document.querySelector('#appFileInput');
            const statusDiv = document.getElementById('status');
            
            if (fileInput) {
                fileInput.value = '';
            }
            if (statusDiv) {
                statusDiv.innerHTML = '';
            }
            
            console.log('File input reseteado');
        }

        function testClick() {
            const fileInput = document.querySelector('#appFileInput');
            console.log('Test click manual...');
            fileInput.click();
        }

        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM cargado, inicializando...');
            setupFileInput();
        });
        
        // También inicializar inmediatamente por si el DOM ya está listo
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupFileInput);
        } else {
            console.log('DOM ya listo, inicializando inmediatamente...');
            setupFileInput();
        }
    </script>
</body>
</html>
