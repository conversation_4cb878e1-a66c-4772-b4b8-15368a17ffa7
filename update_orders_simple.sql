-- Script simple para agregar campos TMDB
-- Ejecutar línea por línea en phpMyAdmin si hay errores

-- Para tabla orders (ejecutar solo las que no den error)
ALTER TABLE orders ADD COLUMN media_type ENUM('movie', 'tv', 'person') NULL AFTER tmdb_id;
ALTER TABLE orders ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER media_type;
ALTER TABLE orders ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title;
ALTER TABLE orders ADD COLUMN tmdb_year INT NULL AFTER tmdb_original_title;
ALTER TABLE orders ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_year;
ALTER TABLE orders ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path;
ALTER TABLE orders ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview;

-- Para tabla m3u_content (ejecutar solo las que no den error)
ALTER TABLE m3u_content ADD COLUMN tmdb_id INT NULL AFTER year;
ALTER TABLE m3u_content ADD COLUMN tmdb_title VARCHAR(500) NULL AFTER tmdb_id;
ALTER TABLE m3u_content ADD COLUMN tmdb_original_title VARCHAR(500) NULL AFTER tmdb_title;
ALTER TABLE m3u_content ADD COLUMN tmdb_poster_path VARCHAR(500) NULL AFTER tmdb_original_title;
ALTER TABLE m3u_content ADD COLUMN tmdb_overview TEXT NULL AFTER tmdb_poster_path;
ALTER TABLE m3u_content ADD COLUMN tmdb_language VARCHAR(10) NULL AFTER tmdb_overview;
ALTER TABLE m3u_content ADD COLUMN tmdb_vote_average DECIMAL(3,1) NULL AFTER tmdb_language;
ALTER TABLE m3u_content ADD COLUMN tmdb_release_date DATE NULL AFTER tmdb_vote_average;

-- Agregar índices (ejecutar solo si las columnas existen)
ALTER TABLE orders ADD INDEX idx_tmdb_id (tmdb_id);
ALTER TABLE orders ADD INDEX idx_media_type (media_type);
ALTER TABLE orders ADD INDEX idx_tmdb_year (tmdb_year);

ALTER TABLE m3u_content ADD INDEX idx_content_tmdb_id (tmdb_id);
ALTER TABLE m3u_content ADD INDEX idx_content_tmdb_title (tmdb_title);
ALTER TABLE m3u_content ADD INDEX idx_content_tmdb_year (tmdb_release_date);

-- Crear tablas nuevas
CREATE TABLE IF NOT EXISTS tmdb_similar_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tmdb_id INT NOT NULL,
    similar_tmdb_id INT NOT NULL,
    similarity_score DECIMAL(5,2) DEFAULT 0.00,
    media_type ENUM('movie', 'tv') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tmdb_mapping (tmdb_id),
    INDEX idx_similar_mapping (similar_tmdb_id),
    UNIQUE KEY unique_mapping (tmdb_id, similar_tmdb_id)
);

CREATE TABLE IF NOT EXISTS tmdb_search_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_query VARCHAR(500) NOT NULL,
    search_hash VARCHAR(64) NOT NULL,
    tmdb_response JSON,
    language VARCHAR(10) DEFAULT 'es-MX',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
    INDEX idx_search_hash (search_hash),
    INDEX idx_expires (expires_at)
);
