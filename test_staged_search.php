<?php
// Test de búsqueda por etapas para verificar calidad de resultados
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Simular búsqueda por etapas para "Perdida"
$search_terms = ['Perdida', 'Gone Girl'];
$stages = [];

// Etapa 1: Búsqueda por títulos TMDB
foreach ($search_terms as $term) {
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 'tmdb_title' as match_type, 95 as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 AND (
            c.tmdb_title LIKE ? OR 
            c.tmdb_original_title LIKE ?
        )
        ORDER BY c.title
        LIMIT 10
    ");
    $search_term = "%$term%";
    $stmt->execute([$search_term, $search_term]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stages["Etapa 1: TMDB Títulos ($term)"] = $results;
}

// Etapa 2: Búsqueda por título original
foreach ($search_terms as $term) {
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 'original_title' as match_type, 80 as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 AND c.title LIKE ?
        ORDER BY 
            CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
            c.title
        LIMIT 10
    ");
    $search_term = "%$term%";
    $stmt->execute([$search_term]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stages["Etapa 2: Título Original ($term)"] = $results;
}

// Etapa 3: Búsqueda por clean_title
foreach ($search_terms as $term) {
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 'clean_title' as match_type, 70 as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 AND c.clean_title LIKE ?
        ORDER BY 
            CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
            c.title
        LIMIT 10
    ");
    $search_term = "%$term%";
    $stmt->execute([$search_term]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stages["Etapa 3: Clean Title ($term)"] = $results;
}

// Búsqueda directa por TMDB ID
$stmt = $pdo->prepare("
    SELECT c.*, l.name as list_name, 'tmdb_id_exact' as match_type, 100 as relevance
    FROM m3u_content c 
    LEFT JOIN m3u_lists l ON c.list_id = l.id 
    WHERE l.is_active = 1 AND c.tmdb_id = 210577
    ORDER BY c.title
");
$stmt->execute();
$tmdb_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stages["Búsqueda por TMDB ID (210577)"] = $tmdb_results;
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Test Búsqueda por Etapas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .stage-section {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 1.5rem;
        }
        
        .stage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #404040;
        }
        
        .stage-title {
            color: #46d347;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .result-count {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .result-count.excellent {
            background: #10b981;
        }
        
        .result-count.good {
            background: #3b82f6;
        }
        
        .result-count.poor {
            background: #f59e0b;
        }
        
        .result-count.none {
            background: #ef4444;
        }
        
        .result-item {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #46d347;
            margin-bottom: 0.5rem;
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: center;
        }
        
        .result-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .result-title {
            color: white;
            font-weight: bold;
        }
        
        .result-meta {
            color: #888;
            font-size: 0.9rem;
        }
        
        .result-badges {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .badge.tmdb {
            background: #01b4e4;
            color: white;
        }
        
        .badge.relevance {
            background: #10b981;
            color: white;
        }
        
        .badge.year {
            background: #f59e0b;
            color: white;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .summary {
            background: #0f1419;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #46d347;
            margin-top: 2rem;
        }
        
        .summary h3 {
            color: #46d347;
            margin-bottom: 1rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .test-button {
            background: #46d347;
            color: white;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #3bc55a;
        }
        
        .test-button.primary {
            background: #3b82f6;
        }
        
        .test-button.primary:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🔍 Test de Búsqueda por Etapas</h1>
            <p>Análisis de calidad de resultados para "Perdida" / "Gone Girl"</p>
        </div>
        
        <?php foreach ($stages as $stage_name => $results): ?>
        <div class="stage-section">
            <div class="stage-header">
                <div class="stage-title"><?php echo htmlspecialchars($stage_name); ?></div>
                <div class="result-count <?php 
                    $count = count($results);
                    if ($count >= 5) echo 'excellent';
                    elseif ($count >= 2) echo 'good';
                    elseif ($count >= 1) echo 'poor';
                    else echo 'none';
                ?>">
                    <?php echo $count; ?> resultado(s)
                </div>
            </div>
            
            <?php if (empty($results)): ?>
            <div style="color: #888; text-align: center; padding: 1rem;">
                No se encontraron coincidencias en esta etapa
            </div>
            <?php else: ?>
            <?php foreach (array_slice($results, 0, 5) as $result): ?>
            <div class="result-item">
                <div class="result-info">
                    <div class="result-title"><?php echo htmlspecialchars($result['title']); ?></div>
                    <div class="result-meta">
                        Lista: <?php echo htmlspecialchars($result['list_name']); ?> | 
                        Año: <?php echo htmlspecialchars($result['year'] ?? 'N/A'); ?>
                    </div>
                </div>
                <div class="result-badges">
                    <?php if (isset($result['tmdb_id']) && $result['tmdb_id']): ?>
                    <div class="badge tmdb">TMDB: <?php echo $result['tmdb_id']; ?></div>
                    <?php endif; ?>
                    <?php if (isset($result['relevance'])): ?>
                    <div class="badge relevance"><?php echo $result['relevance']; ?>%</div>
                    <?php endif; ?>
                    <?php if (isset($result['year']) && $result['year']): ?>
                    <div class="badge year"><?php echo $result['year']; ?></div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
            <?php if (count($results) > 5): ?>
            <div style="color: #888; text-align: center; margin-top: 0.5rem;">
                ... y <?php echo count($results) - 5; ?> resultado(s) más
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
        
        <div class="summary">
            <h3>📊 Análisis de Calidad</h3>
            <?php
            $total_results = 0;
            $best_stage = '';
            $best_count = 0;
            $tmdb_results_count = 0;
            
            foreach ($stages as $stage_name => $results) {
                $count = count($results);
                $total_results += $count;
                
                if ($count > $best_count) {
                    $best_count = $count;
                    $best_stage = $stage_name;
                }
                
                foreach ($results as $result) {
                    if (isset($result['tmdb_id']) && $result['tmdb_id']) {
                        $tmdb_results_count++;
                    }
                }
            }
            ?>
            <ul style="color: #cbd5e1; line-height: 1.6;">
                <li><strong>Total de coincidencias:</strong> <?php echo $total_results; ?></li>
                <li><strong>Con TMDB ID:</strong> <?php echo $tmdb_results_count; ?> (<?php echo $total_results > 0 ? round(($tmdb_results_count/$total_results)*100, 1) : 0; ?>%)</li>
                <li><strong>Mejor etapa:</strong> <?php echo htmlspecialchars($best_stage); ?> (<?php echo $best_count; ?> resultados)</li>
                <li><strong>Calidad esperada:</strong> 
                    <?php if ($tmdb_results_count >= 5): ?>
                        ✅ Excelente - Muchos resultados con TMDB ID
                    <?php elseif ($tmdb_results_count >= 2): ?>
                        ✅ Buena - Algunos resultados con TMDB ID
                    <?php elseif ($total_results >= 5): ?>
                        ⚠️ Regular - Muchos resultados pero pocos con TMDB ID
                    <?php else: ?>
                        ❌ Pobre - Pocos resultados relevantes
                    <?php endif; ?>
                </li>
            </ul>
        </div>
        
        <div class="test-buttons">
            <a href="m3u_search_tmdb.php?q=Perdida&auto_search=1" target="_blank" class="test-button primary">
                🔍 Probar Búsqueda Mejorada: "Perdida"
            </a>
            <a href="m3u_search_enhanced.php?q=Perdida" target="_blank" class="test-button">
                📊 Comparar con Enhanced: "Perdida"
            </a>
            <a href="m3u_search_tmdb.php?q=210577&auto_search=1" target="_blank" class="test-button">
                🎯 Probar por TMDB ID: 210577
            </a>
        </div>
    </div>
</body>
</html>
