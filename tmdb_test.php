<?php
// Página de prueba para verificar configuración TMDB
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Incluir configuración TMDB
require_once 'tmdb_config.php';

echo "<h1>🔍 Test de Configuración TMDB</h1>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }</style>";

echo "<h2>📋 Información de Configuración:</h2>";
echo "<ul>";
echo "<li><strong>TMDB_API_KEY definida:</strong> " . (defined('TMDB_API_KEY') ? '✅ SÍ' : '❌ NO') . "</li>";
echo "<li><strong>TMDB_LANGUAGE definida:</strong> " . (defined('TMDB_LANGUAGE') ? '✅ SÍ' : '❌ NO') . "</li>";
echo "<li><strong>TMDB_BASE_URL definida:</strong> " . (defined('TMDB_BASE_URL') ? '✅ SÍ' : '❌ NO') . "</li>";
echo "<li><strong>TMDB_IMAGE_BASE_URL definida:</strong> " . (defined('TMDB_IMAGE_BASE_URL') ? '✅ SÍ' : '❌ NO') . "</li>";
echo "</ul>";

if (defined('TMDB_LANGUAGE')) {
    echo "<h3>🌍 Idioma Configurado: <span style='color: #10b981;'>" . TMDB_LANGUAGE . "</span></h3>";
}

if (defined('TMDB_API_KEY')) {
    echo "<h3>🔑 API Key: <span style='color: #10b981;'>" . substr(TMDB_API_KEY, 0, 8) . "..." . "</span></h3>";
}

echo "<h2>🧪 Prueba de Búsqueda:</h2>";

if (isTMDBConfigured()) {
    echo "<p style='color: #10b981;'>✅ TMDB está configurado correctamente</p>";
    
    // Hacer una búsqueda de prueba
    $test_query = "Breaking Bad";
    echo "<h3>Buscando: '$test_query'</h3>";
    
    $search_result = searchTMDBContent($test_query);
    
    if ($search_result) {
        echo "<p style='color: #10b981;'>✅ Búsqueda exitosa</p>";
        echo "<h4>Resultados encontrados: " . count($search_result['results']) . "</h4>";
        
        if (!empty($search_result['results'])) {
            echo "<h4>Primer resultado:</h4>";
            $first = $search_result['results'][0];
            echo "<ul>";
            echo "<li><strong>Título:</strong> " . ($first['title'] ?? $first['name'] ?? 'N/A') . "</li>";
            echo "<li><strong>Título original:</strong> " . ($first['original_title'] ?? $first['original_name'] ?? 'N/A') . "</li>";
            echo "<li><strong>Idioma original:</strong> " . ($first['original_language'] ?? 'N/A') . "</li>";
            echo "<li><strong>Fecha:</strong> " . ($first['release_date'] ?? $first['first_air_date'] ?? 'N/A') . "</li>";
            echo "<li><strong>Descripción:</strong> " . substr($first['overview'] ?? 'N/A', 0, 200) . "...</li>";
            echo "</ul>";
        }
        
        // Mostrar URL de la petición
        $test_url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=" . TMDB_LANGUAGE . "&query=" . urlencode($test_query);
        echo "<h4>URL de la petición:</h4>";
        echo "<code style='background: #333; padding: 10px; display: block; word-break: break-all;'>";
        echo htmlspecialchars($test_url);
        echo "</code>";
        
    } else {
        echo "<p style='color: #ef4444;'>❌ Error en la búsqueda</p>";
    }
    
} else {
    echo "<p style='color: #ef4444;'>❌ TMDB no está configurado</p>";
}

echo "<br><br>";
echo "<a href='m3u_search_tmdb.php' style='color: #10b981;'>← Volver al buscador</a> | ";
echo "<a href='admin.php' style='color: #10b981;'>← Volver al admin</a>";
?>
