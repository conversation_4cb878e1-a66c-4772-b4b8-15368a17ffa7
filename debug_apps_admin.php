<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Debug Apps Admin</h1>";

try {
    // 1. Verificar directorios
    echo "<h2>📁 Verificando directorios...</h2>";
    
    $apploader_dir = __DIR__ . '/apploader';
    $uploads_dir = __DIR__ . '/uploads/apps';
    
    if (is_dir($apploader_dir)) {
        $success_messages[] = "✅ Directorio apploader existe: $apploader_dir";
        if (is_writable($apploader_dir)) {
            $success_messages[] = "✅ Directorio apploader es escribible";
        } else {
            $error_messages[] = "❌ Directorio apploader no es escribible";
        }
    } else {
        $error_messages[] = "❌ Directorio apploader no existe";
    }
    
    if (is_dir($uploads_dir)) {
        $success_messages[] = "✅ Directorio uploads/apps existe: $uploads_dir";
    } else {
        $error_messages[] = "❌ Directorio uploads/apps no existe";
    }
    
    // 2. Verificar archivos en apploader
    echo "<h2>📱 Verificando archivos APK...</h2>";
    
    $apk_files = glob($apploader_dir . '/*.apk');
    $success_messages[] = "ℹ️ Archivos APK encontrados: " . count($apk_files);
    
    foreach ($apk_files as $apk) {
        $success_messages[] = "📱 " . basename($apk) . " (" . formatBytes(filesize($apk)) . ")";
    }
    
    // 3. Verificar tabla support_apps
    echo "<h2>🗄️ Verificando base de datos...</h2>";
    
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'support_apps'");
        if ($stmt->rowCount() > 0) {
            $success_messages[] = "✅ Tabla support_apps existe";
            
            $stmt = $pdo->query("SELECT COUNT(*) FROM support_apps");
            $app_count = $stmt->fetchColumn();
            $success_messages[] = "ℹ️ Aplicaciones registradas: $app_count";
            
            // Mostrar algunas aplicaciones
            $stmt = $pdo->query("SELECT name, version, platform, status FROM support_apps LIMIT 5");
            $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($apps as $app) {
                $success_messages[] = "📱 {$app['name']} v{$app['version']} ({$app['platform']}) - {$app['status']}";
            }
            
        } else {
            $error_messages[] = "❌ Tabla support_apps no existe";
        }
    } catch (Exception $e) {
        $error_messages[] = "❌ Error verificando tabla: " . $e->getMessage();
    }
    
    // 4. Verificar configuración PHP
    echo "<h2>⚙️ Verificando configuración PHP...</h2>";
    
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $file_uploads = ini_get('file_uploads');
    $max_execution_time = ini_get('max_execution_time');
    
    if ($file_uploads) {
        $success_messages[] = "✅ Subida de archivos habilitada";
    } else {
        $error_messages[] = "❌ Subida de archivos deshabilitada";
    }
    
    $success_messages[] = "ℹ️ upload_max_filesize: $upload_max";
    $success_messages[] = "ℹ️ post_max_size: $post_max";
    $success_messages[] = "ℹ️ max_execution_time: $max_execution_time segundos";
    
    // 5. Verificar permisos de archivos
    echo "<h2>🔐 Verificando permisos...</h2>";
    
    $apps_admin_file = __DIR__ . '/apps_admin.php';
    if (file_exists($apps_admin_file)) {
        $success_messages[] = "✅ apps_admin.php existe";
        $perms = substr(sprintf('%o', fileperms($apps_admin_file)), -4);
        $success_messages[] = "ℹ️ Permisos apps_admin.php: $perms";
    } else {
        $error_messages[] = "❌ apps_admin.php no existe";
    }
    
} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Apps Admin - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
        .test-section {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .file-input-test {
            border: 2px dashed #334155;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #1e293b;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-input-test:hover {
            border-color: #2563eb;
            background: rgba(37, 99, 235, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Apps Admin Completado</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Estado Correcto</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Problemas Encontrados</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="test-section">
            <h3>🧪 Test de Funcionalidad</h3>
            <p>Haz clic en el área de abajo para probar el diálogo de archivos:</p>
            <div class="file-input-test" onclick="testFileDialog()">
                <i class="fas fa-upload" style="font-size: 2rem; margin-bottom: 1rem; color: #cbd5e1;"></i>
                <p><strong>Hacer clic para probar diálogo de archivos</strong></p>
                <p style="font-size: 0.9rem; color: #cbd5e1;">Esto debería abrir el selector de archivos una sola vez</p>
            </div>
            <input type="file" id="testFileInput" style="display: none;" accept=".apk,.ipa,.exe,.dmg,.deb,.zip,.msi">
        </div>
        
        <div class="actions">
            <a href="apps_admin.php" class="btn">📱 Apps Admin</a>
            <a href="setup_apploader.php" class="btn">🔧 Setup Apploader</a>
            <a href="test_file_upload.html" class="btn">🧪 Test Upload</a>
            <a href="admin2.php" class="btn">🏠 Dashboard</a>
        </div>
        
        <?php if (count($error_messages) == 0): ?>
        <div style="background: rgba(16, 185, 129, 0.1); border: 2px solid #10b981; border-radius: 12px; padding: 2rem; margin: 2rem 0; text-align: center;">
            <h2 style="color: #10b981; margin-bottom: 1rem;">🎉 ¡Sistema Funcionando!</h2>
            <p style="color: #10b981; font-size: 1.1rem;">
                El sistema de aplicaciones está configurado correctamente y listo para usar.
            </p>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        function testFileDialog() {
            const fileInput = document.getElementById('testFileInput');
            fileInput.click();
        }
        
        document.getElementById('testFileInput').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                alert('✅ ¡Archivo seleccionado correctamente! Nombre: ' + e.target.files[0].name);
            }
        });
    </script>
</body>
</html>
