<?php
session_start();
require_once 'config.php';

// Solo permitir acceso a administradores
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$success_messages = [];
$error_messages = [];

echo "<h1>🔧 Reparación Simple de Base de Datos</h1>";

try {
    // 1. Verificar y corregir tabla users
    echo "<h2>👥 Verificando tabla users...</h2>";
    try {
        // Verificar si la tabla existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla users desde cero
            $pdo->exec("
                CREATE TABLE users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100),
                    password_hash VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $success_messages[] = "✅ Tabla users creada";
        } else {
            // Verificar si tiene password_hash
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password_hash'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE users ADD COLUMN password_hash VARCHAR(255) AFTER email");
                $success_messages[] = "✅ Columna password_hash agregada a users";
            }
        }
        
        // Insertar usuarios de ejemplo
        $stmt = $pdo->prepare("INSERT IGNORE INTO users (id, username, email, password_hash) VALUES (?, ?, ?, ?)");
        $stmt->execute([1, 'usuario', '<EMAIL>', 'demo_hash']);
        $stmt->execute([2, 'admin', '<EMAIL>', 'admin_hash']);
        $success_messages[] = "✅ Usuarios de ejemplo verificados";
        
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con users: " . $e->getMessage();
    }

    // 2. Verificar tabla support_tickets
    echo "<h2>🎫 Verificando tabla support_tickets...</h2>";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM support_tickets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Verificar si tiene title o subject
        if (!in_array('title', $columns) && !in_array('subject', $columns)) {
            $pdo->exec("ALTER TABLE support_tickets ADD COLUMN title VARCHAR(255) NOT NULL AFTER user_id");
            $success_messages[] = "✅ Columna title agregada a support_tickets";
        }
        
        $success_messages[] = "✅ Tabla support_tickets verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con support_tickets: " . $e->getMessage();
    }

    // 3. Crear tabla admin_notifications
    echo "<h2>🔔 Creando tabla admin_notifications...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type ENUM('ticket', 'chat', 'channel_request', 'app_download', 'system') NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                reference_id INT NULL,
                reference_type VARCHAR(50) NULL,
                is_read BOOLEAN DEFAULT FALSE,
                admin_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_type (type),
                INDEX idx_is_read (is_read),
                INDEX idx_created_at (created_at)
            )
        ");
        $success_messages[] = "✅ Tabla admin_notifications creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando admin_notifications: " . $e->getMessage();
    }

    // 4. Crear tabla chat_status
    echo "<h2>💬 Creando tabla chat_status...</h2>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS chat_status (
                id INT AUTO_INCREMENT PRIMARY KEY,
                session_id INT NOT NULL,
                admin_online BOOLEAN DEFAULT FALSE,
                user_online BOOLEAN DEFAULT FALSE,
                last_admin_activity TIMESTAMP NULL,
                last_user_activity TIMESTAMP NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_session (session_id)
            )
        ");
        $success_messages[] = "✅ Tabla chat_status creada/verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error creando chat_status: " . $e->getMessage();
    }

    // 5. Verificar y corregir tabla support_apps
    echo "<h2>📱 Verificando tabla support_apps...</h2>";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM support_apps");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $columns_to_add = [
            'features' => "ALTER TABLE support_apps ADD COLUMN features TEXT AFTER description",
            'download_url' => "ALTER TABLE support_apps ADD COLUMN download_url VARCHAR(500) AFTER file_size",
            'external_url' => "ALTER TABLE support_apps ADD COLUMN external_url VARCHAR(500) AFTER download_url",
            'status' => "ALTER TABLE support_apps ADD COLUMN status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active' AFTER is_active"
        ];
        
        foreach ($columns_to_add as $column => $sql) {
            if (!in_array($column, $columns)) {
                $pdo->exec($sql);
                $success_messages[] = "✅ Columna '$column' agregada a support_apps";
            }
        }
        
        $success_messages[] = "✅ Tabla support_apps verificada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error con support_apps: " . $e->getMessage();
    }

    // 6. Insertar datos de ejemplo básicos
    echo "<h2>📊 Insertando datos de ejemplo...</h2>";
    
    // Tickets de ejemplo (usando la columna correcta)
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM support_tickets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $title_column = in_array('title', $columns) ? 'title' : 'subject';
        
        $stmt = $pdo->prepare("INSERT IGNORE INTO support_tickets (id, user_id, $title_column, description, priority, category, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([6, 1, 'Problema con la reproducción', 'Los canales se cortan constantemente', 'high', 'technical', 'open']);
        
        $success_messages[] = "✅ Ticket de ejemplo insertado";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando ticket: " . $e->getMessage();
    }
    
    // Aplicación de ejemplo
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO support_apps (name, description, version, platform, file_path, file_size, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['IPTV Player Demo', 'Aplicación de demostración', '1.0.0', 'android', 'uploads/apps/demo.apk', 1024000, 1]);
        
        $success_messages[] = "✅ App de ejemplo insertada";
    } catch (Exception $e) {
        $error_messages[] = "❌ Error insertando app: " . $e->getMessage();
    }

    // 7. Verificar integridad final
    echo "<h2>🔍 Verificación final...</h2>";
    $tables_to_check = ['users', 'support_tickets', 'support_apps', 'admin_notifications', 'chat_status'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $success_messages[] = "✅ Tabla '$table': $count registros";
        } catch (Exception $e) {
            $error_messages[] = "❌ Error verificando '$table': " . $e->getMessage();
        }
    }

} catch (Exception $e) {
    $error_messages[] = "❌ Error general: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Reparación Simple - RGS Support</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            color: #f8fafc;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #334155;
        }
        h1 {
            color: #10b981;
            text-align: center;
            margin-bottom: 2rem;
        }
        h2 {
            color: #3b82f6;
            border-bottom: 2px solid #334155;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-color: #10b981;
            color: #10b981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
            color: #ef4444;
        }
        .actions {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #334155;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        .summary {
            background: #0f172a;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Reparación Simple Completada</h1>
        
        <div class="summary">
            <h3>📊 Resumen</h3>
            <p><strong>✅ Éxitos:</strong> <?php echo count($success_messages); ?></p>
            <p><strong>❌ Errores:</strong> <?php echo count($error_messages); ?></p>
        </div>
        
        <?php if (!empty($success_messages)): ?>
        <h2>✅ Operaciones Exitosas</h2>
        <?php foreach ($success_messages as $message): ?>
        <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
        <h2>❌ Errores</h2>
        <?php foreach ($error_messages as $message): ?>
        <div class="message error"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="actions">
            <a href="apps_admin.php" class="btn">📱 Probar Apps</a>
            <a href="ticket_detail.php?id=6" class="btn">🎫 Probar Ticket</a>
            <a href="admin2.php" class="btn">🏠 Panel Admin</a>
        </div>
    </div>
</body>
</html>
