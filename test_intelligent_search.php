<?php
// Test de búsqueda inteligente para verificar que funciona correctamente
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener el pedido "Perdida" para pruebas
$stmt = $pdo->prepare("
    SELECT * FROM orders 
    WHERE title = 'Perdida' OR tmdb_original_title = 'Gone Girl'
    ORDER BY created_at DESC 
    LIMIT 1
");
$stmt->execute();
$order = $stmt->fetch(PDO::FETCH_ASSOC);

// Simular búsqueda inteligente
$search_terms = [];
$results_by_method = [];

if ($order) {
    // Método 1: Búsqueda por TMDB ID
    if ($order['tmdb_id']) {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 AND c.tmdb_id = ?
            LIMIT 10
        ");
        $stmt->execute([$order['tmdb_id']]);
        $results_by_method['tmdb_id'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Método 2: Búsqueda por título español
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 
        AND (c.title LIKE ? OR c.clean_title LIKE ? OR c.tmdb_title LIKE ?)
        LIMIT 10
    ");
    $spanish_term = "%{$order['title']}%";
    $stmt->execute([$spanish_term, $spanish_term, $spanish_term]);
    $results_by_method['spanish_title'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Método 3: Búsqueda por título original
    if ($order['tmdb_original_title']) {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 
            AND (c.title LIKE ? OR c.clean_title LIKE ? OR c.tmdb_title LIKE ?)
            LIMIT 10
        ");
        $original_term = "%{$order['tmdb_original_title']}%";
        $stmt->execute([$original_term, $original_term, $original_term]);
        $results_by_method['original_title'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Método 4: Búsqueda por año
    if ($order['year']) {
        $stmt = $pdo->prepare("
            SELECT c.*, l.name as list_name
            FROM m3u_content c 
            LEFT JOIN m3u_lists l ON c.list_id = l.id 
            WHERE l.is_active = 1 
            AND c.year = ?
            AND (c.title LIKE ? OR c.title LIKE ?)
            LIMIT 10
        ");
        $stmt->execute([$order['year'], "%{$order['title']}%", "%{$order['tmdb_original_title']}%"]);
        $results_by_method['by_year'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Test Búsqueda Inteligente</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .order-info {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 2rem;
        }
        
        .method-section {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 1.5rem;
        }
        
        .method-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #404040;
        }
        
        .method-title {
            color: #46d347;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .result-count {
            background: #3b82f6;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .result-count.success {
            background: #10b981;
        }
        
        .result-count.warning {
            background: #f59e0b;
        }
        
        .result-item {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #46d347;
            margin-bottom: 0.5rem;
        }
        
        .result-title {
            color: white;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .result-meta {
            color: #888;
            font-size: 0.9rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .test-button {
            background: #46d347;
            color: white;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #3bc55a;
        }
        
        .test-button.secondary {
            background: #3b82f6;
        }
        
        .test-button.secondary:hover {
            background: #2563eb;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .summary {
            background: #0f1419;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #46d347;
            margin-top: 2rem;
        }
        
        .summary h3 {
            color: #46d347;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🧠 Test de Búsqueda Inteligente</h1>
            <p>Verificación de métodos de búsqueda para "Perdida" / "Gone Girl"</p>
        </div>
        
        <?php if ($order): ?>
        <div class="order-info">
            <h3 style="color: #46d347; margin-bottom: 1rem;">📋 Información del Pedido</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div><strong>Título:</strong> <?php echo htmlspecialchars($order['title']); ?></div>
                <div><strong>Título TMDB:</strong> <?php echo htmlspecialchars($order['tmdb_title'] ?? 'N/A'); ?></div>
                <div><strong>Título Original:</strong> <?php echo htmlspecialchars($order['tmdb_original_title'] ?? 'N/A'); ?></div>
                <div><strong>TMDB ID:</strong> <?php echo htmlspecialchars($order['tmdb_id'] ?? 'N/A'); ?></div>
                <div><strong>Año:</strong> <?php echo htmlspecialchars($order['year'] ?? 'N/A'); ?></div>
                <div><strong>Tipo:</strong> <?php echo htmlspecialchars($order['media_type'] ?? 'N/A'); ?></div>
            </div>
        </div>
        
        <?php foreach ($results_by_method as $method => $results): ?>
        <div class="method-section">
            <div class="method-header">
                <div class="method-title">
                    <?php
                    $method_names = [
                        'tmdb_id' => '🎯 Búsqueda por TMDB ID (' . ($order['tmdb_id'] ?? 'N/A') . ')',
                        'spanish_title' => '🇪🇸 Búsqueda por Título Español ("' . $order['title'] . '")',
                        'original_title' => '🇺🇸 Búsqueda por Título Original ("' . ($order['tmdb_original_title'] ?? 'N/A') . '")',
                        'by_year' => '📅 Búsqueda por Año (' . ($order['year'] ?? 'N/A') . ')'
                    ];
                    echo $method_names[$method] ?? $method;
                    ?>
                </div>
                <div class="result-count <?php echo count($results) > 0 ? 'success' : 'warning'; ?>">
                    <?php echo count($results); ?> resultado(s)
                </div>
            </div>
            
            <?php if (empty($results)): ?>
            <div style="color: #888; text-align: center; padding: 1rem;">
                No se encontraron coincidencias con este método
            </div>
            <?php else: ?>
            <?php foreach (array_slice($results, 0, 3) as $result): ?>
            <div class="result-item">
                <div class="result-title"><?php echo htmlspecialchars($result['title']); ?></div>
                <div class="result-meta">
                    Lista: <?php echo htmlspecialchars($result['list_name']); ?> | 
                    Año: <?php echo htmlspecialchars($result['year'] ?? 'N/A'); ?> |
                    TMDB: <?php echo htmlspecialchars($result['tmdb_id'] ?? 'N/A'); ?>
                </div>
            </div>
            <?php endforeach; ?>
            <?php if (count($results) > 3): ?>
            <div style="color: #888; text-align: center; margin-top: 0.5rem;">
                ... y <?php echo count($results) - 3; ?> resultado(s) más
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
        
        <div class="summary">
            <h3>📊 Resumen de Resultados</h3>
            <ul style="color: #cbd5e1; line-height: 1.6;">
                <?php
                $total_results = array_sum(array_map('count', $results_by_method));
                $best_method = '';
                $best_count = 0;
                foreach ($results_by_method as $method => $results) {
                    if (count($results) > $best_count) {
                        $best_count = count($results);
                        $best_method = $method;
                    }
                }
                ?>
                <li><strong>Total de coincidencias encontradas:</strong> <?php echo $total_results; ?></li>
                <li><strong>Mejor método:</strong> <?php echo $best_method; ?> (<?php echo $best_count; ?> resultados)</li>
                <li><strong>Recomendación:</strong> 
                    <?php if ($best_count > 0): ?>
                        ✅ La búsqueda inteligente debería encontrar coincidencias
                    <?php else: ?>
                        ⚠️ Es posible que no haya coincidencias en las listas M3U activas
                    <?php endif; ?>
                </li>
            </ul>
        </div>
        
        <div class="test-buttons">
            <a href="m3u_search_tmdb.php?q=Perdida&auto_search=1" target="_blank" class="test-button">
                🔍 Probar Búsqueda: "Perdida"
            </a>
            <a href="m3u_search_tmdb.php?q=Gone Girl&auto_search=1" target="_blank" class="test-button">
                🔍 Probar Búsqueda: "Gone Girl"
            </a>
            <a href="m3u_search_tmdb.php?q=210577&auto_search=1" target="_blank" class="test-button secondary">
                🎯 Probar Búsqueda: TMDB ID
            </a>
        </div>
        
        <?php else: ?>
        <div class="order-info">
            <p style="text-align: center; color: #888;">No se encontró el pedido "Perdida" en la base de datos</p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
