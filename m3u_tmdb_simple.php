<?php
// Analizador TMDB Simple (sin timeouts)
session_start();

// Configurar límites básicos
ini_set('max_execution_time', 120);
ini_set('memory_limit', '256M');

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función simple para limpiar títulos
function cleanTitle($title) {
    $title = preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/i', '', $title);
    $title = preg_replace('/\s*\(\d{4}\)/', '', $title);
    $title = preg_replace('/\s*(HD|4K|1080p|720p).*$/i', '', $title);
    return trim($title);
}

// Procesar en lotes pequeños
if (isset($_POST['process_batch'])) {
    $list_id = $_POST['list_id'];
    $batch_size = 10; // Procesar solo 10 a la vez
    
    // Obtener siguiente lote sin TMDB ID
    $batch_size = (int)$batch_size; // Asegurar que sea entero
    $stmt = $pdo->prepare("
        SELECT id, title, media_type
        FROM m3u_content
        WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)
        LIMIT $batch_size
    ");
    $stmt->execute([$list_id]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $processed = 0;
    $found = 0;
    
    foreach ($items as $item) {
        $processed++;
        $clean_title = cleanTitle($item['title']);
        
        // Búsqueda simple en TMDB
        $tmdb_data = searchTMDBContent($clean_title);
        
        if ($tmdb_data && !empty($tmdb_data['results'])) {
            $tmdb_result = $tmdb_data['results'][0];
            $found++;
            
            // Actualizar solo campos básicos
            $update_stmt = $pdo->prepare("
                UPDATE m3u_content SET 
                    tmdb_id = ?,
                    tmdb_title = ?,
                    tmdb_poster_path = ?
                WHERE id = ?
            ");
            
            $update_stmt->execute([
                $tmdb_result['id'],
                $tmdb_result['title'] ?? $tmdb_result['name'],
                $tmdb_result['poster_path'],
                $item['id']
            ]);
        }
        
        // Pausa muy corta
        usleep(100000); // 0.1 segundos
    }
    
    $result = ['processed' => $processed, 'found' => $found, 'remaining' => count($items) == $batch_size];
}

// Obtener estadísticas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        COUNT(c.id) as total,
        COUNT(CASE WHEN c.tmdb_id IS NOT NULL AND c.tmdb_id > 0 THEN 1 END) as with_tmdb,
        COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as without_tmdb
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Analizador TMDB Simple - RogsMediaTV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a1a1a;
            --secondary-color: #2d2d2d;
            --accent-color: #46d347;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --success-color: #28a745;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--accent-color);
        }

        .card {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--primary-color);
            color: var(--text-primary);
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #3bc73c;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .stat-card {
            background: var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--accent-color);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
        }

        .list-info {
            background: var(--primary-color);
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .progress-info {
            background: rgba(70, 211, 71, 0.1);
            border: 1px solid var(--accent-color);
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Panel Admin
        </a>

        <div class="header">
            <h1><i class="fas fa-film"></i> Analizador TMDB Simple</h1>
            <p>Procesamiento por lotes pequeños para evitar timeouts</p>
        </div>

        <!-- Selección de lista -->
        <div class="card">
            <h2>📋 Seleccionar Lista para Analizar</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Lista M3U:</label>
                    <select name="list_id" id="list_id" class="form-select" required>
                        <option value="">Selecciona una lista...</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" <?php echo (isset($_POST['list_id']) && $_POST['list_id'] == $list['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['total']; ?> items, <?php echo $list['without_tmdb']; ?> sin TMDB)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="submit" name="process_batch" class="btn btn-primary">
                    <i class="fas fa-play"></i>
                    Procesar Lote (10 elementos)
                </button>
            </form>
        </div>

        <!-- Información de la lista seleccionada -->
        <?php if (isset($_POST['list_id']) && $_POST['list_id']): ?>
        <?php 
        $selected_list = array_filter($lists, function($list) {
            return $list['id'] == $_POST['list_id'];
        });
        $selected_list = reset($selected_list);
        ?>
        <div class="card">
            <h3>📊 Información de la Lista</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $selected_list['total']; ?></div>
                    <div class="stat-label">Total Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $selected_list['with_tmdb']; ?></div>
                    <div class="stat-label">Con TMDB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $selected_list['without_tmdb']; ?></div>
                    <div class="stat-label">Sin TMDB</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $selected_list['total'] > 0 ? round(($selected_list['with_tmdb']/$selected_list['total'])*100, 1) : 0; ?>%</div>
                    <div class="stat-label">Completado</div>
                </div>
            </div>

            <?php if ($selected_list['without_tmdb'] > 0): ?>
            <div class="progress-info">
                <i class="fas fa-info-circle"></i>
                <strong>Progreso estimado:</strong> 
                Necesitas aproximadamente <?php echo ceil($selected_list['without_tmdb'] / 10); ?> lotes para completar esta lista.
            </div>
            <?php else: ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                ¡Esta lista ya está completamente analizada!
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Resultados del procesamiento -->
        <?php if (isset($result)): ?>
        <div class="card">
            <h3>✅ Lote Procesado</h3>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                Lote completado exitosamente
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['processed']; ?></div>
                    <div class="stat-label">Procesados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['found']; ?></div>
                    <div class="stat-label">Encontrados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $result['processed'] > 0 ? round(($result['found']/$result['processed'])*100, 1) : 0; ?>%</div>
                    <div class="stat-label">Éxito</div>
                </div>
            </div>

            <?php if ($result['remaining']): ?>
            <div class="progress-info">
                <i class="fas fa-refresh"></i>
                <strong>Hay más elementos para procesar.</strong> 
                Haz clic en "Procesar Lote" nuevamente para continuar.
            </div>
            <?php else: ?>
            <div class="success">
                <i class="fas fa-check-circle"></i>
                No hay más elementos sin TMDB ID en esta lista.
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Instrucciones -->
        <div class="card">
            <h3>📖 Instrucciones</h3>
            <ul style="padding-left: 1.5rem; color: var(--text-secondary);">
                <li>Selecciona una lista M3U de la lista desplegable</li>
                <li>Haz clic en "Procesar Lote" para analizar 10 elementos</li>
                <li>Repite el proceso hasta completar toda la lista</li>
                <li>El procesamiento por lotes evita timeouts en listas grandes</li>
            </ul>
        </div>
    </div>
</body>
</html>
