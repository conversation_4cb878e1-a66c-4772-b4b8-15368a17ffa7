<?php
session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // COMENTADO TEMPORALMENTE - Agregar nuevos campos de servicios si no existen
    // Esto puede estar causando el error 500
    /*
    $new_service_columns = ['is_infest84', 'is_rogsmediatv', 'is_saul'];
    foreach ($new_service_columns as $column) {
        $cols = $pdo->query("SHOW COLUMNS FROM users LIKE '$column'")->fetchAll();
        if (count($cols) === 0) {
            $pdo->exec("ALTER TABLE users ADD COLUMN $column TINYINT(1) DEFAULT 0");
        }
    }
    */
} catch(PDOException $e) {
    die('Error de conexión: ' . $e->getMessage());
}

// Procesar acciones de administración de usuarios
$user_message = '';
$user_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_user':
                $username = trim($_POST['username']);
                $password = $_POST['password'];
                $is_admin = isset($_POST['is_admin']) ? 1 : 0;

                if (!empty($username) && !empty($password)) {
                    try {
                        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                        $stmt->execute([$username]);

                        if ($stmt->fetch()) {
                            $user_error = 'El usuario ya existe';
                        } else {
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("INSERT INTO users (username, password, is_admin) VALUES (?, ?, ?)");
                            $stmt->execute([$username, $hashed_password, $is_admin]);
                            $user_message = 'Usuario creado exitosamente';
                        }
                    } catch (Exception $e) {
                        $user_error = 'Error al crear usuario: ' . $e->getMessage();
                    }
                } else {
                    $user_error = 'Todos los campos son obligatorios';
                }
                break;

            case 'reset_password':
                $user_id = $_POST['user_id'];
                $new_password = $_POST['new_password'];

                if (!empty($user_id) && !empty($new_password)) {
                    try {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ?, failed_attempts = 0, locked_until = NULL WHERE id = ?");
                        $stmt->execute([$hashed_password, $user_id]);
                        $user_message = 'Contraseña actualizada exitosamente';
                    } catch (Exception $e) {
                        $user_error = 'Error al actualizar contraseña: ' . $e->getMessage();
                    }
                } else {
                    $user_error = 'Datos incompletos';
                }
                break;

            case 'ban_user':
                $user_id = $_POST['user_id'];
                try {
                    $stmt = $pdo->prepare("UPDATE users SET locked_until = DATE_ADD(NOW(), INTERVAL 30 DAY) WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user_message = 'Usuario baneado por 30 días';
                } catch (Exception $e) {
                    $user_error = 'Error al banear usuario: ' . $e->getMessage();
                }
                break;

            case 'unban_user':
                $user_id = $_POST['user_id'];
                try {
                    $stmt = $pdo->prepare("UPDATE users SET locked_until = NULL, failed_attempts = 0 WHERE id = ?");
                    $stmt->execute([$user_id]);
                    $user_message = 'Usuario desbaneado exitosamente';
                } catch (Exception $e) {
                    $user_error = 'Error al desbanear usuario: ' . $e->getMessage();
                }
                break;

            case 'delete_user':
                $user_id = $_POST['user_id'];
                try {
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ? AND id != ?");
                    $stmt->execute([$user_id, $_SESSION['user_id']]);
                    $user_message = 'Usuario eliminado exitosamente';
                } catch (Exception $e) {
                    $user_error = 'Error al eliminar usuario: ' . $e->getMessage();
                }
                break;

            case 'toggle_admin':
                $user_id = $_POST['user_id'];
                try {
                    $stmt = $pdo->prepare("UPDATE users SET is_admin = NOT is_admin WHERE id = ? AND id != ?");
                    $stmt->execute([$user_id, $_SESSION['user_id']]);
                    $user_message = 'Permisos de administrador actualizados';
                } catch (Exception $e) {
                    $user_error = 'Error al actualizar permisos: ' . $e->getMessage();
                }
                break;
        }
    }
}

// Obtener estadísticas
$stats = [];
$stats['total_orders'] = $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn();
$stats['pending_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status IN ('Pendiente', 'En Cola')")->fetchColumn();
$stats['processing_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Procesando'")->fetchColumn();
$stats['completed_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Listo'")->fetchColumn();
$stats['existing_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Ya en existencia'")->fetchColumn();
$stats['unavailable_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'No disponible'")->fetchColumn();
$stats['new_orders'] = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'Recibido'")->fetchColumn();

// No calcular coincidencias automáticamente para mejorar rendimiento
$stats['order_matches'] = 0;

// Función para obtener información de país por IP
function getCountryByIP($ip) {
    if ($ip === '127.0.0.1' || $ip === '::1' || strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0) {
        return ['country' => 'Local', 'flag' => '🏠'];
    }

    try {
        $url = "http://ip-api.com/json/{$ip}?fields=country,countryCode";
        $context = stream_context_create(['http' => ['timeout' => 3]]);
        $response = @file_get_contents($url, false, $context);

        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['country'])) {
                $flag = getFlagEmoji($data['countryCode'] ?? '');
                return ['country' => $data['country'], 'flag' => $flag];
            }
        }
    } catch (Exception $e) {
        // Silenciar errores
    }

    return ['country' => 'Desconocido', 'flag' => '🌍'];
}

function getFlagEmoji($countryCode) {
    $flags = [
        'US' => '🇺🇸', 'MX' => '🇲🇽', 'GT' => '🇬🇹', 'BZ' => '🇧🇿', 'SV' => '🇸🇻',
        'HN' => '🇭🇳', 'NI' => '🇳🇮', 'CR' => '🇨🇷', 'PA' => '🇵🇦', 'CU' => '🇨🇺',
        'JM' => '🇯🇲', 'HT' => '🇭🇹', 'DO' => '🇩🇴', 'PR' => '🇵🇷', 'CO' => '🇨🇴',
        'VE' => '🇻🇪', 'GY' => '🇬🇾', 'SR' => '🇸🇷', 'GF' => '🇬🇫', 'BR' => '🇧🇷',
        'EC' => '🇪🇨', 'PE' => '🇵🇪', 'BO' => '🇧🇴', 'PY' => '🇵🇾', 'UY' => '🇺🇾',
        'AR' => '🇦🇷', 'CL' => '🇨🇱'
    ];
    return $flags[$countryCode] ?? '🌍';
}

// Obtener lista de usuarios con información adicional
$users_query = "
    SELECT
        u.*,
        (SELECT COUNT(*) FROM orders WHERE user_id = u.id) as total_orders,
        (SELECT MAX(created_at) FROM orders WHERE user_id = u.id) as last_order_date
    FROM users u
    ORDER BY u.created_at DESC
";
$users = $pdo->query($users_query)->fetchAll(PDO::FETCH_ASSOC);

// Obtener pedidos organizados por estado con información TMDB
$orders_by_status = [];

// Pedidos nuevos (status Recibido)
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status = 'Recibido'
    ORDER BY o.created_at DESC
    LIMIT 50
");
$orders_by_status['new'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pedidos pendientes (Pendiente y En Cola)
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status IN ('Pendiente', 'En Cola')
    ORDER BY o.created_at DESC
    LIMIT 50
");
$orders_by_status['pending'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pedidos procesando
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status = 'Procesando'
    ORDER BY o.created_at DESC
");
$orders_by_status['processing'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pedidos completados
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status = 'Listo'
    ORDER BY o.created_at DESC
    LIMIT 50
");
$orders_by_status['completed'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pedidos ya en existencia
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status = 'Ya en existencia'
    ORDER BY o.created_at DESC
    LIMIT 50
");
$orders_by_status['existing'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Pedidos no disponibles
$stmt = $pdo->query("
    SELECT o.*, u.username
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.status = 'No disponible'
    ORDER BY o.created_at DESC
    LIMIT 50
");
$orders_by_status['unavailable'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Función para generar tabla de pedidos
function generateOrdersTable($orders, $section_id) {
    if (empty($orders)) {
        $messages = [
            'new' => 'No hay pedidos recibidos en este momento',
            'pending' => 'No hay pedidos pendientes',
            'processing' => 'No hay pedidos en proceso',
            'completed' => 'No hay pedidos completados',
            'existing' => 'No hay pedidos marcados como "Ya en existencia"',
            'unavailable' => 'No hay pedidos marcados como "No disponible"'
        ];

        $message = $messages[$section_id] ?? 'No hay pedidos en esta categoría';

        return '<div style="padding: 2rem; text-align: center; color: var(--text-secondary);">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>' . $message . '</p>
                    ' . ($section_id === 'new' ? '<p style="font-size: 0.9rem; margin-top: 1rem; color: var(--text-secondary);">Los nuevos pedidos aparecerán aquí automáticamente</p>' : '') . '
                </div>';
    }

    $html = '<div class="orders-table-container">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                        <th>Coincidencias</th>
                            <th>Usuario</th>
                            <th>Título</th>
                            <th>Tipo</th>
                            <th>Estado</th>
                            <th>Cliente Notif.</th>
                            <th>Ubicación</th>
                            <th>Fecha</th>
                            <th>TMDB</th>
                            <th>Coincidencias</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($orders as $order) {
        $isNew = $order['notif_seen'] == 0;
        $html .= '<tr class="' . ($isNew ? 'new-order' : '') . '" data-order-id="' . $order['id'] . '">
                    <td>
                        <strong>#' . $order['id'] . '</strong>';
        if ($isNew) {
            $html .= '<span style="color: var(--accent-color); margin-left: 0.5rem;">
                        <i class="fas fa-circle" style="font-size: 0.5rem;"></i>
                      </span>';
        }
        $html .= '</td>
                    <td>
                        <div class="user-info">
                            <div class="username">' . htmlspecialchars($order['username'] ?? 'Usuario #' . $order['user_id']) . '</div>
                            <div class="user-badges">
                                <span class="user-badge badge-cliente">Usuario</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="order-title" title="' . htmlspecialchars($order['title']) . '">
                            ' . htmlspecialchars($order['title']) . '
                        </div>
                        <div style="font-size: 0.8rem; color: var(--text-secondary);">
                            ' . ($order['year'] ? '(' . $order['year'] . ')' : '') . '
                        </div>
                    </td>
                    <td>
                        <span style="color: var(--text-primary);">
                            <i class="fas fa-' . ($order['media_type'] === 'movie' ? 'film' : 'tv') . '"></i>
                            ' . ($order['media_type'] === 'movie' ? 'Película' : 'Serie') . '
                        </span>
                    </td>
                    <td>
                        <select class="status-select" onchange="updateOrderStatus(' . $order['id'] . ', this.value)">
                            <option value="Recibido"' . ($order['status'] === 'Recibido' ? ' selected' : '') . '>📦 Recibido</option>
                            <option value="Pendiente"' . ($order['status'] === 'Pendiente' ? ' selected' : '') . '>⏳ Pendiente</option>
                            <option value="En Cola"' . ($order['status'] === 'En Cola' ? ' selected' : '') . '>🔄 En Cola</option>
                            <option value="Procesando"' . ($order['status'] === 'Procesando' ? ' selected' : '') . '>⚙️ Procesando</option>
                            <option value="Ya en existencia"' . ($order['status'] === 'Ya en existencia' ? ' selected' : '') . '>🎯 Ya en existencia</option>
                            <option value="Listo"' . ($order['status'] === 'Listo' ? ' selected' : '') . '>✅ Listo</option>
                            <option value="No disponible"' . ($order['status'] === 'No disponible' ? ' selected' : '') . '>❌ No disponible</option>
                        </select>
                    </td>
                    <td>
                        <div style="text-align: center; font-size: 0.9rem;">';

        // Mostrar estado de notificación del cliente
        if (isset($order['client_notif_seen'])) {
            if ($order['client_notif_seen']) {
                $html .= '<div style="color: var(--success-color);">
                            <i class="fas fa-eye"></i> Visto
                          </div>';
                if ($order['client_notif_seen_at']) {
                    $html .= '<div style="font-size: 0.7rem; color: var(--text-secondary);">' .
                             date('d/m H:i', strtotime($order['client_notif_seen_at'])) . '</div>';
                }
            } else {
                $html .= '<div style="color: var(--warning-color);">
                            <i class="fas fa-eye-slash"></i> No visto
                          </div>';
            }
        } else {
            $html .= '<div style="color: var(--text-secondary);">
                        <i class="fas fa-question"></i> N/A
                      </div>';
        }

        $html .= '</div>
                    </td>
                    <td>
                        <div style="font-size: 0.8rem;">
                            <div>' . htmlspecialchars($order['city'] ?? 'N/A') . '</div>
                            <div style="color: var(--text-secondary);">' . htmlspecialchars($order['country'] ?? 'N/A') . '</div>
                        </div>
                    </td>
                    <td>
                        <div class="order-time">
                            <div>' . date('d/m/Y', strtotime($order['created_at'])) . '</div>
                            <div style="color: var(--text-secondary);">' . date('H:i', strtotime($order['created_at'])) . '</div>
                        </div>
                    </td>
                    <td>
                        <a href="https://www.themoviedb.org/' . $order['media_type'] . '/' . $order['tmdb_id'] . '?language=es-MX"
                           target="_blank" class="tmdb-link">
                            <i class="fas fa-external-link-alt"></i>
                            Ver
                        </a>
                    </td>
                    <td>
                        <a href="m3u_search_tmdb.php?search=' . urlencode($order['title']) . '&auto_search=1"
                           target="_blank" class="coincidences-link" title="Buscar coincidencias M3U para: ' . htmlspecialchars($order['title']) . '">
                            <i class="fas fa-search"></i>
                            Coincidencias
                        </a>
                    </td>
                </tr>';
    }

    $html .= '</tbody></table></div>';
    return $html;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Panel de Administración - RGS TOOL</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔐</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --success-color: #10b981;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .admin-header {
            background: var(--gradient-dark);
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .admin-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .admin-title h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .admin-title .badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .admin-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.1);
            color: var(--text-primary);
            text-decoration: none;
            border: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .nav-btn.refresh-btn {
            background: var(--accent-color);
        }

        .nav-btn.refresh-btn:hover {
            background: #3bc55a;
        }

        .nav-btn.logout-btn {
            background: var(--error-color);
        }

        .nav-btn.logout-btn:hover {
            background: #c82333;
        }

        /* Stats Cards */
        .stats-section {
            padding: 2rem 0;
            background: var(--dark-bg);
        }

        .stats-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            background: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-card.new-orders::before {
            background: var(--accent-color);
        }

        .stat-card.pending::before {
            background: var(--warning-color);
        }

        .stat-card.processing::before {
            background: #ff9800;
        }

        .stat-card.completed::before {
            background: var(--success-color);
        }

        .stat-card.unavailable::before {
            background: var(--error-color);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .stat-icon.new { background: rgba(70, 211, 105, 0.2); color: var(--accent-color); }
        .stat-icon.pending { background: rgba(255, 165, 0, 0.2); color: var(--warning-color); }
        .stat-icon.processing { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .stat-icon.completed { background: rgba(40, 167, 69, 0.2); color: var(--success-color); }
        .stat-icon.unavailable { background: rgba(244, 67, 54, 0.2); color: var(--error-color); }
        .stat-icon.total { background: rgba(229, 9, 20, 0.2); color: var(--primary-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* Orders Section */
        .orders-section {
            padding: 2rem 0;
        }

        .orders-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .refresh-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Animaciones parpadeantes para tareas pendientes */
        .stat-card.has-pending {
            animation: pendingPulse 2s infinite;
        }

        @keyframes pendingPulse {
            0%, 100% {
                box-shadow: var(--shadow-light);
                border-color: var(--border-color);
            }
            50% {
                box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
                border-color: var(--warning-color);
            }
        }

        /* Estilos para cartas de soporte */
        .support-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .support-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .support-card.has-activity {
            animation: supportActivity 2s infinite;
        }

        @keyframes supportActivity {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 6px 20px rgba(233, 30, 99, 0.5);
            }
        }

        .support-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(233, 30, 99, 0.5), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .support-card.has-activity::before {
            opacity: 1;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .stat-card.has-new {
            animation: newPulse 1.5s infinite;
        }

        @keyframes newPulse {
            0%, 100% {
                box-shadow: var(--shadow-light);
                border-color: var(--border-color);
            }
            50% {
                box-shadow: 0 0 20px rgba(70, 211, 105, 0.4);
                border-color: var(--accent-color);
            }
        }

        /* Secciones expandibles */
        .orders-section-expandable {
            margin-bottom: 2rem;
        }

        .section-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            margin-bottom: 1rem;
        }

        .section-toggle:hover {
            background: rgba(255,255,255,0.05);
            transform: translateY(-2px);
        }

        .section-toggle.expanded {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
            margin-bottom: 0;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .section-toggle.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .orders-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            border: 1px solid var(--border-color);
            border-top: none;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }

        .orders-content.expanded {
            max-height: 2000px;
        }

        /* Estilos para Sistema de Bandejas */
        .inbox-navigation {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
        }

        .inbox-tabs {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            flex: 1;
        }

        .inbox-tab {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
            min-width: 120px;
        }

        .inbox-tab:hover {
            background: rgba(255,255,255,0.05);
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }

        .inbox-tab.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-medium);
        }

        .inbox-tab.active .inbox-icon {
            background: rgba(255,255,255,0.2) !important;
            color: white !important;
        }

        .inbox-tab.active .inbox-label {
            color: white;
        }

        .inbox-tab.active .inbox-count {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .inbox-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .inbox-icon.new { background: rgba(70, 211, 105, 0.2); color: var(--accent-color); }
        .inbox-icon.pending { background: rgba(255, 165, 0, 0.2); color: var(--warning-color); }
        .inbox-icon.processing { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .inbox-icon.completed { background: rgba(40, 167, 69, 0.2); color: var(--success-color); }
        .inbox-icon.existing { background: rgba(70, 211, 71, 0.2); color: #46d347; }
        .inbox-icon.unavailable { background: rgba(244, 67, 54, 0.2); color: var(--error-color); }

        .inbox-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .inbox-label {
            font-size: 0.85rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .inbox-count {
            font-size: 0.75rem;
            font-weight: 600;
            background: rgba(255,255,255,0.1);
            color: var(--text-secondary);
            padding: 0.15rem 0.4rem;
            border-radius: 10px;
            text-align: center;
            min-width: 20px;
        }

        .inbox-controls {
            display: flex;
            gap: 0.5rem;
        }

        .nav-control {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-control:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .nav-control:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Contenedor de Bandejas */
        .inbox-container {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            position: relative;
        }

        .inbox-slider {
            display: flex;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 600%; /* 6 bandejas × 100% */
        }

        .inbox-panel {
            width: 16.666%; /* 100% / 6 bandejas */
            flex-shrink: 0;
            min-height: 400px;
        }

        .inbox-panel.active {
            /* Bandeja activa - no necesita estilos especiales aquí */
        }

        .inbox-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            background: var(--dark-bg);
            border-bottom: 1px solid var(--border-color);
        }

        .inbox-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .inbox-title h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .inbox-title p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .inbox-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.85rem;
        }

        .action-btn:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .inbox-content {
            padding: 0;
            max-height: 70vh;
            overflow-y: auto;
        }

        /* Orders Table */
        .orders-table-container {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }

        .orders-table th,
        .orders-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .orders-table th {
            background: var(--dark-bg);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .orders-table td {
            color: var(--text-secondary);
        }

        .orders-table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        .order-title {
            color: var(--text-primary);
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .username {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-badges {
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }

        .user-badge {
            padding: 0.1rem 0.4rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .badge-cliente { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .badge-mavistv { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .badge-tvdigital { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }
        .badge-limites { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .badge-infest84 { background: rgba(255, 87, 34, 0.2); color: #ff5722; }
        .badge-rogsmediatv { background: rgba(229, 9, 20, 0.2); color: #e50914; }
        .badge-saul { background: rgba(103, 58, 183, 0.2); color: #673ab7; }

        .status-select {
            padding: 0.5rem;
            background: var(--dark-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 0.85rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .status-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .status-badge {
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
            min-width: 80px;
        }

        .status-recibido { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .status-pendiente { background: rgba(156, 39, 176, 0.2); color: #9c27b0; }
        .status-en-cola { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status-procesando { background: rgba(255, 152, 0, 0.2); color: #ff9800; }
        .status-ya-en-existencia { background: rgba(70, 211, 71, 0.2); color: #46d347; }
        .status-listo { background: rgba(76, 175, 80, 0.2); color: #4caf50; }
        .status-no-disponible { background: rgba(244, 67, 54, 0.2); color: #f44336; }

        .tmdb-link {
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            padding: 0.4rem 0.8rem;
            background: #01b4e4;
            color: white;
            text-decoration: none;
            border-radius: var(--border-radius);
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .tmdb-link:hover {
            background: #0099cc;
            transform: translateY(-2px);
        }

        .coincidences-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: var(--accent-color);
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .coincidences-link:hover {
            background: #3bc55a;
            transform: translateY(-2px);
        }

        /* Estilos para botones de navegación del admin */
        .admin-nav-buttons a {
            transition: all 0.3s ease;
            min-height: 44px;
            box-sizing: border-box;
            white-space: nowrap;
        }

        .admin-nav-buttons a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .admin-nav-buttons a i {
            flex-shrink: 0;
        }

        .admin-nav-buttons a span {
            white-space: nowrap;
        }

        /* Botón de coincidencias (verde) */
        .admin-nav-buttons a[href*="order_matches"]:hover {
            background: rgba(70, 211, 71, 0.2) !important;
            border-color: #3bc55a !important;
            color: #3bc55a !important;
        }

        /* Botón de búsqueda TMDB (azul) */
        .admin-nav-buttons a[href*="m3u_search"]:hover {
            background: rgba(23, 162, 184, 0.2) !important;
            border-color: #138496 !important;
            color: #138496 !important;
        }

        /* Botón de backups (amarillo) */
        .admin-nav-buttons a[href*="backup_manager"]:hover {
            background: rgba(255, 193, 7, 0.2) !important;
            border-color: #e0a800 !important;
            color: #e0a800 !important;
        }

        /* Botón de gestionar (morado) */
        .admin-nav-buttons a[href*="m3u_manager"]:hover {
            background: rgba(111, 66, 193, 0.2) !important;
            border-color: #5a2d91 !important;
            color: #5a2d91 !important;
        }

        /* Botón de Admin Soporte (rosa) */
        .admin-nav-buttons a[href*="admin2"]:hover {
            background: rgba(233, 30, 99, 0.2) !important;
            border-color: #c2185b !important;
            color: #c2185b !important;
        }

        .order-time {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .new-order {
            background: rgba(70, 211, 105, 0.1) !important;
            border-left: 4px solid var(--accent-color);
        }

        /* Animación para indicador de coincidencias */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Loading States */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Estilos para la sección de soporte integrada */
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .service-card a:hover,
        .service-card button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        /* Estilos para administración de usuarios */
        .user-management-form {
            background: var(--secondary-color);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .user-management-form input,
        .user-management-form select {
            width: 100%;
            padding: 0.75rem;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .user-management-form input:focus,
        .user-management-form select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .user-management-form label {
            display: block;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .user-actions-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .user-actions-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .btn-reset { background: var(--warning-color); color: white; }
        .btn-ban { background: var(--error-color); color: white; }
        .btn-unban { background: var(--success-color); color: white; }
        .btn-admin { background: var(--primary-color); color: white; }
        .btn-remove-admin { background: var(--info-color); color: white; }
        .btn-delete { background: #dc2626; color: white; }

        .user-status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-admin {
            background: rgba(37, 99, 235, 0.2);
            color: var(--primary-color);
        }

        .status-user {
            background: rgba(107, 114, 128, 0.2);
            color: var(--text-secondary);
        }

        .status-banned {
            background: rgba(239, 68, 68, 0.2);
            color: var(--error-color);
        }

        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: var(--success-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                text-align: center;
            }

            .admin-nav {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
            }

            .stats-container {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }

            .orders-table-container {
                overflow-x: auto;
            }

            .admin-nav-buttons {
                margin-left: 0 !important;
                justify-content: center;
                gap: 0.75rem !important;
                flex-wrap: wrap;
            }

            .admin-nav-buttons a,
            .admin-nav-buttons button {
                flex: 1 1 auto;
                min-width: 160px;
                justify-content: center;
                text-align: center;
                padding: 0.6rem 1rem !important;
                font-size: 0.8rem !important;
            }

            .admin-nav-buttons a span,
            .admin-nav-buttons button span {
                display: block;
                white-space: normal;
                line-height: 1.2;
            }

            .orders-table {
                min-width: 800px;
            }

            .section-header {
                flex-direction: column;
                text-align: center;
            }

            /* Responsive para sección de soporte */
            #support-section .service-card {
                margin-bottom: 1rem;
            }

            /* Responsive para sistema de bandejas */
            .inbox-navigation {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .inbox-tabs {
                justify-content: center;
                gap: 0.25rem;
            }

            .inbox-tab {
                min-width: auto;
                padding: 0.5rem;
                flex-direction: column;
                text-align: center;
                gap: 0.25rem;
            }

            .inbox-tab .inbox-icon {
                width: 28px;
                height: 28px;
                font-size: 0.8rem;
            }

            .inbox-tab .inbox-label {
                font-size: 0.7rem;
            }

            .inbox-tab .inbox-count {
                font-size: 0.65rem;
                padding: 0.1rem 0.3rem;
            }

            .inbox-controls {
                justify-content: center;
            }

            .nav-control {
                width: 36px;
                height: 36px;
            }

            .inbox-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
                padding: 1rem;
            }

            .inbox-title {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .inbox-title h3 {
                font-size: 1rem;
            }

            .inbox-title p {
                font-size: 0.8rem;
            }

            .inbox-actions {
                justify-content: center;
            }

            .inbox-content {
                max-height: 60vh;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="header-container">
            <div class="admin-title">
                <h1>
                    <i class="fas fa-cog"></i>
                    Panel de Administración
                </h1>
                <span class="badge">ADMIN</span>
            </div>

            <nav class="admin-nav">
                <button class="nav-btn refresh-btn" onclick="manualRefresh()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Actualizar</span>
                </button>
                <a href="index.php" class="nav-btn">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </a>
                <a href="admin_logout.php" class="nav-btn logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </nav>
        </div>
    </header>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="stats-container">
            <div class="stat-card new-orders <?php echo $stats['new_orders'] > 0 ? 'has-new' : ''; ?>" onclick="toggleSection('new')">
                <div class="stat-header">
                    <div class="stat-icon new">
                        <i class="fas fa-bell"></i>
                    </div>
                </div>
                <div class="stat-number" id="newOrdersCount"><?php echo $stats['new_orders']; ?></div>
                <div class="stat-label">Recibidos</div>
            </div>

            <div class="stat-card pending <?php echo $stats['pending_orders'] > 0 ? 'has-pending' : ''; ?>" onclick="toggleSection('pending')">
                <div class="stat-header">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-number" id="pendingOrdersCount"><?php echo $stats['pending_orders']; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>

            <div class="stat-card processing" onclick="toggleSection('processing')">
                <div class="stat-header">
                    <div class="stat-icon processing">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
                <div class="stat-number" id="processingOrdersCount"><?php echo $stats['processing_orders']; ?></div>
                <div class="stat-label">Procesando</div>
            </div>

            <div class="stat-card completed" onclick="toggleSection('completed')">
                <div class="stat-header">
                    <div class="stat-icon completed">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
                <div class="stat-number" id="completedOrdersCount"><?php echo $stats['completed_orders']; ?></div>
                <div class="stat-label">Completados</div>
            </div>

            <div class="stat-card" style="border: 2px solid #46d347;" onclick="toggleSection('existing')">
                <div class="stat-header">
                    <div class="stat-icon" style="background: #46d347; color: var(--primary-color);">
                        <i class="fas fa-bullseye"></i>
                    </div>
                </div>
                <div class="stat-number" style="color: #46d347;" id="existingOrdersCount"><?php echo $stats['existing_orders']; ?></div>
                <div class="stat-label">Ya en Existencia</div>
            </div>

            <div class="stat-card unavailable" onclick="toggleSection('unavailable')">
                <div class="stat-header">
                    <div class="stat-icon unavailable">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="stat-number" id="unavailableOrdersCount"><?php echo $stats['unavailable_orders']; ?></div>
                <div class="stat-label">No Disponibles</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon total">
                        <i class="fas fa-list"></i>
                    </div>
                </div>
                <div class="stat-number" id="totalOrdersCount"><?php echo $stats['total_orders']; ?></div>
                <div class="stat-label">Total Pedidos</div>
            </div>

            <!-- Cartas de Soporte en Tiempo Real -->
            <div class="stat-card support-card" onclick="toggleSupportSection('chat')" style="cursor: pointer;">
                <div class="stat-header">
                    <div class="stat-icon" style="background: rgba(233, 30, 99, 0.2); color: #e91e63;">
                        <i class="fas fa-comments"></i>
                    </div>
                </div>
                <div class="stat-number" id="supportChatCount">0</div>
                <div class="stat-label">Chats Activos</div>
            </div>

            <div class="stat-card support-card" onclick="toggleSupportSection('tickets')" style="cursor: pointer;">
                <div class="stat-header">
                    <div class="stat-icon" style="background: rgba(245, 158, 11, 0.2); color: #f59e0b;">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                </div>
                <div class="stat-number" id="supportTicketsCount">0</div>
                <div class="stat-label">Tickets Abiertos</div>
            </div>

            <div class="stat-card support-card" onclick="toggleSupportSection('channels')" style="cursor: pointer;">
                <div class="stat-header">
                    <div class="stat-icon" style="background: rgba(6, 182, 212, 0.2); color: #06b6d4;">
                        <i class="fas fa-tv"></i>
                    </div>
                </div>
                <div class="stat-number" id="supportChannelsCount">0</div>
                <div class="stat-label">Canales Pendientes</div>
            </div>

            <div class="stat-card support-card" onclick="toggleSupportSection('apps')" style="cursor: pointer;">
                <div class="stat-header">
                    <div class="stat-icon" style="background: rgba(16, 185, 129, 0.2); color: #10b981;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                </div>
                <div class="stat-number" id="supportAppsCount">0</div>
                <div class="stat-label">Descargas Hoy</div>
            </div>

            <!-- Nueva carta de Administración de Usuarios -->
            <div class="stat-card support-card" onclick="toggleUserManagement()" style="cursor: pointer;">
                <div class="stat-header">
                    <div class="stat-icon" style="background: rgba(139, 69, 19, 0.2); color: #8b4513;">
                        <i class="fas fa-users-cog"></i>
                    </div>
                </div>
                <div class="stat-number" id="totalUsersCount"><?php
                    $user_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                    echo $user_count;
                ?></div>
                <div class="stat-label">Gestión de Usuarios</div>
            </div>

        </div>
    </section>

    <!-- Orders Section -->
    <section class="orders-section">
        <div class="orders-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-inbox"></i>
                    Bandeja de Pedidos por Estado
                </h2>
                <div class="auto-refresh">
                    <div class="refresh-indicator" id="refreshIndicator"></div>
                    <span id="autoRefreshStatus">Auto-actualización inteligente</span>

                    <!-- Indicador de estado de conexión -->
                    <div id="connection-status" style="margin-left: 1rem; display: flex; align-items: center; gap: 0.5rem; font-size: 0.8rem;">
                        <span class="status-indicator" style="width: 8px; height: 8px; border-radius: 50%; background: #6b7280;"></span>
                        <span class="status-text" style="color: var(--text-secondary);">Conectando...</span>
                    </div>

                    <button onclick="manualRefresh()" style="background: var(--accent-color); color: var(--primary-color); border: none; padding: 0.3rem 0.8rem; border-radius: 4px; cursor: pointer; font-size: 0.8rem; margin-left: 1rem;">
                        <i class="fas fa-sync-alt" id="refreshIcon"></i>
                        Actualizar Ahora
                    </button>

                    <button onclick="toggleAutoRefresh()" id="autoRefreshBtn" style="background: rgba(70, 211, 71, 0.2); color: var(--accent-color); border: 1px solid var(--accent-color); padding: 0.3rem 0.8rem; border-radius: 4px; cursor: pointer; font-size: 0.8rem; margin-left: 0.5rem;">
                        <i class="fas fa-pause" id="autoRefreshIcon"></i>
                        <span id="autoRefreshText">Pausar</span>
                    </button>
                </div>
                <div class="admin-nav-buttons" style="margin-left: 2rem; display: flex; gap: 1.5rem; align-items: center; flex-wrap: wrap;">
                    <button onclick="toggleSupportSection('chat')" style="color: #e91e63; text-decoration: none; font-weight: 500; background: rgba(233, 30, 99, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid #e91e63; display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box; cursor: pointer;">
                        <i class="fas fa-headset" style="font-size: 1.1em;"></i>
                        <span>Panel Soporte Integrado ⚡</span>
                    </button>
                    <a href="order_matches_safe.php" style="color: var(--accent-color); text-decoration: none; font-weight: 500; background: rgba(70, 211, 71, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid var(--accent-color); display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box;">
                        <i class="fas fa-bullseye" style="font-size: 1.1em;"></i>
                        <span>Coincidencias Encontradas</span>
                    </a>
                    <a href="admin_tmdb_hub.php" style="color: #01b4e4; text-decoration: none; font-weight: 500; background: rgba(1, 180, 228, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid #01b4e4; display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box;">
                        <i class="fas fa-film" style="font-size: 1.1em;"></i>
                        <span>Hub TMDB</span>
                    </a>
                    <a href="m3u_search_tmdb.php" style="color: var(--info-color); text-decoration: none; font-weight: 500; background: rgba(23, 162, 184, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid var(--info-color); display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box;">
                        <i class="fas fa-search" style="font-size: 1.1em;"></i>
                        <span>Buscar con TMDB</span>
                    </a>
                    <a href="backup_manager.php" style="color: var(--warning-color); text-decoration: none; font-weight: 500; background: rgba(255, 193, 7, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid var(--warning-color); display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box;">
                        <i class="fas fa-database" style="font-size: 1.1em;"></i>
                        <span>Gestor de Backups</span>
                    </a>
                    <a href="m3u_manager.php" style="color: #6f42c1; text-decoration: none; font-weight: 500; background: rgba(111, 66, 193, 0.1); padding: 0.75rem 1.25rem; border-radius: 8px; border: 1px solid #6f42c1; display: inline-flex; align-items: center; gap: 0.75rem; font-size: 0.9rem; min-height: 40px; box-sizing: border-box;">
                        <i class="fas fa-cog" style="font-size: 1.1em;"></i>
                        <span>Gestionar</span>
                    </a>
                </div>
            </div>

            <!-- Navegación de Bandejas -->
            <div class="inbox-navigation">
                <div class="inbox-tabs">
                    <button class="inbox-tab active" data-inbox="new" onclick="switchInbox('new')">
                        <div class="inbox-icon new">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">Recibidos</span>
                            <span class="inbox-count" id="inbox-count-new"><?php echo count($orders_by_status['new']); ?></span>
                        </div>
                    </button>

                    <button class="inbox-tab" data-inbox="pending" onclick="switchInbox('pending')">
                        <div class="inbox-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">Pendientes</span>
                            <span class="inbox-count" id="inbox-count-pending"><?php echo count($orders_by_status['pending']); ?></span>
                        </div>
                    </button>

                    <button class="inbox-tab" data-inbox="processing" onclick="switchInbox('processing')">
                        <div class="inbox-icon processing">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">Procesando</span>
                            <span class="inbox-count" id="inbox-count-processing"><?php echo count($orders_by_status['processing']); ?></span>
                        </div>
                    </button>

                    <button class="inbox-tab" data-inbox="completed" onclick="switchInbox('completed')">
                        <div class="inbox-icon completed">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">Completados</span>
                            <span class="inbox-count" id="inbox-count-completed"><?php echo count($orders_by_status['completed']); ?></span>
                        </div>
                    </button>

                    <button class="inbox-tab" data-inbox="existing" onclick="switchInbox('existing')">
                        <div class="inbox-icon existing">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">En Existencia</span>
                            <span class="inbox-count" id="inbox-count-existing"><?php echo count($orders_by_status['existing']); ?></span>
                        </div>
                    </button>

                    <button class="inbox-tab" data-inbox="unavailable" onclick="switchInbox('unavailable')">
                        <div class="inbox-icon unavailable">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="inbox-info">
                            <span class="inbox-label">No Disponibles</span>
                            <span class="inbox-count" id="inbox-count-unavailable"><?php echo count($orders_by_status['unavailable']); ?></span>
                        </div>
                    </button>
                </div>

                <!-- Controles de navegación -->
                <div class="inbox-controls">
                    <button class="nav-control" onclick="previousInbox()" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="nav-control" onclick="nextInbox()" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <!-- Contenedor de Bandejas Deslizantes -->
            <div class="inbox-container">
                <div class="inbox-slider" id="inboxSlider">
                    <!-- Bandeja de Pedidos Recibidos -->
                    <div class="inbox-panel active" id="inbox-new">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon new">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div>
                                    <h3>📦 Pedidos Recibidos</h3>
                                    <p><?php echo count($orders_by_status['new']); ?> pedidos con status "Recibido"</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('new')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('new')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['new'], 'new'); ?>
                        </div>
                    </div>

                    <!-- Bandeja de Pedidos Pendientes -->
                    <div class="inbox-panel" id="inbox-pending">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon pending">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h3>⏳ Pedidos Pendientes</h3>
                                    <p><?php echo count($orders_by_status['pending']); ?> pedidos "Pendiente" y "En Cola"</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('pending')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('pending')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['pending'], 'pending'); ?>
                        </div>
                    </div>

                    <!-- Bandeja de Pedidos Procesando -->
                    <div class="inbox-panel" id="inbox-processing">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon processing">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div>
                                    <h3>⚙️ Pedidos Procesando</h3>
                                    <p><?php echo count($orders_by_status['processing']); ?> pedidos en proceso</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('processing')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('processing')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['processing'], 'processing'); ?>
                        </div>
                    </div>

                    <!-- Bandeja de Pedidos Completados -->
                    <div class="inbox-panel" id="inbox-completed">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon completed">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div>
                                    <h3>✅ Pedidos Completados</h3>
                                    <p><?php echo count($orders_by_status['completed']); ?> pedidos listos</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('completed')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('completed')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['completed'], 'completed'); ?>
                        </div>
                    </div>

                    <!-- Bandeja de Pedidos Ya en Existencia -->
                    <div class="inbox-panel" id="inbox-existing">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon existing">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <div>
                                    <h3>🎯 Ya en Existencia</h3>
                                    <p><?php echo count($orders_by_status['existing']); ?> pedidos ya disponibles en plataforma</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('existing')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('existing')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['existing'], 'existing'); ?>
                        </div>
                    </div>

                    <!-- Bandeja de Pedidos No Disponibles -->
                    <div class="inbox-panel" id="inbox-unavailable">
                        <div class="inbox-header">
                            <div class="inbox-title">
                                <div class="inbox-icon unavailable">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div>
                                    <h3>❌ Pedidos No Disponibles</h3>
                                    <p><?php echo count($orders_by_status['unavailable']); ?> pedidos no disponibles</p>
                                </div>
                            </div>
                            <div class="inbox-actions">
                                <button class="action-btn" onclick="markAllAsRead('unavailable')" title="Marcar todos como leídos">
                                    <i class="fas fa-check-double"></i>
                                </button>
                                <button class="action-btn" onclick="refreshInbox('unavailable')" title="Actualizar bandeja">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="inbox-content">
                            <?php echo generateOrdersTable($orders_by_status['unavailable'], 'unavailable'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Support Section -->
    <section class="orders-section" id="support-section" style="display: none;">
        <div class="orders-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-headset"></i>
                    Panel de Soporte Técnico Integrado
                </h2>
                <button onclick="closeSupportSection()" style="background: var(--error-color); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                    Cerrar
                </button>
            </div>

            <!-- Support Services Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">

                <!-- Chat Support -->
                <div class="service-card" style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 2rem; background: var(--gradient-dark); text-align: center;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; margin: 0 auto 1rem; background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); color: white;">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Chat en Tiempo Real</h3>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">Gestionar conversaciones con usuarios</p>
                    </div>
                    <div style="padding: 2rem;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <a href="admin_chat_real.php" target="_blank" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fas fa-external-link-alt"></i>
                                Abrir Chat Admin
                            </a>
                            <button onclick="window.open('user_chat_realtime.php', '_blank')" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: transparent; color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                <i class="fas fa-eye"></i>
                                Vista de Usuario
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tickets Support -->
                <div class="service-card" style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 2rem; background: var(--gradient-dark); text-align: center;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; margin: 0 auto 1rem; background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); color: white;">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Sistema de Tickets</h3>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">Gestionar tickets de soporte</p>
                    </div>
                    <div style="padding: 2rem;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <a href="tickets_admin.php" target="_blank" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fas fa-external-link-alt"></i>
                                Gestionar Tickets
                            </a>
                            <button onclick="window.open('user_tickets.php', '_blank')" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: transparent; color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                <i class="fas fa-eye"></i>
                                Vista de Usuario
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Channel Requests -->
                <div class="service-card" style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 2rem; background: var(--gradient-dark); text-align: center;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; margin: 0 auto 1rem; background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); color: white;">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Solicitudes de Canales</h3>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">Gestionar peticiones de canales</p>
                    </div>
                    <div style="padding: 2rem;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <a href="admin_channels_real.php" target="_blank" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fas fa-external-link-alt"></i>
                                Gestionar Canales
                            </a>
                            <button onclick="window.open('user_channels.php', '_blank')" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: transparent; color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                <i class="fas fa-eye"></i>
                                Vista de Usuario
                            </button>
                        </div>
                    </div>
                </div>

                <!-- App Management -->
                <div class="service-card" style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); overflow: hidden; transition: all 0.3s ease;">
                    <div style="padding: 2rem; background: var(--gradient-dark); text-align: center;">
                        <div style="width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 2rem; margin: 0 auto 1rem; background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); color: white;">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem;">Gestión de Apps</h3>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">Administrar aplicaciones y descargas</p>
                    </div>
                    <div style="padding: 2rem;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <a href="apps_admin.php" target="_blank" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fas fa-external-link-alt"></i>
                                Gestionar Apps
                            </a>
                            <button onclick="window.open('user_apps.php', '_blank')" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: transparent; color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                <i class="fas fa-eye"></i>
                                Vista de Usuario
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- User Management Section -->
    <section class="orders-section" id="user-management-section" style="display: none;">
        <div class="orders-container">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-users-cog"></i>
                    Administración de Usuarios
                </h2>
                <button onclick="closeUserManagement()" style="background: var(--error-color); color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-times"></i>
                    Cerrar
                </button>
            </div>

            <?php if ($user_message): ?>
            <div style="background: rgba(16, 185, 129, 0.2); border: 1px solid #10b981; color: #10b981; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($user_message); ?>
            </div>
            <?php endif; ?>

            <?php if ($user_error): ?>
            <div style="background: rgba(239, 68, 68, 0.2); border: 1px solid #ef4444; color: #ef4444; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($user_error); ?>
            </div>
            <?php endif; ?>

            <!-- Formulario para agregar usuario -->
            <div style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); padding: 2rem; margin-bottom: 2rem;">
                <h3 style="color: var(--text-primary); margin-bottom: 1.5rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-user-plus"></i>
                    Agregar Nuevo Usuario
                </h3>

                <form method="POST" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
                    <input type="hidden" name="action" value="add_user">

                    <div>
                        <label style="display: block; color: var(--text-secondary); margin-bottom: 0.5rem; font-size: 0.9rem;">Usuario:</label>
                        <input type="text" name="username" required style="width: 100%; padding: 0.75rem; background: var(--dark-bg); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                    </div>

                    <div>
                        <label style="display: block; color: var(--text-secondary); margin-bottom: 0.5rem; font-size: 0.9rem;">Contraseña:</label>
                        <input type="password" name="password" required style="width: 100%; padding: 0.75rem; background: var(--dark-bg); border: 1px solid var(--border-color); border-radius: 6px; color: var(--text-primary);">
                    </div>

                    <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 0;">
                        <input type="checkbox" name="is_admin" id="is_admin" style="margin: 0;">
                        <label for="is_admin" style="color: var(--text-secondary); font-size: 0.9rem;">Es Administrador</label>
                    </div>

                    <button type="submit" style="padding: 0.75rem 1.5rem; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500;">
                        <i class="fas fa-plus"></i> Crear Usuario
                    </button>
                </form>
            </div>

            <!-- Lista de usuarios -->
            <div style="background: var(--secondary-color); border-radius: 12px; border: 1px solid var(--border-color); overflow: hidden;">
                <div style="padding: 1.5rem; background: var(--dark-bg); border-bottom: 1px solid var(--border-color);">
                    <h3 style="color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-users"></i>
                        Lista de Usuarios (<?php echo count($users); ?> total)
                    </h3>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: var(--dark-bg);">
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">ID</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Usuario</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Tipo</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Estado</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Pedidos</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Último Acceso</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Registro</th>
                                <th style="padding: 1rem; text-align: left; color: var(--text-primary); font-weight: 600; border-bottom: 1px solid var(--border-color);">Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <?php
                                $is_banned = $user['locked_until'] && strtotime($user['locked_until']) > time();
                                $is_current_user = $user['id'] == $_SESSION['user_id'];
                            ?>
                            <tr style="border-bottom: 1px solid var(--border-color); <?php echo $is_current_user ? 'background: rgba(37, 99, 235, 0.1);' : ''; ?>">
                                <td style="padding: 1rem; color: var(--text-secondary);">
                                    <strong>#<?php echo $user['id']; ?></strong>
                                    <?php if ($is_current_user): ?>
                                    <span style="color: var(--primary-color); font-size: 0.8rem; margin-left: 0.5rem;">(Tú)</span>
                                    <?php endif; ?>
                                </td>
                                <td style="padding: 1rem;">
                                    <div style="color: var(--text-primary); font-weight: 500;"><?php echo htmlspecialchars($user['username']); ?></div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                        Intentos fallidos: <?php echo $user['failed_attempts'] ?? 0; ?>
                                    </div>
                                </td>
                                <td style="padding: 1rem;">
                                    <?php if ($user['is_admin']): ?>
                                    <span style="background: rgba(37, 99, 235, 0.2); color: var(--primary-color); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; font-weight: 500;">
                                        <i class="fas fa-crown"></i> Admin
                                    </span>
                                    <?php else: ?>
                                    <span style="background: rgba(107, 114, 128, 0.2); color: var(--text-secondary); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                        <i class="fas fa-user"></i> Usuario
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td style="padding: 1rem;">
                                    <?php if ($is_banned): ?>
                                    <span style="background: rgba(239, 68, 68, 0.2); color: var(--error-color); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; font-weight: 500;">
                                        <i class="fas fa-ban"></i> Baneado
                                    </span>
                                    <div style="font-size: 0.7rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                        Hasta: <?php echo date('d/m/Y H:i', strtotime($user['locked_until'])); ?>
                                    </div>
                                    <?php else: ?>
                                    <span style="background: rgba(16, 185, 129, 0.2); color: var(--success-color); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                                        <i class="fas fa-check"></i> Activo
                                    </span>
                                    <?php endif; ?>
                                </td>
                                <td style="padding: 1rem; color: var(--text-secondary);">
                                    <div style="font-weight: 500; color: var(--text-primary);"><?php echo $user['total_orders']; ?></div>
                                    <?php if ($user['last_order_date']): ?>
                                    <div style="font-size: 0.8rem; margin-top: 0.25rem;">
                                        Último: <?php echo date('d/m/Y', strtotime($user['last_order_date'])); ?>
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td style="padding: 1rem; color: var(--text-secondary);">
                                    <?php if ($user['last_login']): ?>
                                    <div><?php echo date('d/m/Y', strtotime($user['last_login'])); ?></div>
                                    <div style="font-size: 0.8rem;"><?php echo date('H:i', strtotime($user['last_login'])); ?></div>
                                    <?php else: ?>
                                    <span style="color: var(--text-secondary);">Nunca</span>
                                    <?php endif; ?>
                                </td>
                                <td style="padding: 1rem; color: var(--text-secondary);">
                                    <div><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></div>
                                    <div style="font-size: 0.8rem;"><?php echo date('H:i', strtotime($user['created_at'])); ?></div>
                                </td>
                                <td style="padding: 1rem;">
                                    <?php if (!$is_current_user): ?>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <!-- Resetear contraseña -->
                                        <button onclick="resetUserPassword(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')"
                                                style="padding: 0.25rem 0.5rem; background: var(--warning-color); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;"
                                                title="Resetear contraseña">
                                            <i class="fas fa-key"></i>
                                        </button>

                                        <!-- Banear/Desbanear -->
                                        <?php if ($is_banned): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="unban_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit"
                                                    style="padding: 0.25rem 0.5rem; background: var(--success-color); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;"
                                                    title="Desbanear usuario"
                                                    onclick="return confirm('¿Desbanear a <?php echo htmlspecialchars($user['username']); ?>?')">
                                                <i class="fas fa-unlock"></i>
                                            </button>
                                        </form>
                                        <?php else: ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="ban_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit"
                                                    style="padding: 0.25rem 0.5rem; background: var(--error-color); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;"
                                                    title="Banear usuario"
                                                    onclick="return confirm('¿Banear a <?php echo htmlspecialchars($user['username']); ?> por 30 días?')">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>

                                        <!-- Toggle Admin -->
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_admin">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit"
                                                    style="padding: 0.25rem 0.5rem; background: <?php echo $user['is_admin'] ? 'var(--info-color)' : 'var(--primary-color)'; ?>; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;"
                                                    title="<?php echo $user['is_admin'] ? 'Quitar admin' : 'Hacer admin'; ?>"
                                                    onclick="return confirm('¿<?php echo $user['is_admin'] ? 'Quitar permisos de admin' : 'Dar permisos de admin'; ?> a <?php echo htmlspecialchars($user['username']); ?>?')">
                                                <i class="fas fa-<?php echo $user['is_admin'] ? 'user-minus' : 'user-shield'; ?>"></i>
                                            </button>
                                        </form>

                                        <!-- Eliminar -->
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="delete_user">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit"
                                                    style="padding: 0.25rem 0.5rem; background: #dc2626; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;"
                                                    title="Eliminar usuario"
                                                    onclick="return confirm('¿ELIMINAR PERMANENTEMENTE a <?php echo htmlspecialchars($user['username']); ?>? Esta acción no se puede deshacer.')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                    <?php else: ?>
                                    <span style="color: var(--text-secondary); font-size: 0.8rem;">Usuario actual</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <script>
        let refreshInterval;
        let isUpdating = false;

        // Función para expandir/contraer secciones
        function toggleSection(sectionName) {
            const toggle = document.getElementById(`toggle-${sectionName}`);
            const content = document.getElementById(`content-${sectionName}`);

            if (toggle && content) {
                const isExpanded = toggle.classList.contains('expanded');

                if (isExpanded) {
                    // Contraer
                    toggle.classList.remove('expanded');
                    content.classList.remove('expanded');
                } else {
                    // Expandir
                    toggle.classList.add('expanded');
                    content.classList.add('expanded');
                }
            }
        }

        // Sistema inteligente de notificaciones sin interrumpir el trabajo
        let lastOrderCount = 0;
        let lastStats = {};
        let userIsActive = false;
        let lastActivityTime = Date.now();

        async function refreshOrders() {
            if (isUpdating) return;

            isUpdating = true;

            try {
                // Obtener estadísticas actuales
                const response = await fetch('api_notifications.php?action=check_new_orders');
                const data = await response.json();

                if (data.new_orders !== undefined) {
                    const currentOrderCount = data.new_orders;

                    console.log(`📊 Estadísticas: Anterior: ${lastOrderCount}, Actual: ${currentOrderCount}`);

                    // Detectar nuevos pedidos
                    if (lastOrderCount >= 0 && currentOrderCount > lastOrderCount) {
                        const newOrdersReceived = currentOrderCount - lastOrderCount;

                        // Mostrar notificación de nuevos pedidos
                        showNotification(
                            `🔔 ${newOrdersReceived} nuevo${newOrdersReceived > 1 ? 's' : ''} pedido${newOrdersReceived > 1 ? 's' : ''} recibido${newOrdersReceived > 1 ? 's' : ''}`,
                            'success'
                        );

                        // SIEMPRE actualizar la sección de pedidos recibidos cuando hay nuevos pedidos
                        const newSection = document.getElementById('content-new');
                        const newToggle = document.getElementById('toggle-new');

                        if (newSection && newToggle) {
                            // Abrir la sección automáticamente si está cerrada
                            if (!newToggle.classList.contains('expanded')) {
                                newToggle.classList.add('expanded');
                                newSection.style.display = 'block';
                                newSection.classList.add('expanded');
                            }

                            // Mostrar indicador de actualización
                            const indicator = document.createElement('div');
                            indicator.style.cssText = `
                                position: fixed;
                                top: 50%;
                                right: 20px;
                                background: var(--accent-color);
                                color: var(--primary-color);
                                padding: 0.5rem 1rem;
                                border-radius: 20px;
                                font-size: 0.8rem;
                                z-index: 9999;
                                animation: slideIn 0.3s ease;
                            `;
                            indicator.textContent = '🔄 Cargando nuevos pedidos...';
                            document.body.appendChild(indicator);

                            // Actualizar contenido de la sección
                            setTimeout(() => {
                                refreshSectionContent('new');
                                indicator.remove();

                                // Scroll suave hacia la sección de nuevos pedidos
                                setTimeout(() => {
                                    newToggle.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'start'
                                    });
                                }, 300);
                            }, 500);
                        }
                    }

                    // Actualizar estadísticas en las tarjetas
                    document.getElementById('newOrdersCount').textContent = currentOrderCount;
                    updateStatsCards();

                    lastOrderCount = currentOrderCount;
                }

            } catch (error) {
                console.error('Error refreshing orders:', error);
            } finally {
                isUpdating = false;
            }
        }

        // Función para actualizar estadísticas en las tarjetas clickeables
        function updateStatsCards() {
            // Actualizar las animaciones parpadeantes basadas en los contadores
            const newCount = parseInt(document.getElementById('newOrdersCount').textContent);
            const pendingCount = parseInt(document.getElementById('pendingOrdersCount').textContent);

            const newCard = document.querySelector('.stat-card.new-orders');
            const pendingCard = document.querySelector('.stat-card.pending');

            if (newCard) {
                if (newCount > 0) {
                    newCard.classList.add('has-new');
                } else {
                    newCard.classList.remove('has-new');
                }
            }

            if (pendingCard) {
                if (pendingCount > 0) {
                    pendingCard.classList.add('has-pending');
                } else {
                    pendingCard.classList.remove('has-pending');
                }
            }
        }

        // Función para actualizar estadísticas
        async function updateStats() {
            try {
                const response = await fetch('api_notifications.php?action=check_new_orders');
                const data = await response.json();

                if (data.new_orders !== undefined) {
                    document.getElementById('newOrdersCount').textContent = data.new_orders;
                    // Actualizar animaciones después de cambiar los números
                    updateStatsCards();
                }


            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        // Función para actualizar estado de pedido
        async function updateOrderStatus(orderId, newStatus) {
            try {
                const response = await fetch('api_notifications.php?action=update_order_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=${orderId}&status=${encodeURIComponent(newStatus)}`
                });

                const data = await response.json();

                if (data.success) {
                    // Remover el pedido de la sección actual
                    const row = document.querySelector(`tr[data-order-id="${orderId}"]`);
                    if (row) {
                        // Animación de salida
                        row.style.transition = 'all 0.3s ease';
                        row.style.transform = 'translateX(100%)';
                        row.style.opacity = '0';

                        setTimeout(() => {
                            row.remove();

                            // Verificar si la tabla quedó vacía
                            const table = row.closest('.orders-table-container');
                            if (table) {
                                const tbody = table.querySelector('tbody');
                                if (tbody && tbody.children.length === 0) {
                                    table.innerHTML = '<div style="padding: 2rem; text-align: center; color: var(--text-secondary);"><i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i><p>No hay pedidos en esta categoría</p></div>';
                                }
                            }
                        }, 300);
                    }

                    // Mostrar notificación de éxito con información de destino
                    const statusMessages = {
                        'Recibido': 'Movido a Pedidos Recibidos',
                        'Pendiente': 'Movido a Pedidos Pendientes',
                        'En Cola': 'Movido a Pedidos Pendientes',
                        'Procesando': 'Movido a Procesando',
                        'Ya en existencia': 'Movido a Ya en Existencia',
                        'Listo': 'Movido a Completados',
                        'No disponible': 'Movido a No Disponibles'
                    };

                    const message = statusMessages[newStatus] || 'Estado actualizado correctamente';
                    showNotification(message, 'success');

                    // Actualizar estadísticas sin recargar página
                    setTimeout(() => {
                        updateStats();
                        // También actualizar la sección actual para reflejar el cambio inmediatamente
                        const currentSection = row.closest('.orders-content');
                        if (currentSection) {
                            const sectionId = currentSection.id.replace('content-', '');
                            refreshSectionContent(sectionId);
                        }
                    }, 500);

                    // Abrir automáticamente la sección correspondiente y refrescar contenido
                    setTimeout(() => {
                        const sectionMap = {
                            'Recibido': 'new',
                            'Pendiente': 'pending',
                            'En Cola': 'pending',
                            'Procesando': 'processing',
                            'Ya en existencia': 'existing',
                            'Listo': 'completed',
                            'No disponible': 'unavailable'
                        };

                        const targetSection = sectionMap[newStatus];
                        if (targetSection) {
                            // Abrir la sección destino si no está abierta
                            const targetToggle = document.getElementById('toggle-' + targetSection);
                            const targetContent = document.getElementById('content-' + targetSection);

                            if (targetToggle && targetContent) {
                                // Solo abrir si está cerrada
                                if (!targetToggle.classList.contains('expanded')) {
                                    targetToggle.classList.add('expanded');
                                    targetContent.style.display = 'block';
                                }

                                // Refrescar el contenido de la sección destino
                                refreshSectionContent(targetSection);

                                // Scroll suave hacia la sección solo si no está visible
                                setTimeout(() => {
                                    const rect = targetToggle.getBoundingClientRect();
                                    const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

                                    if (!isVisible) {
                                        targetToggle.scrollIntoView({
                                            behavior: 'smooth',
                                            block: 'start'
                                        });
                                    }
                                }, 500);
                            }
                        }
                    }, 800);

                } else {
                    showNotification('Error al actualizar estado', 'error');
                }
            } catch (error) {
                console.error('Error updating order status:', error);
                showNotification('Error de conexión', 'error');
            }
        }

        // Función para refrescar el contenido de una sección específica
        async function refreshSectionContent(sectionType, silent = false) {
            try {
                const contentElement = document.getElementById('content-' + sectionType);
                if (contentElement) {
                    // Mostrar indicador de carga solo si no es silencioso
                    const originalContent = contentElement.innerHTML;

                    if (!silent) {
                        contentElement.style.opacity = '0.6';
                        contentElement.style.pointerEvents = 'none';
                    }

                    const response = await fetch(`api_notifications.php?action=get_section_orders&section=${sectionType}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.success && data.html) {
                        contentElement.innerHTML = data.html;

                        if (!silent) {
                            // Animación de entrada
                            contentElement.style.opacity = '0';
                            setTimeout(() => {
                                contentElement.style.transition = 'opacity 0.3s ease';
                                contentElement.style.opacity = '1';
                                contentElement.style.pointerEvents = 'auto';
                            }, 100);
                        } else {
                            // Actualización silenciosa
                            contentElement.style.opacity = '1';
                            contentElement.style.pointerEvents = 'auto';
                        }

                        console.log(`✅ Sección ${sectionType} actualizada correctamente`);
                    } else {
                        console.error('Error en respuesta del servidor:', data);
                        // Restaurar contenido original en caso de error
                        contentElement.innerHTML = originalContent;
                        contentElement.style.opacity = '1';
                        contentElement.style.pointerEvents = 'auto';
                    }
                }
            } catch (error) {
                console.error('Error refreshing section content:', error);
                // Restaurar estado en caso de error
                const contentElement = document.getElementById('content-' + sectionType);
                if (contentElement) {
                    contentElement.style.opacity = '1';
                    contentElement.style.pointerEvents = 'auto';
                }

                // Mostrar notificación de error solo si no es silencioso
                if (!silent) {
                    showNotification('Error al actualizar contenido', 'error');
                }
            }
        }

        // Función para mostrar notificaciones
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? 'var(--success-color)' : type === 'error' ? 'var(--error-color)' : 'var(--primary-color)'};
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Variables de control
        let autoRefreshEnabled = true;

        // Detectar actividad del usuario
        function trackUserActivity() {
            lastActivityTime = Date.now();
            userIsActive = true;

            // Marcar como inactivo después de 5 segundos sin actividad
            setTimeout(() => {
                if (Date.now() - lastActivityTime >= 5000) {
                    userIsActive = false;
                }
            }, 5000);
        }

        // Función para refresh manual
        async function manualRefresh() {
            const refreshIcon = document.getElementById('refreshIcon');
            refreshIcon.style.animation = 'spin 1s linear infinite';

            try {
                await refreshOrders();
                showNotification('✅ Pedidos actualizados', 'success');
            } catch (error) {
                showNotification('❌ Error al actualizar', 'error');
            } finally {
                refreshIcon.style.animation = '';
            }
        }

        // Función para toggle del auto-refresh
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;

            const btn = document.getElementById('autoRefreshBtn');
            const icon = document.getElementById('autoRefreshIcon');
            const text = document.getElementById('autoRefreshText');
            const indicator = document.getElementById('refreshIndicator');
            const status = document.getElementById('autoRefreshStatus');

            if (autoRefreshEnabled) {
                btn.style.background = 'rgba(70, 211, 71, 0.2)';
                btn.style.color = 'var(--accent-color)';
                icon.className = 'fas fa-pause';
                text.textContent = 'Pausar';
                indicator.style.display = 'block';
                status.textContent = 'Auto-actualización inteligente';
                showNotification('🔄 Auto-refresh activado', 'success');
            } else {
                btn.style.background = 'rgba(220, 53, 69, 0.2)';
                btn.style.color = '#dc3545';
                icon.className = 'fas fa-play';
                text.textContent = 'Reanudar';
                indicator.style.display = 'none';
                status.textContent = 'Auto-actualización pausada';
                showNotification('⏸️ Auto-refresh pausado', 'warning');
            }
        }

        // Función mejorada de refresh que respeta la actividad del usuario
        async function smartRefreshOrders() {
            // No hacer nada si el auto-refresh está desactivado
            if (!autoRefreshEnabled) {
                return;
            }

            // Si el usuario está activo, actualizar silenciosamente pero también refrescar contenido
            if (userIsActive) {
                try {
                    const response = await fetch('api_notifications.php?action=check_new_orders');
                    const data = await response.json();

                    if (data.new_orders !== undefined) {
                        // Solo actualizar el contador silenciosamente
                        const currentCount = parseInt(document.getElementById('newOrdersCount').textContent);
                        const newCount = data.new_orders;

                        if (newCount > currentCount) {
                            console.log(`🔔 Refresh silencioso: ${newCount - currentCount} nuevos pedidos detectados`);

                            // Mostrar solo un pequeño indicador visual sutil
                            const badge = document.querySelector('.stat-card.new-orders');
                            if (badge) {
                                badge.style.animation = 'pulse 0.5s ease';
                                setTimeout(() => badge.style.animation = '', 500);
                            }

                            // Mostrar notificación muy discreta
                            showDiscreteNotification(`+${newCount - currentCount} nuevo(s)`);

                            // Actualizar silenciosamente el contenido de la sección de nuevos pedidos
                            const newSection = document.getElementById('content-new');
                            if (newSection) {
                                console.log(`🔄 Actualizando contenido de sección 'new' silenciosamente`);
                                refreshSectionContent('new', true); // true = silencioso
                            }
                        }

                        document.getElementById('newOrdersCount').textContent = newCount;
                        updateStatsCards();
                        lastOrderCount = newCount;
                    }
                } catch (error) {
                    console.error('Error in silent refresh:', error);
                }
            } else {
                // Usuario inactivo, hacer refresh completo pero sin interrumpir
                await refreshOrders();
            }
        }

        // Función para mostrar notificaciones discretas
        function showDiscreteNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(70, 211, 71, 0.9);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.8rem;
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                pointer-events: none;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // Mostrar
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Ocultar después de 2 segundos
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 2000);
        }

        // Inicialización
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-refresh inteligente cada 30 segundos
            refreshInterval = setInterval(smartRefreshOrders, 30000);

            // Actualizar estadísticas iniciales
            updateStats();

            // Activar animaciones iniciales
            updateStatsCards();

            // Detectores de actividad del usuario
            ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
                document.addEventListener(event, trackUserActivity, { passive: true });
            });

            // Inicializar contador
            const initialCount = parseInt(document.getElementById('newOrdersCount').textContent) || 0;
            lastOrderCount = initialCount;

            console.log(`🚀 Sistema iniciado. Contador inicial: ${lastOrderCount}`);

            // Mostrar notificación de inicio
            showNotification('🔄 Sistema de notificaciones inteligente activado', 'success');
        });

        // Limpiar interval al salir
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });

        // Agregar estilos para animaciones
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- Sistema de Notificaciones en Tiempo Real (Deshabilitado temporalmente) -->
    <!-- <script src="realtime_notifications.js"></script> -->
    <script>
        // Configuración específica para admin.php
        document.addEventListener('DOMContentLoaded', function() {
            // Sistema de notificaciones simplificado sin WebSocket
            console.log('📢 Admin - Sistema de notificaciones simplificado activo');

            // Actualizar estadísticas de soporte cada 10 segundos
            setInterval(loadSupportStats, 10000);
            loadSupportStats(); // Cargar inmediatamente
        });

        // Función para cargar estadísticas de soporte
        async function loadSupportStats() {
            try {
                const response = await fetch('api_support_stats.php');
                const data = await response.json();

                if (data.success && data.stats) {
                    // Actualizar cartas de soporte
                    document.getElementById('supportChatCount').textContent = data.stats.chat_active || 0;
                    document.getElementById('supportTicketsCount').textContent = data.stats.tickets_open || 0;
                    document.getElementById('supportChannelsCount').textContent = data.stats.channels_pending || 0;
                    document.getElementById('supportAppsCount').textContent = data.stats.downloads_today || 0;

                    // Animar cartas si hay actividad
                    const chatCard = document.querySelector('#supportChatCount').closest('.support-card');
                    const ticketsCard = document.querySelector('#supportTicketsCount').closest('.support-card');
                    const channelsCard = document.querySelector('#supportChannelsCount').closest('.support-card');

                    // Remover clases anteriores
                    [chatCard, ticketsCard, channelsCard].forEach(card => {
                        if (card) card.classList.remove('has-activity');
                    });

                    // Agregar animaciones si hay actividad
                    if (data.stats.chat_active > 0 && chatCard) {
                        chatCard.classList.add('has-activity');
                    }
                    if (data.stats.tickets_open > 0 && ticketsCard) {
                        ticketsCard.classList.add('has-activity');
                    }
                    if (data.stats.channels_pending > 0 && channelsCard) {
                        channelsCard.classList.add('has-activity');
                    }

                    // Mostrar indicador de conexión activa
                    updateConnectionStatus(true);
                }
            } catch (error) {
                console.error('Error loading support stats:', error);
                updateConnectionStatus(false);
            }
        }

        // Función para actualizar el estado de conexión
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                if (connected) {
                    statusElement.innerHTML = `
                        <span class="status-indicator" style="background: #10b981; animation: pulse 2s infinite;"></span>
                        <span class="status-text" style="color: #10b981;">Conectado</span>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <span class="status-indicator" style="background: #ef4444;"></span>
                        <span class="status-text" style="color: #ef4444;">Desconectado</span>
                        <button onclick="loadSupportStats()" style="margin-left: 0.5rem; padding: 0.25rem 0.5rem; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">
                            Reconectar
                        </button>
                    `;
                }
            }
        }

        // Función para mostrar/ocultar la sección de soporte
        function toggleSupportSection(type) {
            const supportSection = document.getElementById('support-section');
            const userManagementSection = document.getElementById('user-management-section');
            const ordersSection = document.querySelector('.orders-section:not(#support-section):not(#user-management-section)');

            // Ocultar otras secciones
            userManagementSection.style.display = 'none';

            if (supportSection.style.display === 'none' || !supportSection.style.display) {
                // Mostrar sección de soporte
                supportSection.style.display = 'block';
                ordersSection.style.display = 'none';

                // Scroll hacia la sección
                supportSection.scrollIntoView({ behavior: 'smooth' });

                // Resaltar la carta correspondiente
                highlightSupportCard(type);
            } else {
                // Si ya está visible, solo resaltar la carta
                highlightSupportCard(type);
            }
        }

        // Función para mostrar/ocultar la sección de administración de usuarios
        function toggleUserManagement() {
            const userManagementSection = document.getElementById('user-management-section');
            const supportSection = document.getElementById('support-section');
            const ordersSection = document.querySelector('.orders-section:not(#support-section):not(#user-management-section)');

            // Ocultar otras secciones
            supportSection.style.display = 'none';

            if (userManagementSection.style.display === 'none' || !userManagementSection.style.display) {
                // Mostrar sección de administración de usuarios
                userManagementSection.style.display = 'block';
                ordersSection.style.display = 'none';

                // Scroll hacia la sección
                userManagementSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function closeSupportSection() {
            const supportSection = document.getElementById('support-section');
            const ordersSection = document.querySelector('.orders-section:not(#support-section):not(#user-management-section)');

            supportSection.style.display = 'none';
            ordersSection.style.display = 'block';

            // Scroll hacia las estadísticas
            document.querySelector('.stats-section').scrollIntoView({ behavior: 'smooth' });
        }

        function closeUserManagement() {
            const userManagementSection = document.getElementById('user-management-section');
            const ordersSection = document.querySelector('.orders-section:not(#support-section):not(#user-management-section)');

            userManagementSection.style.display = 'none';
            ordersSection.style.display = 'block';

            // Scroll hacia las estadísticas
            document.querySelector('.stats-section').scrollIntoView({ behavior: 'smooth' });
        }

        // Función para resetear contraseña de usuario
        function resetUserPassword(userId, username) {
            const newPassword = prompt(`Ingresa la nueva contraseña para ${username}:`);

            if (newPassword && newPassword.length >= 6) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'reset_password';

                const userIdInput = document.createElement('input');
                userIdInput.type = 'hidden';
                userIdInput.name = 'user_id';
                userIdInput.value = userId;

                const passwordInput = document.createElement('input');
                passwordInput.type = 'hidden';
                passwordInput.name = 'new_password';
                passwordInput.value = newPassword;

                form.appendChild(actionInput);
                form.appendChild(userIdInput);
                form.appendChild(passwordInput);

                document.body.appendChild(form);
                form.submit();
            } else if (newPassword !== null) {
                alert('La contraseña debe tener al menos 6 caracteres');
            }
        }

        function highlightSupportCard(type) {
            // Remover resaltado anterior
            document.querySelectorAll('.service-card').forEach(card => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });

            // Resaltar carta específica
            const cardMap = {
                'chat': 0,
                'tickets': 1,
                'channels': 2,
                'apps': 3
            };

            const cardIndex = cardMap[type];
            if (cardIndex !== undefined) {
                const cards = document.querySelectorAll('.service-card');
                if (cards[cardIndex]) {
                    cards[cardIndex].style.transform = 'translateY(-10px) scale(1.02)';
                    cards[cardIndex].style.boxShadow = '0 8px 32px rgba(233, 30, 99, 0.3)';

                    setTimeout(() => {
                        cards[cardIndex].style.transform = '';
                        cards[cardIndex].style.boxShadow = '';
                    }, 2000);
                }
            }
        }

        // Función para resaltar el botón de Admin Soporte cuando llegan notificaciones (mantenida para compatibilidad)
        function highlightAdmin2Button() {
            // Ahora abre la sección integrada en lugar del admin2.php
            toggleSupportSection('chat');
        }

        // ===== SISTEMA DE BANDEJAS =====
        let currentInboxIndex = 0;
        const inboxes = ['new', 'pending', 'processing', 'completed', 'existing', 'unavailable'];

        // Función para cambiar de bandeja
        function switchInbox(inboxType) {
            const index = inboxes.indexOf(inboxType);
            if (index !== -1) {
                currentInboxIndex = index;
                updateInboxDisplay();
                updateInboxTabs();
                updateNavigationButtons();
            }
        }

        // Función para ir a la bandeja anterior
        function previousInbox() {
            if (currentInboxIndex > 0) {
                currentInboxIndex--;
                const inboxType = inboxes[currentInboxIndex];
                updateInboxDisplay();
                updateInboxTabs();
                updateNavigationButtons();
            }
        }

        // Función para ir a la siguiente bandeja
        function nextInbox() {
            if (currentInboxIndex < inboxes.length - 1) {
                currentInboxIndex++;
                const inboxType = inboxes[currentInboxIndex];
                updateInboxDisplay();
                updateInboxTabs();
                updateNavigationButtons();
            }
        }

        // Función para actualizar la visualización de las bandejas
        function updateInboxDisplay() {
            const slider = document.getElementById('inboxSlider');
            if (slider) {
                const translateX = -(currentInboxIndex * 16.666); // 100% / 6 bandejas
                slider.style.transform = `translateX(${translateX}%)`;
            }
        }

        // Función para actualizar las pestañas activas
        function updateInboxTabs() {
            const tabs = document.querySelectorAll('.inbox-tab');
            tabs.forEach((tab, index) => {
                if (index === currentInboxIndex) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
        }

        // Función para actualizar los botones de navegación
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (prevBtn) {
                prevBtn.disabled = currentInboxIndex === 0;
            }

            if (nextBtn) {
                nextBtn.disabled = currentInboxIndex === inboxes.length - 1;
            }
        }

        // Función para marcar todos como leídos
        function markAllAsRead(inboxType) {
            // Aquí puedes agregar la lógica para marcar como leídos
            console.log(`Marcando todos los pedidos de ${inboxType} como leídos`);

            // Mostrar confirmación
            const confirmation = confirm(`¿Marcar todos los pedidos de ${inboxType} como leídos?`);
            if (confirmation) {
                // Implementar la lógica de marcado
                alert(`Todos los pedidos de ${inboxType} han sido marcados como leídos`);
            }
        }

        // Función para actualizar una bandeja específica
        function refreshInbox(inboxType) {
            console.log(`Actualizando bandeja: ${inboxType}`);

            // Mostrar indicador de carga
            const actionBtn = event.target;
            const originalIcon = actionBtn.innerHTML;
            actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Simular actualización (aquí puedes hacer una llamada AJAX)
            setTimeout(() => {
                actionBtn.innerHTML = originalIcon;
                // Aquí recargarías el contenido de la bandeja específica
                manualRefresh(); // Por ahora usa la función de actualización general
            }, 1000);
        }

        // Función para navegar con teclado
        function handleKeyboardNavigation(event) {
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.tagName === 'SELECT') {
                return; // No interferir con campos de entrada
            }

            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    previousInbox();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    nextInbox();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                    event.preventDefault();
                    const index = parseInt(event.key) - 1;
                    if (index >= 0 && index < inboxes.length) {
                        currentInboxIndex = index;
                        updateInboxDisplay();
                        updateInboxTabs();
                        updateNavigationButtons();
                    }
                    break;
            }
        }

        // Función para actualizar contadores de bandejas
        function updateInboxCounts(stats) {
            if (stats) {
                const countElements = {
                    'new': document.getElementById('inbox-count-new'),
                    'pending': document.getElementById('inbox-count-pending'),
                    'processing': document.getElementById('inbox-count-processing'),
                    'completed': document.getElementById('inbox-count-completed'),
                    'existing': document.getElementById('inbox-count-existing'),
                    'unavailable': document.getElementById('inbox-count-unavailable')
                };

                // Actualizar contadores
                if (countElements.new) countElements.new.textContent = stats.new_orders || 0;
                if (countElements.pending) countElements.pending.textContent = stats.pending_orders || 0;
                if (countElements.processing) countElements.processing.textContent = stats.processing_orders || 0;
                if (countElements.completed) countElements.completed.textContent = stats.completed_orders || 0;
                if (countElements.existing) countElements.existing.textContent = stats.existing_orders || 0;
                if (countElements.unavailable) countElements.unavailable.textContent = stats.unavailable_orders || 0;
            }
        }

        // Inicializar sistema de bandejas
        function initializeInboxSystem() {
            updateInboxDisplay();
            updateInboxTabs();
            updateNavigationButtons();

            // Agregar event listeners para navegación con teclado
            document.addEventListener('keydown', handleKeyboardNavigation);

            // Agregar tooltips para atajos de teclado
            const tabs = document.querySelectorAll('.inbox-tab');
            tabs.forEach((tab, index) => {
                const originalTitle = tab.title || '';
                tab.title = `${originalTitle} (Atajo: ${index + 1})`.trim();
            });
        }

        // Función modificada para integrar con el sistema existente
        function toggleSection(sectionType) {
            // Convertir el sistema anterior al nuevo sistema de bandejas
            switchInbox(sectionType);
        }

        // Cargar estadísticas de soporte al inicio
        loadSupportStats();

        // Actualizar estadísticas de soporte cada 30 segundos
        setInterval(loadSupportStats, 30000);

        // Inicializar sistema de bandejas cuando se carga la página
        document.addEventListener('DOMContentLoaded', function() {
            initializeInboxSystem();
        });

        // Si el DOM ya está cargado, inicializar inmediatamente
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeInboxSystem);
        } else {
            initializeInboxSystem();
        }
    </script>

    <!-- Widget de Notificaciones de Soporte -->
    <?php include 'admin_notifications_widget.php'; ?>
</body>
</html>
