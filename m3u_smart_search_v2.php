<?php
// Búsqueda inteligente TMDB v2 - Con validación de coincidencias
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para calcular similitud entre títulos
function calculateSimilarity($title1, $title2) {
    $title1 = strtolower(trim($title1));
    $title2 = strtolower(trim($title2));
    
    // Quitar acentos para comparación
    $title1 = iconv('UTF-8', 'ASCII//TRANSLIT', $title1);
    $title2 = iconv('UTF-8', 'ASCII//TRANSLIT', $title2);
    
    // Similitud exacta
    if ($title1 === $title2) return 100;
    
    // Similitud por palabras clave
    $words1 = explode(' ', $title1);
    $words2 = explode(' ', $title2);
    
    $common_words = array_intersect($words1, $words2);
    $total_words = array_unique(array_merge($words1, $words2));
    
    if (count($total_words) == 0) return 0;
    
    $similarity = (count($common_words) / count($total_words)) * 100;
    
    // Bonus si el título original está contenido en el encontrado
    if (strpos($title2, $title1) !== false || strpos($title1, $title2) !== false) {
        $similarity += 20;
    }
    
    return min(100, $similarity);
}

// Función de limpieza inteligente de títulos
function smartCleanTitle($title) {
    $variations = [];
    
    // 1. Título original
    $variations['original'] = $title;
    
    // 2. Quitar año
    $no_year = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    if ($no_year !== $title) $variations['no_year'] = $no_year;
    
    // 3. Quitar episodios
    $no_episodes = trim(preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/', '', $no_year));
    if ($no_episodes !== $no_year) $variations['no_episodes'] = $no_episodes;
    
    // 4. Quitar calidad y idioma
    $no_quality = trim(preg_replace('/\s*(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV|CAM|TS|DVDRip|BRRip|DUAL|LATINO|SPANISH|ENGLISH).*$/i', '', $no_episodes));
    if ($no_quality !== $no_episodes) $variations['no_quality'] = $no_quality;
    
    // 5. Quitar corchetes y paréntesis
    $no_brackets = $no_quality;
    $no_brackets = trim(preg_replace('/\s*\[.*?\]/', '', $no_brackets));
    $no_brackets = trim(preg_replace('/\s*\(.*?\)/', '', $no_brackets));
    if ($no_brackets !== $no_quality) $variations['no_brackets'] = $no_brackets;
    
    // 6. Solo caracteres alfanuméricos y espacios
    $clean = trim(preg_replace('/\s+/', ' ', preg_replace('/[^\w\s]/', ' ', $no_brackets)));
    if ($clean !== $no_brackets) $variations['clean'] = $clean;
    
    // Filtrar variaciones válidas (mínimo 3 caracteres)
    return array_filter($variations, function($v) {
        return strlen(trim($v)) >= 3;
    });
}

// Función de búsqueda inteligente con validación
function smartTMDBSearchWithValidation($title) {
    $variations = smartCleanTitle($title);
    $languages = ['es-MX', 'en-US', 'es-ES'];
    $best_match = null;
    $best_similarity = 0;
    
    foreach ($variations as $type => $clean_title) {
        foreach ($languages as $lang) {
            $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=$lang&query=" . urlencode($clean_title);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 8);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 && $response) {
                $data = json_decode($response, true);
                if ($data && isset($data['results']) && !empty($data['results'])) {
                    // Evaluar los primeros 3 resultados
                    foreach (array_slice($data['results'], 0, 3) as $result) {
                        $found_title = $result['title'] ?? $result['name'];
                        $similarity = calculateSimilarity($title, $found_title);
                        
                        // Solo considerar si la similitud es razonable
                        if ($similarity >= 40 && $similarity > $best_similarity) {
                            $best_similarity = $similarity;
                            $best_match = [
                                'result' => $result,
                                'variation_used' => $type,
                                'language_used' => $lang,
                                'search_term' => $clean_title,
                                'similarity' => $similarity
                            ];
                        }
                    }
                }
            }
            
            usleep(100000); // 0.1 segundos entre peticiones
        }
    }
    
    // Solo devolver si la similitud es suficientemente alta
    return ($best_similarity >= 50) ? $best_match : null;
}

$message = '';
$error = '';

if (isset($_POST['smart_search']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    $batch_size = (int)($_POST['batch_size'] ?? 10);
    $min_similarity = (int)($_POST['min_similarity'] ?? 50);
    
    try {
        // Obtener solo películas y series
        $stmt = $pdo->prepare("
            SELECT id, title, url, media_type
            FROM m3u_content 
            WHERE list_id = ? 
            AND (tmdb_id IS NULL OR tmdb_id = 0)
            AND (
                media_type IN ('movie', 'tv') 
                OR url REGEXP '/movie/' 
                OR url REGEXP '/series/'
                OR (url REGEXP '\\.(mkv|mp4|avi|m4v)$' AND url NOT REGEXP '/live/')
            )
            ORDER BY 
                CASE 
                    WHEN url REGEXP '/movie/' THEN 1
                    WHEN url REGEXP '/series/' THEN 2
                    WHEN title REGEXP '(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV)' THEN 4
                    ELSE 3
                END,
                LENGTH(title),
                id
            LIMIT $batch_size
        ");
        $stmt->execute([$list_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $processed = 0;
        $found = 0;
        $rejected = 0;
        $errors = 0;
        $search_details = [];
        
        foreach ($items as $item) {
            $processed++;
            
            try {
                $search_result = smartTMDBSearchWithValidation($item['title']);
                
                if ($search_result && $search_result['similarity'] >= $min_similarity) {
                    $found++;
                    $tmdb_result = $search_result['result'];
                    
                    // Guardar detalles para mostrar
                    $search_details[] = [
                        'original_title' => $item['title'],
                        'search_term' => $search_result['search_term'],
                        'variation' => $search_result['variation_used'],
                        'language' => $search_result['language_used'],
                        'found_title' => $tmdb_result['title'] ?? $tmdb_result['name'],
                        'tmdb_id' => $tmdb_result['id'],
                        'similarity' => $search_result['similarity']
                    ];
                    
                    // Actualizar base de datos
                    $update_stmt = $pdo->prepare("
                        UPDATE m3u_content SET 
                            tmdb_id = ?,
                            tmdb_title = ?,
                            tmdb_poster_path = ?,
                            media_type = ?
                        WHERE id = ?
                    ");
                    
                    $media_type = 'unknown';
                    if (isset($tmdb_result['media_type'])) {
                        $media_type = $tmdb_result['media_type'] === 'movie' ? 'movie' : 'tv';
                    }
                    
                    $update_stmt->execute([
                        $tmdb_result['id'],
                        $tmdb_result['title'] ?? $tmdb_result['name'],
                        $tmdb_result['poster_path'],
                        $media_type,
                        $item['id']
                    ]);
                } elseif ($search_result) {
                    $rejected++;
                }
                
            } catch (Exception $e) {
                $errors++;
            }
            
            // Pausa entre elementos
            usleep(300000); // 0.3 segundos
        }
        
        // Contar pendientes
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM m3u_content 
            WHERE list_id = ? 
            AND (tmdb_id IS NULL OR tmdb_id = 0)
            AND (
                media_type IN ('movie', 'tv') 
                OR url REGEXP '/movie/' 
                OR url REGEXP '/series/'
                OR (url REGEXP '\\.(mkv|mp4|avi|m4v)$' AND url NOT REGEXP '/live/')
            )
        ");
        $stmt->execute([$list_id]);
        $remaining = $stmt->fetchColumn();
        
        $message = "✅ Procesados: $processed | 🎯 Encontrados: $found | ⚠️ Rechazados: $rejected | ❌ Errores: $errors | 📋 Pendientes: $remaining";
        
        // Guardar detalles en sesión para mostrar
        $_SESSION['search_details'] = $search_details;
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        COUNT(c.id) as total,
        COUNT(CASE 
            WHEN (c.tmdb_id IS NULL OR c.tmdb_id = 0) 
            AND (
                c.media_type IN ('movie', 'tv') 
                OR c.url REGEXP '/movie/' 
                OR c.url REGEXP '/series/'
                OR (c.url REGEXP '\\\\.(mkv|mp4|avi|m4v)$' AND c.url NOT REGEXP '/live/')
            ) THEN 1 
        END) as pending_valid
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name
    HAVING pending_valid > 0
    ORDER BY pending_valid DESC, l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

$search_details = $_SESSION['search_details'] ?? [];
unset($_SESSION['search_details']);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Búsqueda Inteligente TMDB v2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select, input {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .batch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .batch-btn {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-align: center;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .search-detail {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            border-left: 4px solid #46d347;
        }
        
        .original-title {
            color: #f59e0b;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .found-title {
            color: #10b981;
            font-weight: bold;
        }
        
        .search-info {
            color: #b0b0b0;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .similarity-high { color: #10b981; }
        .similarity-medium { color: #f59e0b; }
        .similarity-low { color: #ef4444; }
        
        .info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🧠 Búsqueda Inteligente TMDB v2</h1>
        
        <div class="info">
            <strong>🎯 Versión Mejorada:</strong> Incluye validación de similitud para evitar coincidencias incorrectas. Solo acepta resultados con alta similitud al título original.
        </div>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🔍 Procesar con Validación de Similitud</h2>
            
            <form method="POST" id="searchForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo number_format($list['pending_valid']); ?> pendientes)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="min_similarity">Similitud Mínima (%):</label>
                    <select name="min_similarity" id="min_similarity">
                        <option value="40">40% - Permisivo</option>
                        <option value="50" selected>50% - Balanceado</option>
                        <option value="60">60% - Estricto</option>
                        <option value="70">70% - Muy Estricto</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tamaño del Lote:</label>
                    <div class="batch-options">
                        <button type="submit" name="smart_search" onclick="setBatchSize(5)" class="batch-btn">
                            5 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(10)" class="batch-btn">
                            10 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(15)" class="batch-btn">
                            15 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(20)" class="batch-btn">
                            20 elementos
                        </button>
                    </div>
                    <input type="hidden" name="batch_size" id="batch_size" value="10">
                </div>
            </form>
        </div>
        
        <?php if (!empty($search_details)): ?>
        <div class="card">
            <h2>🎯 Detalles de Búsquedas Exitosas</h2>
            <?php foreach ($search_details as $detail): ?>
            <div class="search-detail">
                <div class="original-title">
                    📺 Original: <?php echo htmlspecialchars($detail['original_title']); ?>
                </div>
                <div class="found-title">
                    🎬 Encontrado: <?php echo htmlspecialchars($detail['found_title']); ?> (ID: <?php echo $detail['tmdb_id']; ?>)
                </div>
                <div class="search-info">
                    🔍 Búsqueda: "<?php echo htmlspecialchars($detail['search_term']); ?>" 
                    | Variación: <?php echo $detail['variation']; ?> 
                    | Idioma: <?php echo $detail['language']; ?>
                    | <span class="<?php 
                        echo $detail['similarity'] >= 80 ? 'similarity-high' : 
                             ($detail['similarity'] >= 60 ? 'similarity-medium' : 'similarity-low'); 
                    ?>">Similitud: <?php echo round($detail['similarity'], 1); ?>%</span>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🎯 Mejoras en v2</h2>
            <ul>
                <li>✅ <strong>Validación de Similitud:</strong> Calcula similitud entre título original y encontrado</li>
                <li>✅ <strong>Filtro de Calidad:</strong> Solo acepta coincidencias con similitud mínima configurable</li>
                <li>✅ <strong>Evaluación Múltiple:</strong> Revisa los primeros 3 resultados de cada búsqueda</li>
                <li>✅ <strong>Mejor Precisión:</strong> Reduce falsos positivos significativamente</li>
                <li>✅ <strong>Estadísticas Detalladas:</strong> Muestra encontrados vs rechazados</li>
            </ul>
        </div>
    </div>
    
    <script>
        function setBatchSize(size) {
            document.getElementById('batch_size').value = size;
        }
    </script>
</body>
</html>
