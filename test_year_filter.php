<?php
// Test del filtro de año para verificar que prioriza 2014
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Buscar todas las versiones de "Gone Girl"
$stmt = $pdo->prepare("
    SELECT c.*, l.name as list_name
    FROM m3u_content c 
    LEFT JOIN m3u_lists l ON c.list_id = l.id 
    WHERE l.is_active = 1 
    AND c.title LIKE '%Gone Girl%'
    ORDER BY c.year, c.title
");
$stmt->execute();
$all_gone_girl = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Obtener información del pedido "Perdida"
$stmt = $pdo->prepare("
    SELECT * FROM orders 
    WHERE title = 'Perdida' OR tmdb_original_title = 'Gone Girl'
    ORDER BY created_at DESC 
    LIMIT 1
");
$stmt->execute();
$order_info = $stmt->fetch(PDO::FETCH_ASSOC);

// Simular búsqueda con filtro de año
$filtered_results = [];
if ($order_info && $order_info['year']) {
    $stmt = $pdo->prepare("
        SELECT c.*, l.name as list_name, 
               CASE WHEN c.year = ? THEN 90 ELSE 80 END as relevance
        FROM m3u_content c 
        LEFT JOIN m3u_lists l ON c.list_id = l.id 
        WHERE l.is_active = 1 
        AND c.title LIKE '%Gone Girl%'
        AND c.year = ?
        ORDER BY c.title
    ");
    $stmt->execute([$order_info['year'], $order_info['year']]);
    $filtered_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Test Filtro de Año</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .section {
            background: #2d2d2d;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #404040;
            margin-bottom: 1.5rem;
        }
        
        .section h3 {
            color: #46d347;
            margin-bottom: 1rem;
        }
        
        .result-item {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 4px;
            border-left: 4px solid #46d347;
            margin-bottom: 0.5rem;
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            align-items: center;
        }
        
        .result-item.target-year {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        
        .result-item.wrong-year {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        
        .result-title {
            color: white;
            font-weight: bold;
        }
        
        .result-meta {
            color: #888;
            font-size: 0.9rem;
        }
        
        .year-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .year-correct {
            background: #10b981;
            color: white;
        }
        
        .year-wrong {
            background: #f59e0b;
            color: white;
        }
        
        .year-unknown {
            background: #6b7280;
            color: white;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .order-info {
            background: #0f1419;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #46d347;
            margin-bottom: 1rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .test-button {
            background: #46d347;
            color: white;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            background: #3bc55a;
        }
        
        .test-button.primary {
            background: #3b82f6;
        }
        
        .test-button.primary:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>🎯 Test de Filtro de Año</h1>
            <p>Verificación del filtro de año para "Gone Girl" (2014)</p>
        </div>
        
        <?php if ($order_info): ?>
        <div class="order-info">
            <h4 style="color: #46d347; margin-bottom: 0.5rem;">📋 Información del Pedido "Perdida"</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div><strong>Título:</strong> <?php echo htmlspecialchars($order_info['title']); ?></div>
                <div><strong>Título Original:</strong> <?php echo htmlspecialchars($order_info['tmdb_original_title'] ?? 'N/A'); ?></div>
                <div><strong>Año Objetivo:</strong> <span style="color: #10b981; font-weight: bold;"><?php echo htmlspecialchars($order_info['year'] ?? 'N/A'); ?></span></div>
                <div><strong>TMDB ID:</strong> <?php echo htmlspecialchars($order_info['tmdb_id'] ?? 'N/A'); ?></div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h3>🔍 Todas las Versiones de "Gone Girl" Encontradas</h3>
            
            <?php if (empty($all_gone_girl)): ?>
            <div style="color: #888; text-align: center; padding: 1rem;">
                No se encontraron versiones de "Gone Girl" en las listas M3U
            </div>
            <?php else: ?>
            <?php foreach ($all_gone_girl as $result): ?>
            <div class="result-item <?php 
                if ($order_info && $result['year'] == $order_info['year']) {
                    echo 'target-year';
                } elseif ($result['year'] && $result['year'] != ($order_info['year'] ?? '')) {
                    echo 'wrong-year';
                }
            ?>">
                <div>
                    <div class="result-title"><?php echo htmlspecialchars($result['title']); ?></div>
                    <div class="result-meta">Lista: <?php echo htmlspecialchars($result['list_name']); ?></div>
                </div>
                <div class="year-badge <?php 
                    if ($order_info && $result['year'] == $order_info['year']) {
                        echo 'year-correct';
                    } elseif ($result['year']) {
                        echo 'year-wrong';
                    } else {
                        echo 'year-unknown';
                    }
                ?>">
                    <?php echo $result['year'] ?: 'Sin año'; ?>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($filtered_results)): ?>
        <div class="section">
            <h3>✅ Resultados con Filtro de Año (<?php echo $order_info['year']; ?>)</h3>
            
            <?php foreach ($filtered_results as $result): ?>
            <div class="result-item target-year">
                <div>
                    <div class="result-title"><?php echo htmlspecialchars($result['title']); ?></div>
                    <div class="result-meta">Lista: <?php echo htmlspecialchars($result['list_name']); ?> | Relevancia: <?php echo $result['relevance']; ?>%</div>
                </div>
                <div class="year-badge year-correct">
                    <?php echo $result['year']; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div class="section">
            <h3>⚠️ Sin Resultados con Filtro de Año</h3>
            <div style="color: #f59e0b; text-align: center; padding: 1rem;">
                No se encontraron versiones de "Gone Girl" del año <?php echo $order_info['year'] ?? 'N/A'; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h3>📊 Análisis</h3>
            <ul style="color: #cbd5e1; line-height: 1.6;">
                <li><strong>Total versiones encontradas:</strong> <?php echo count($all_gone_girl); ?></li>
                <li><strong>Versiones del año objetivo (<?php echo $order_info['year'] ?? 'N/A'; ?>):</strong> <?php echo count($filtered_results); ?></li>
                <li><strong>Filtro funcionando:</strong> 
                    <?php if (count($filtered_results) > 0): ?>
                        ✅ SÍ - Encuentra versiones del año correcto
                    <?php else: ?>
                        ❌ NO - No hay versiones del año objetivo en las listas
                    <?php endif; ?>
                </li>
                <li><strong>Recomendación:</strong> 
                    <?php if (count($filtered_results) > 0): ?>
                        ✅ El filtro de año debería mostrar "Gone Girl (2014)" primero
                    <?php else: ?>
                        ⚠️ Puede que "Gone Girl (2014)" no esté disponible en las listas M3U
                    <?php endif; ?>
                </li>
            </ul>
        </div>
        
        <div class="test-buttons">
            <a href="m3u_search_tmdb.php?q=Perdida&auto_search=1" target="_blank" class="test-button primary">
                🔍 Probar Búsqueda con Filtro: "Perdida"
            </a>
            <a href="m3u_search_tmdb.php?q=Gone Girl&auto_search=1" target="_blank" class="test-button">
                🎬 Probar Búsqueda: "Gone Girl"
            </a>
        </div>
    </div>
</body>
</html>
