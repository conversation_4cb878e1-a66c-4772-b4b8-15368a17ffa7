<?php
// Script para agregar columnas TMDB a la tabla orders
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

echo "<h1>🔧 Agregar Columnas TMDB a Orders</h1>";
echo "<style>
body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
.success { color: #10b981; }
.error { color: #ef4444; }
.info { color: #3b82f6; }
</style>";

// Función para verificar si una columna existe
function columnExists($pdo, $table, $column) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE table_name = ? 
        AND table_schema = DATABASE() 
        AND column_name = ?
    ");
    $stmt->execute([$table, $column]);
    return $stmt->fetchColumn() > 0;
}

// Columnas TMDB para orders
$orders_columns = [
    'tmdb_id' => 'INT NULL',
    'tmdb_title' => 'VARCHAR(500) NULL',
    'tmdb_year' => 'INT NULL',
    'tmdb_poster_path' => 'VARCHAR(500) NULL',
    'tmdb_overview' => 'TEXT NULL'
];

echo "<h2>📋 Verificando y Agregando Columnas TMDB a 'orders'</h2>";

$added_columns = 0;
$existing_columns = 0;
$errors = 0;

foreach ($orders_columns as $column => $type) {
    echo "<p>";
    
    if (columnExists($pdo, 'orders', $column)) {
        echo "<span class='info'>ℹ️ Columna '$column' ya existe</span>";
        $existing_columns++;
    } else {
        try {
            $sql = "ALTER TABLE orders ADD COLUMN $column $type";
            $pdo->exec($sql);
            echo "<span class='success'>✅ Columna '$column' agregada exitosamente</span>";
            $added_columns++;
        } catch (PDOException $e) {
            echo "<span class='error'>❌ Error agregando '$column': " . $e->getMessage() . "</span>";
            $errors++;
        }
    }
    
    echo "</p>";
}

echo "<h2>📊 Resumen</h2>";
echo "<ul>";
echo "<li><span class='success'>✅ Columnas agregadas: $added_columns</span></li>";
echo "<li><span class='info'>ℹ️ Columnas existentes: $existing_columns</span></li>";
if ($errors > 0) {
    echo "<li><span class='error'>❌ Errores: $errors</span></li>";
}
echo "</ul>";

if ($added_columns > 0) {
    echo "<div style='background: rgba(16, 185, 129, 0.2); border: 1px solid #10b981; color: #10b981; padding: 15px; border-radius: 6px; margin: 20px 0;'>";
    echo "<strong>🎉 ¡Éxito!</strong> Las columnas TMDB han sido agregadas a la tabla 'orders'. ";
    echo "Ahora los pedidos incluirán automáticamente información de TMDB.";
    echo "</div>";
}

// Verificar estructura final
echo "<h2>🔍 Estructura Final de 'orders'</h2>";
$stmt = $pdo->query("DESCRIBE orders");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
echo "<tr style='background: #333;'>";
echo "<th style='border: 1px solid #444; padding: 10px;'>Campo</th>";
echo "<th style='border: 1px solid #444; padding: 10px;'>Tipo</th>";
echo "<th style='border: 1px solid #444; padding: 10px;'>Nulo</th>";
echo "<th style='border: 1px solid #444; padding: 10px;'>Default</th>";
echo "</tr>";

foreach ($columns as $column) {
    $is_tmdb = strpos($column['Field'], 'tmdb_') === 0;
    $row_style = $is_tmdb ? 'background: rgba(16, 185, 129, 0.1);' : '';
    
    echo "<tr style='$row_style'>";
    echo "<td style='border: 1px solid #444; padding: 10px;'>{$column['Field']}</td>";
    echo "<td style='border: 1px solid #444; padding: 10px;'>{$column['Type']}</td>";
    echo "<td style='border: 1px solid #444; padding: 10px;'>{$column['Null']}</td>";
    echo "<td style='border: 1px solid #444; padding: 10px;'>{$column['Default']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🚀 Próximos Pasos</h2>";
echo "<ol>";
echo "<li>✅ <strong>Columnas TMDB agregadas</strong> - Los pedidos ahora incluirán información TMDB automáticamente</li>";
echo "<li>🔍 <strong>Probar búsqueda avanzada</strong> - Ve a <a href='search_tmdb.php' style='color: #10b981;'>search_tmdb.php</a></li>";
echo "<li>📋 <strong>Hacer un pedido de prueba</strong> - Ve a <a href='index.php' style='color: #10b981;'>index.php</a> y haz un pedido</li>";
echo "<li>👀 <strong>Verificar pedidos</strong> - Ve a <a href='admin_orders.php' style='color: #10b981;'>admin_orders.php</a> para ver los TMDB IDs</li>";
echo "</ol>";

echo "<br><br>";
echo "<a href='admin.php' style='color: #10b981;'>← Volver al admin</a>";
?>
