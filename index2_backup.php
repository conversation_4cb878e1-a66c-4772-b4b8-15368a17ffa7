<?php
session_start();

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? '';

// Configuración MySQL
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    error_log('Error de base de datos: ' . $e->getMessage());
    die('Error de conexión a la base de datos.');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ RGS TOOL - Servicios de Soporte</title>
    <meta name="description" content="Centro de servicios de soporte técnico para IPTV. Gestiona tickets, chat en vivo, aplicaciones y más.">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛠️</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de colores profesional para soporte técnico */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --accent-dark: #059669;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --border-color: #334155;
            --border-light: #475569;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.6);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Header Profesional para Soporte Técnico */
        .header {
            background: var(--gradient-dark);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(20px);
            border-bottom: 2px solid var(--border-color);
            box-shadow: var(--shadow-medium);
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .logo i {
            font-size: 2rem;
        }

        .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .nav-breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .nav-breadcrumb a:hover {
            color: var(--accent-color);
        }

        /* User Menu */
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: rgba(37, 99, 235, 0.15);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(37, 99, 235, 0.3);
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(30, 41, 59, 0.8);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(37, 99, 235, 0.2);
            transform: translateY(-2px);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-light);
        }

        .nav-btn.logout-btn {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--danger-color);
        }

        .nav-btn.logout-btn:hover {
            background: rgba(239, 68, 68, 0.25);
            border-color: var(--danger-color);
            color: white;
        }

        .nav-btn.admin-btn {
            background: rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.3);
            color: var(--warning-color);
        }

        .nav-btn.admin-btn:hover {
            background: rgba(245, 158, 11, 0.25);
            border-color: var(--warning-color);
            color: white;
        }

        /* Hero Section - Servicios de Soporte */
        .hero {
            background: var(--gradient-dark);
            padding: 2rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .hero-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .hero-header h1 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .hero-header .subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--border-radius);
            color: var(--accent-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--accent-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Tarjetas de Servicios de Soporte */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: var(--secondary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--primary-color);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-icon {
            width: 64px;
            height: 64px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .service-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .service-action {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1rem;
            text-decoration: none;
            transition: var(--transition);
        }

        .service-action:hover {
            color: var(--accent-color);
            transform: translateX(4px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .service-card {
                padding: 1.5rem;
            }

            .service-icon {
                width: 56px;
                height: 56px;
                font-size: 1.75rem;
            }

            .nav-btn span {
                display: none;
            }

            .user-info span {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .hero-container {
                padding: 0 1rem;
            }

            .services-grid {
                gap: 1rem;
            }

            .service-card {
                padding: 1.25rem;
            }
        }

        /* Animaciones */
        .fade-in {
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stagger-animation .service-card {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }

        .stagger-animation .service-card:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation .service-card:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation .service-card:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation .service-card:nth-child(4) { animation-delay: 0.4s; }
        .stagger-animation .service-card:nth-child(5) { animation-delay: 0.5s; }
        .stagger-animation .service-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="index.php" class="logo">
                <i class="fas fa-headset"></i>
                <span>RGS TOOL</span>
            </a>

            <div class="nav-breadcrumb">
                <a href="index.php">
                    <i class="fas fa-home"></i>
                    Inicio
                </a>
                <i class="fas fa-chevron-right"></i>
                <span>Servicios de Soporte</span>
            </div>

            <div class="user-menu">
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span>Hola, <?php echo htmlspecialchars($username); ?></span>
                </div>

                <a href="index.php" class="nav-btn">
                    <i class="fas fa-film"></i>
                    <span>Contenido</span>
                </a>

                <a href="pedidos.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Mis Pedidos</span>
                </a>

                <?php
                // Verificar si el usuario es admin
                $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ? AND username IN ('admin', 'Infest84')");
                $stmt->execute([$user_id]);
                $is_admin = $stmt->fetch();
                if ($is_admin):
                ?>
                <a href="admin_login.php" class="nav-btn admin-btn">
                    <i class="fas fa-cog"></i>
                    <span>Admin</span>
                </a>
                <?php endif; ?>

                <a href="logout.php" class="nav-btn logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Salir</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section - Servicios de Soporte -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-header">
                <h1 class="fade-in">
                    <i class="fas fa-headset" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    Centro de Servicios de Soporte
                </h1>
                <p class="subtitle fade-in">
                    Accede a todos nuestros servicios de soporte técnico especializado para IPTV. 
                    Gestiona tickets, obtén ayuda en tiempo real y accede a recursos técnicos.
                </p>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <div>
                        <strong>Todos los Servicios Operativos</strong>
                        <span style="margin-left: 0.5rem; font-size: 0.9rem;">24/7 Disponible</span>
                    </div>
                </div>
            </div>

            <!-- Tarjetas de Servicios de Soporte -->
            <div class="services-grid stagger-animation">
                <div class="service-card" onclick="window.location.href='user_tickets.php'">
                    <div class="service-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="service-title">Gestión de Tickets</h3>
                    <p class="service-description">
                        Crea y gestiona tickets de soporte técnico. Seguimiento en tiempo real del estado de tus solicitudes
                        con notificaciones automáticas y historial completo.
                    </p>
                    <a href="user_tickets.php" class="service-action">
                        <span>Acceder a Tickets</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_chat_realtime.php'">
                    <div class="service-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="service-title">Chat en Tiempo Real</h3>
                    <p class="service-description">
                        Soporte instantáneo con nuestros agentes técnicos especializados en IPTV.
                        Resolución rápida de problemas y asistencia en tiempo real.
                    </p>
                    <a href="user_chat_realtime.php" class="service-action">
                        <span>Iniciar Chat</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_apps.php'">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="service-title">Aplicaciones IPTV</h3>
                    <p class="service-description">
                        Descarga aplicaciones oficiales para Android, iOS, Smart TV, Windows, macOS y otros
                        dispositivos compatibles con tu servicio IPTV.
                    </p>
                    <a href="user_apps.php" class="service-action">
                        <span>Ver Aplicaciones</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_help.php'">
                    <div class="service-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3 class="service-title">Centro de Ayuda</h3>
                    <p class="service-description">
                        Guías de configuración paso a paso, preguntas frecuentes, tutoriales para el primer uso
                        y documentación técnica completa.
                    </p>
                    <a href="user_help.php" class="service-action">
                        <span>Ver Guías</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_channels.php'">
                    <div class="service-icon">
                        <i class="fas fa-list-ul"></i>
                    </div>
                    <h3 class="service-title">Solicitar Canales</h3>
                    <p class="service-description">
                        Solicita canales específicos de TV que no estén disponibles en tu lista actual.
                        Proceso rápido de evaluación y agregado de contenido.
                    </p>
                    <a href="user_channels.php" class="service-action">
                        <span>Solicitar Canal</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="service-card" onclick="window.location.href='user_activation.php'">
                    <div class="service-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="service-title">Activación de Listas</h3>
                    <p class="service-description">
                        Activa y configura tus listas M3U y EPG para diferentes dispositivos y aplicaciones.
                        Generación automática de URLs personalizadas.
                    </p>
                    <a href="user_activation.php" class="service-action">
                        <span>Activar Listas</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Sección de Aplicaciones Disponibles -->
    <section style="padding: 3rem 0; background: var(--dark-bg);">
        <div style="max-width: 1400px; margin: 0 auto; padding: 0 1.5rem;">
            <?php include 'user_apps_display.php'; ?>
        </div>
    </section>

    <script>
        // Animación de entrada para las tarjetas
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.service-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            cards.forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
