<?php
// Visor de datos crudos M3U
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$raw_data = [];
$list_info = null;

if (isset($_POST['view_raw']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    
    // Obtener información de la lista
    $stmt = $pdo->prepare("SELECT * FROM m3u_lists WHERE id = ?");
    $stmt->execute([$list_id]);
    $list_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Obtener muestra de datos crudos
    $stmt = $pdo->prepare("
        SELECT * FROM m3u_content 
        WHERE list_id = ?
        ORDER BY id
        LIMIT 10
    ");
    $stmt->execute([$list_id]);
    $raw_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Obtener listas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        l.list_type,
        l.url,
        COUNT(c.id) as total_items
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name, l.list_type, l.url
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Visor de Datos Crudos M3U</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        button {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9em;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #404040;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        .data-table th {
            background: #1a1a1a;
            color: #46d347;
            font-weight: bold;
        }
        
        .data-table td {
            background: #2d2d2d;
            max-width: 200px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .field-name {
            color: #f59e0b;
            font-weight: bold;
        }
        
        .field-value {
            color: #b0b0b0;
            font-family: monospace;
            font-size: 0.85em;
        }
        
        .null-value {
            color: #ef4444;
            font-style: italic;
        }
        
        .url-value {
            color: #10b981;
            word-break: break-all;
        }
        
        .list-info {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .info-item {
            margin-bottom: 10px;
        }
        
        .info-label {
            color: #f59e0b;
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        
        .info-value {
            color: #b0b0b0;
        }
        
        .raw-item {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #404040;
        }
        
        .item-title {
            color: #46d347;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .field-grid {
            display: grid;
            grid-template-columns: 150px 1fr;
            gap: 10px;
            margin-bottom: 8px;
        }
        
        .highlight {
            background: rgba(70, 211, 71, 0.2);
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🔍 Visor de Datos Crudos M3U</h1>
        
        <div class="card">
            <h2>📋 Ver Estructura de Datos</h2>
            <p>Esta herramienta muestra exactamente cómo están almacenados los datos en la base de datos para identificar dónde puede estar la información TMDB.</p>
            
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['list_type']; ?>, <?php echo $list['total_items']; ?> items)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="view_raw">
                    🔍 Ver Datos Crudos
                </button>
            </form>
        </div>
        
        <?php if ($list_info): ?>
        <div class="card">
            <h2>📊 Información de la Lista</h2>
            <div class="list-info">
                <div class="info-item">
                    <span class="info-label">Nombre:</span>
                    <span class="info-value"><?php echo htmlspecialchars($list_info['name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tipo:</span>
                    <span class="info-value"><?php echo htmlspecialchars($list_info['list_type']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">URL:</span>
                    <span class="info-value url-value"><?php echo htmlspecialchars($list_info['url']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Total Items:</span>
                    <span class="info-value"><?php echo number_format($list_info['total_items']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Última Act.:</span>
                    <span class="info-value"><?php echo $list_info['last_updated'] ?? 'N/A'; ?></span>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($raw_data)): ?>
        <div class="card">
            <h2>📺 Muestra de Datos Crudos (10 elementos)</h2>
            
            <?php foreach ($raw_data as $index => $item): ?>
            <div class="raw-item">
                <div class="item-title">
                    📺 Elemento #<?php echo $index + 1; ?>: <?php echo htmlspecialchars($item['title']); ?>
                </div>
                
                <?php foreach ($item as $field => $value): ?>
                <div class="field-grid">
                    <div class="field-name"><?php echo htmlspecialchars($field); ?>:</div>
                    <div class="field-value">
                        <?php if ($value === null): ?>
                            <span class="null-value">NULL</span>
                        <?php elseif ($value === ''): ?>
                            <span class="null-value">(vacío)</span>
                        <?php elseif (filter_var($value, FILTER_VALIDATE_URL)): ?>
                            <span class="url-value"><?php echo htmlspecialchars($value); ?></span>
                        <?php elseif (strpos($field, 'tmdb') !== false || strpos($value, 'tmdb') !== false): ?>
                            <span class="highlight"><?php echo htmlspecialchars($value); ?></span>
                        <?php else: ?>
                            <?php echo htmlspecialchars($value); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="card">
            <h2>🔍 Análisis de Campos</h2>
            <?php
            // Analizar qué campos tienen datos
            $field_analysis = [];
            foreach ($raw_data as $item) {
                foreach ($item as $field => $value) {
                    if (!isset($field_analysis[$field])) {
                        $field_analysis[$field] = ['total' => 0, 'filled' => 0, 'sample_values' => []];
                    }
                    $field_analysis[$field]['total']++;
                    if ($value !== null && $value !== '') {
                        $field_analysis[$field]['filled']++;
                        if (count($field_analysis[$field]['sample_values']) < 3) {
                            $field_analysis[$field]['sample_values'][] = $value;
                        }
                    }
                }
            }
            ?>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Campo</th>
                        <th>Datos Llenos</th>
                        <th>% Lleno</th>
                        <th>Valores de Muestra</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($field_analysis as $field => $analysis): ?>
                    <tr>
                        <td class="field-name"><?php echo htmlspecialchars($field); ?></td>
                        <td><?php echo $analysis['filled']; ?>/<?php echo $analysis['total']; ?></td>
                        <td><?php echo round(($analysis['filled']/$analysis['total'])*100, 1); ?>%</td>
                        <td class="field-value">
                            <?php foreach ($analysis['sample_values'] as $sample): ?>
                                <div style="margin-bottom: 5px;">
                                    <?php echo htmlspecialchars(substr($sample, 0, 100)); ?>
                                    <?php if (strlen($sample) > 100): ?>...<?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>💡 Observaciones</h2>
            <ul>
                <?php
                $has_tmdb_fields = false;
                $has_logo_data = false;
                $has_tvg_data = false;
                
                foreach ($field_analysis as $field => $analysis) {
                    if (strpos($field, 'tmdb') !== false && $analysis['filled'] > 0) {
                        $has_tmdb_fields = true;
                    }
                    if (strpos($field, 'logo') !== false && $analysis['filled'] > 0) {
                        $has_logo_data = true;
                    }
                    if (strpos($field, 'tvg') !== false && $analysis['filled'] > 0) {
                        $has_tvg_data = true;
                    }
                }
                ?>
                
                <?php if ($has_tmdb_fields): ?>
                <li style="color: #10b981;">✅ Esta lista tiene campos TMDB con datos. Puedes usar el extractor TMDB.</li>
                <?php else: ?>
                <li style="color: #f59e0b;">⚠️ Esta lista no tiene campos TMDB poblados. Necesitarás búsquedas por título.</li>
                <?php endif; ?>
                
                <?php if ($has_logo_data): ?>
                <li style="color: #10b981;">✅ Esta lista tiene datos de logo/poster.</li>
                <?php else: ?>
                <li style="color: #f59e0b;">⚠️ Esta lista no tiene datos de logo/poster.</li>
                <?php endif; ?>
                
                <?php if ($has_tvg_data): ?>
                <li style="color: #10b981;">✅ Esta lista tiene metadatos TVG.</li>
                <?php endif; ?>
                
                <li>🔧 Para esta lista, recomiendo usar el <strong>Analizador TMDB Mejorado</strong> con búsquedas por título.</li>
                <li>📝 Los títulos con información de calidad necesitarán limpieza adicional.</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
