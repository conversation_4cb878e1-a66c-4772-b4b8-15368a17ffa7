<?php
// Búsqueda pública con TMDB IDs
session_start();

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

$search_results = [];
$search_query = '';
$search_type = 'all';

if (isset($_GET['query']) && !empty($_GET['query'])) {
    $search_query = trim($_GET['query']);
    $search_type = $_GET['type'] ?? 'all';
    
    // Construir consulta SQL base
    $base_sql = "
        SELECT 
            c.title,
            c.media_type,
            c.year,
            c.group_title,
            c.tmdb_id,
            c.tmdb_title,
            c.tmdb_poster_path,
            l.name as list_name,
            COUNT(*) as available_count
        FROM m3u_content c
        JOIN m3u_lists l ON c.list_id = l.id
        WHERE l.is_active = 1
        AND (c.media_type IN ('movie', 'tv') OR c.url REGEXP '/movie/' OR c.url REGEXP '/series/')
    ";
    
    $params = [];
    $conditions = [];
    
    // Filtrar por tipo de contenido
    if ($search_type === 'movie') {
        $conditions[] = "c.media_type = 'movie'";
    } elseif ($search_type === 'tv') {
        $conditions[] = "c.media_type = 'tv'";
    }
    
    // Detectar si es búsqueda por TMDB ID
    if (is_numeric($search_query)) {
        $tmdb_id = (int)$search_query;
        $conditions[] = "c.tmdb_id = ?";
        $params[] = $tmdb_id;
    } else {
        // Búsqueda por texto
        $search_terms = explode(' ', $search_query);
        $search_conditions = [];
        
        foreach ($search_terms as $term) {
            if (strlen($term) >= 2) {
                $search_conditions[] = "(
                    c.title LIKE ? OR 
                    c.tmdb_title LIKE ?
                )";
                $like_term = '%' . $term . '%';
                $params[] = $like_term;
                $params[] = $like_term;
            }
        }
        
        if (!empty($search_conditions)) {
            $conditions[] = '(' . implode(' AND ', $search_conditions) . ')';
        }
    }
    
    // Agregar condiciones a la consulta
    if (!empty($conditions)) {
        $base_sql .= " AND " . implode(' AND ', $conditions);
    }
    
    // Agrupar por contenido único y ordenar
    $base_sql .= " GROUP BY 
        COALESCE(c.tmdb_id, c.title),
        c.media_type,
        c.year
        ORDER BY 
        CASE WHEN c.tmdb_id IS NOT NULL THEN 0 ELSE 1 END,
        available_count DESC,
        c.title ASC
        LIMIT 50";
    
    try {
        $stmt = $pdo->prepare($base_sql);
        $stmt->execute($params);
        $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $error_message = "Error en la búsqueda: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Búsqueda de Contenido - RGS TOOL</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #1e293b;
            --dark-bg: #0f172a;
            --darker-bg: #020617;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --accent-color: #10b981;
            --border-color: #334155;
            --gradient-bg: linear-gradient(135deg, #0f172a 0%, #020617 100%);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .search-container {
            background: var(--secondary-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-medium);
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 1rem;
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
        }

        .search-select {
            padding: 1rem;
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            min-width: 150px;
        }

        .search-btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .search-tips {
            background: rgba(37, 99, 235, 0.1);
            border: 1px solid rgba(37, 99, 235, 0.3);
            color: var(--primary-color);
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .results-container {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            overflow: hidden;
            box-shadow: var(--shadow-medium);
        }

        .results-header {
            background: var(--dark-bg);
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .results-header h2 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .result-item:hover {
            background: rgba(37, 99, 235, 0.05);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-poster {
            width: 80px;
            height: 120px;
            background: var(--dark-bg);
            border-radius: 8px;
            margin-right: 1.5rem;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .result-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .result-info {
            flex: 1;
        }

        .result-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .result-meta {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .result-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 0.75rem;
        }

        .tag {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .tag-tmdb {
            background: rgba(16, 185, 129, 0.2);
            color: var(--accent-color);
            border: 1px solid var(--accent-color);
        }

        .tag-movie {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }

        .tag-tv {
            background: rgba(168, 85, 247, 0.2);
            color: #a855f7;
            border: 1px solid #a855f7;
        }

        .tag-available {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid #f59e0b;
        }

        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .back-link {
            color: var(--accent-color);
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .back-link:hover {
            color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .result-item {
                flex-direction: column;
                text-align: center;
            }
            
            .result-poster {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Volver al Inicio
        </a>
        
        <div class="header">
            <h1><i class="fas fa-search"></i> Búsqueda de Contenido</h1>
            <p>Encuentra películas y series disponibles en nuestras listas IPTV</p>
        </div>
        
        <div class="search-container">
            <form method="GET" class="search-form">
                <input 
                    type="text" 
                    name="query" 
                    class="search-input" 
                    placeholder="Buscar por título o TMDB ID (ej: Gone Girl, 210577)..." 
                    value="<?php echo htmlspecialchars($search_query); ?>"
                    required
                >
                <select name="type" class="search-select">
                    <option value="all" <?php echo $search_type === 'all' ? 'selected' : ''; ?>>Todo</option>
                    <option value="movie" <?php echo $search_type === 'movie' ? 'selected' : ''; ?>>Películas</option>
                    <option value="tv" <?php echo $search_type === 'tv' ? 'selected' : ''; ?>>Series</option>
                </select>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    Buscar
                </button>
            </form>
            
            <div class="search-tips">
                <strong>💡 Consejos:</strong> 
                Busca por título (ej: "Gone Girl") o por TMDB ID (ej: "210577") para resultados más precisos. 
                Los TMDB IDs garantizan coincidencias exactas en todos los idiomas.
            </div>
        </div>
        
        <?php if (!empty($search_query)): ?>
        <div class="results-container">
            <div class="results-header">
                <h2>Resultados para: "<?php echo htmlspecialchars($search_query); ?>"</h2>
                <p><?php echo count($search_results); ?> resultados encontrados</p>
            </div>
            
            <?php if (empty($search_results)): ?>
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No se encontraron resultados</h3>
                <p>Intenta con términos diferentes o verifica la ortografía</p>
                <p style="margin-top: 1rem; color: var(--primary-color);">
                    <strong>Sugerencia:</strong> Si conoces el TMDB ID, úsalo para búsquedas más precisas
                </p>
            </div>
            <?php else: ?>
            <?php foreach ($search_results as $result): ?>
            <div class="result-item">
                <div class="result-poster">
                    <?php if ($result['tmdb_poster_path']): ?>
                        <img src="https://image.tmdb.org/t/p/w200<?php echo $result['tmdb_poster_path']; ?>" 
                             alt="<?php echo htmlspecialchars($result['title']); ?>"
                             onerror="this.style.display='none'">
                    <?php else: ?>
                        <i class="fas fa-film" style="color: var(--text-secondary); font-size: 2rem;"></i>
                    <?php endif; ?>
                </div>
                
                <div class="result-info">
                    <div class="result-title">
                        <?php echo htmlspecialchars($result['title']); ?>
                    </div>
                    
                    <?php if ($result['tmdb_title'] && $result['tmdb_title'] !== $result['title']): ?>
                    <div class="result-meta">
                        <strong>Título TMDB:</strong> <?php echo htmlspecialchars($result['tmdb_title']); ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="result-meta">
                        <?php if ($result['year']): ?>
                        <strong>Año:</strong> <?php echo $result['year']; ?> | 
                        <?php endif; ?>
                        <strong>Disponible en:</strong> <?php echo $result['available_count']; ?> lista(s)
                        <?php if ($result['group_title']): ?>
                        | <strong>Categoría:</strong> <?php echo htmlspecialchars($result['group_title']); ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="result-tags">
                        <?php if ($result['tmdb_id']): ?>
                        <span class="tag tag-tmdb">TMDB: <?php echo $result['tmdb_id']; ?></span>
                        <?php endif; ?>
                        
                        <?php if ($result['media_type'] === 'movie'): ?>
                        <span class="tag tag-movie">Película</span>
                        <?php elseif ($result['media_type'] === 'tv'): ?>
                        <span class="tag tag-tv">Serie</span>
                        <?php endif; ?>
                        
                        <span class="tag tag-available"><?php echo $result['available_count']; ?> fuente(s)</span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
