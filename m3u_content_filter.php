<?php
// Filtro de contenido M3U - Separar películas/series de canales TV
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función para detectar tipo de contenido por URL
function detectContentTypeFromURL($url) {
    if (preg_match('/\/movie\//', $url)) {
        return 'movie';
    } elseif (preg_match('/\/series\//', $url)) {
        return 'tv';
    } elseif (preg_match('/\/live\//', $url)) {
        return 'live_tv';
    } else {
        // Detectar por extensión o patrón
        if (preg_match('/\.(mkv|mp4|avi|m4v)$/i', $url)) {
            return 'unknown_video';
        } else {
            return 'live_tv';
        }
    }
}

// Función para detectar tipo por título
function detectContentTypeFromTitle($title) {
    // Patrones de series
    if (preg_match('/[Ss]\d+[Ee]\d+/', $title)) {
        return 'tv';
    }
    
    // Patrones de canales de TV
    $tv_patterns = [
        '/\b(HD|FHD|4K|UHD)\s*$/i',
        '/\b(TV|Television|Canal|Channel)\b/i',
        '/\b(News|Noticias|Sport|Deportes|Kids|Infantil)\b/i',
        '/\b(24\/7|24h|Live)\b/i'
    ];
    
    foreach ($tv_patterns as $pattern) {
        if (preg_match($pattern, $title)) {
            return 'live_tv';
        }
    }
    
    // Si tiene año, probablemente es película
    if (preg_match('/\(\d{4}\)/', $title)) {
        return 'movie';
    }
    
    return 'unknown';
}

$message = '';
$error = '';
$analysis_results = [];

if (isset($_POST['analyze_content']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    
    try {
        // Obtener muestra de contenido para análisis
        $stmt = $pdo->prepare("
            SELECT id, title, url, media_type
            FROM m3u_content 
            WHERE list_id = ?
            LIMIT 100
        ");
        $stmt->execute([$list_id]);
        $sample_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $analysis = [
            'total_sample' => count($sample_content),
            'by_url' => ['movie' => 0, 'tv' => 0, 'live_tv' => 0, 'unknown_video' => 0],
            'by_title' => ['movie' => 0, 'tv' => 0, 'live_tv' => 0, 'unknown' => 0],
            'examples' => ['movie' => [], 'tv' => [], 'live_tv' => [], 'unknown' => []]
        ];
        
        foreach ($sample_content as $item) {
            // Análisis por URL
            $url_type = detectContentTypeFromURL($item['url']);
            $analysis['by_url'][$url_type]++;
            
            // Análisis por título
            $title_type = detectContentTypeFromTitle($item['title']);
            $analysis['by_title'][$title_type]++;
            
            // Guardar ejemplos
            if (count($analysis['examples'][$url_type]) < 3) {
                $analysis['examples'][$url_type][] = [
                    'title' => $item['title'],
                    'url' => $item['url']
                ];
            }
        }
        
        $analysis_results = $analysis;
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

if (isset($_POST['filter_content']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    
    try {
        // Actualizar media_type basado en URL
        $stmt = $pdo->prepare("
            UPDATE m3u_content 
            SET media_type = CASE 
                WHEN url REGEXP '/movie/' THEN 'movie'
                WHEN url REGEXP '/series/' THEN 'tv'
                WHEN url REGEXP '/live/' THEN 'live_tv'
                WHEN url REGEXP '\\.(mkv|mp4|avi|m4v)$' THEN 'unknown_video'
                ELSE 'live_tv'
            END
            WHERE list_id = ?
        ");
        $stmt->execute([$list_id]);
        $updated = $stmt->rowCount();
        
        // Obtener estadísticas después del filtrado
        $stmt = $pdo->prepare("
            SELECT 
                media_type,
                COUNT(*) as count
            FROM m3u_content 
            WHERE list_id = ?
            GROUP BY media_type
        ");
        $stmt->execute([$list_id]);
        $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $message = "✅ Filtrado completado. $updated elementos actualizados.";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas
$stmt = $pdo->query("
    SELECT 
        l.id, 
        l.name,
        COUNT(c.id) as total,
        COUNT(CASE WHEN c.media_type = 'movie' THEN 1 END) as movies,
        COUNT(CASE WHEN c.media_type = 'tv' THEN 1 END) as series,
        COUNT(CASE WHEN c.media_type = 'live_tv' THEN 1 END) as live_tv,
        COUNT(CASE WHEN c.media_type = 'unknown' OR c.media_type IS NULL THEN 1 END) as unknown
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name
    ORDER BY l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Filtro de Contenido M3U</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        button {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-warning {
            background: #f59e0b;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9em;
        }
        
        .movie { color: #10b981; }
        .tv { color: #3b82f6; }
        .live_tv { color: #ef4444; }
        .unknown { color: #f59e0b; }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .example-item {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 8px;
            border-left: 4px solid;
        }
        
        .example-item.movie { border-left-color: #10b981; }
        .example-item.tv { border-left-color: #3b82f6; }
        .example-item.live_tv { border-left-color: #ef4444; }
        .example-item.unknown { border-left-color: #f59e0b; }
        
        .example-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .example-url {
            font-size: 0.8em;
            color: #b0b0b0;
            word-break: break-all;
        }
        
        .warning {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid #f59e0b;
            color: #f59e0b;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🔍 Filtro de Contenido M3U</h1>
        
        <div class="warning">
            <strong>⚠️ Problema Identificado:</strong> Las listas M3U contienen películas, series Y canales de TV en vivo. 
            Los canales de TV no existen en TMDB, por eso las búsquedas fallan. Esta herramienta separa el contenido.
        </div>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🔍 Analizar Contenido de Lista</h2>
            
            <form method="POST">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo number_format($list['total']); ?> items)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" name="analyze_content">
                    🔍 Analizar Contenido
                </button>
                
                <button type="submit" name="filter_content" class="btn-warning">
                    🔧 Filtrar y Categorizar
                </button>
            </form>
        </div>
        
        <?php if (!empty($analysis_results)): ?>
        <div class="card">
            <h2>📊 Análisis de Contenido (Muestra de <?php echo $analysis_results['total_sample']; ?> elementos)</h2>
            
            <h3>🔗 Análisis por URL:</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number movie"><?php echo $analysis_results['by_url']['movie']; ?></div>
                    <div class="stat-label">Películas (/movie/)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number tv"><?php echo $analysis_results['by_url']['tv']; ?></div>
                    <div class="stat-label">Series (/series/)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number live_tv"><?php echo $analysis_results['by_url']['live_tv']; ?></div>
                    <div class="stat-label">TV en Vivo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number unknown"><?php echo $analysis_results['by_url']['unknown_video']; ?></div>
                    <div class="stat-label">Video Desconocido</div>
                </div>
            </div>
            
            <h3>📺 Ejemplos por Categoría:</h3>
            
            <?php foreach (['movie', 'tv', 'live_tv', 'unknown_video'] as $type): ?>
            <?php if (!empty($analysis_results['examples'][$type])): ?>
            <h4><?php 
                $labels = ['movie' => '🎬 Películas', 'tv' => '📺 Series', 'live_tv' => '📡 TV en Vivo', 'unknown_video' => '❓ Videos Desconocidos'];
                echo $labels[$type];
            ?>:</h4>
            <?php foreach ($analysis_results['examples'][$type] as $example): ?>
            <div class="example-item <?php echo $type; ?>">
                <div class="example-title"><?php echo htmlspecialchars($example['title']); ?></div>
                <div class="example-url"><?php echo htmlspecialchars($example['url']); ?></div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <div class="card">
            <h2>💡 Recomendaciones</h2>
            <ul>
                <?php 
                $movies_series = $analysis_results['by_url']['movie'] + $analysis_results['by_url']['tv'];
                $total_sample = $analysis_results['total_sample'];
                $percentage = $total_sample > 0 ? round(($movies_series / $total_sample) * 100, 1) : 0;
                ?>
                <li><strong>Contenido válido para TMDB:</strong> <?php echo $movies_series; ?>/<?php echo $total_sample; ?> (<?php echo $percentage; ?>%)</li>
                
                <?php if ($percentage > 30): ?>
                <li class="movie">✅ Esta lista tiene suficiente contenido válido para TMDB. Usa el filtro y luego procesa solo películas/series.</li>
                <?php else: ?>
                <li class="live_tv">⚠️ Esta lista es principalmente canales de TV. Pocas coincidencias esperadas en TMDB.</li>
                <?php endif; ?>
                
                <li>🔧 Usa "Filtrar y Categorizar" para separar automáticamente el contenido</li>
                <li>🎯 Después del filtrado, procesa solo las categorías 'movie' y 'tv' con TMDB</li>
            </ul>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📊 Estado Actual de las Listas</h2>
            <?php foreach ($lists as $list): ?>
            <div style="background: #1a1a1a; padding: 15px; border-radius: 4px; margin-bottom: 10px;">
                <div style="font-weight: bold; color: #46d347; margin-bottom: 10px;">
                    <?php echo htmlspecialchars($list['name']); ?>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($list['total']); ?></div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number movie"><?php echo number_format($list['movies']); ?></div>
                        <div class="stat-label">Películas</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number tv"><?php echo number_format($list['series']); ?></div>
                        <div class="stat-label">Series</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number live_tv"><?php echo number_format($list['live_tv']); ?></div>
                        <div class="stat-label">TV en Vivo</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number unknown"><?php echo number_format($list['unknown']); ?></div>
                        <div class="stat-label">Sin Categorizar</div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</body>
</html>
