<?php
// Versión simplificada del setup M3U
session_start();

// Verificar si es admin (versión más permisiva)
if (!isset($_SESSION['admin_logged_in'])) {
    echo "<h1>🔐 Acceso Requerido</h1>";
    echo "<p>Necesitas estar logueado como admin.</p>";
    echo "<p><a href='admin_login.php'>🔐 <PERSON>cer <PERSON>gin</a></p>";
    exit;
}

echo "<h1>📡 Setup M3U - Versión Simple</h1>";
echo "<style>body{font-family:Arial;margin:20px;background:#141414;color:white;} .success{color:#28a745;} .error{color:#dc3545;}</style>";

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión exitosa</p>";
    
    // Crear tabla m3u_lists
    $sql1 = "CREATE TABLE IF NOT EXISTS m3u_lists (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        url TEXT NOT NULL,
        username VARCHAR(255) DEFAULT NULL,
        password VARCHAR(255) DEFAULT NULL,
        server_url VARCHAR(500) DEFAULT NULL COMMENT 'URL del servidor Xtream Codes',
        list_type ENUM('direct_m3u', 'xtream_codes') DEFAULT 'direct_m3u' COMMENT 'Tipo de lista',
        folder_name VARCHAR(255) DEFAULT NULL COMMENT 'Nombre de la carpeta local',
        local_file_path TEXT DEFAULT NULL COMMENT 'Ruta del archivo descargado',
        is_active TINYINT(1) DEFAULT 1,
        last_updated TIMESTAMP NULL DEFAULT NULL,
        last_scan TIMESTAMP NULL DEFAULT NULL,
        last_download TIMESTAMP NULL DEFAULT NULL COMMENT 'Última descarga',
        total_items INT DEFAULT 0,
        file_size BIGINT DEFAULT 0 COMMENT 'Tamaño del archivo descargado',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql1);
    echo "<p class='success'>✅ Tabla m3u_lists creada</p>";
    
    // Crear tabla m3u_content
    $sql2 = "CREATE TABLE IF NOT EXISTS m3u_content (
        id INT AUTO_INCREMENT PRIMARY KEY,
        list_id INT NOT NULL,
        title VARCHAR(500) NOT NULL,
        clean_title VARCHAR(500) NOT NULL,
        media_type ENUM('movie', 'tv', 'unknown') DEFAULT 'unknown',
        season INT DEFAULT NULL,
        episode INT DEFAULT NULL,
        year INT DEFAULT NULL,
        url TEXT NOT NULL,
        group_title VARCHAR(255) DEFAULT NULL,
        logo_url TEXT DEFAULT NULL,
        tvg_id VARCHAR(255) DEFAULT NULL,
        language VARCHAR(10) DEFAULT NULL,
        quality VARCHAR(50) DEFAULT NULL,
        file_size BIGINT DEFAULT NULL,
        duration INT DEFAULT NULL,
        is_available TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql2);
    echo "<p class='success'>✅ Tabla m3u_content creada</p>";
    
    // Crear tabla content_matches
    $sql3 = "CREATE TABLE IF NOT EXISTS content_matches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT NOT NULL,
        content_id INT NOT NULL,
        match_score DECIMAL(5,2) DEFAULT 0.00,
        match_type ENUM('exact', 'partial', 'fuzzy') DEFAULT 'partial',
        is_downloaded TINYINT(1) DEFAULT 0,
        downloaded_at TIMESTAMP NULL DEFAULT NULL,
        downloaded_by INT DEFAULT NULL,
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql3);
    echo "<p class='success'>✅ Tabla content_matches creada</p>";
    
    // Crear directorio
    $dir = 'uploads/m3u_files';
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        echo "<p class='success'>✅ Directorio creado</p>";
    } else {
        echo "<p class='success'>✅ Directorio ya existe</p>";
    }
    
    echo "<h2>🎉 ¡Setup Completado!</h2>";
    echo "<p><a href='m3u_manager.php' style='color:#28a745;'>📡 Ir al Gestor M3U</a></p>";
    echo "<p><a href='admin.php' style='color:#17a2b8;'>📊 Volver al Admin</a></p>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
