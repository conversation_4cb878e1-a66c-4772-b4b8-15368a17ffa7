<?php
// Resumen de Implementación TMDB
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Verificar estado de implementación
$implementation_status = [];

// 1. Verificar columnas TMDB en m3u_content
$stmt = $pdo->query("SHOW COLUMNS FROM m3u_content LIKE 'tmdb_%'");
$tmdb_columns_content = $stmt->fetchAll(PDO::FETCH_ASSOC);
$implementation_status['m3u_content_columns'] = count($tmdb_columns_content);

// 2. Verificar columnas TMDB en orders
$stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'tmdb_%'");
$tmdb_columns_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
$implementation_status['orders_columns'] = count($tmdb_columns_orders);

// 3. Verificar contenido con TMDB ID
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb
    FROM m3u_content c
    JOIN m3u_lists l ON c.list_id = l.id
    WHERE l.is_active = 1
");
$content_stats = $stmt->fetch(PDO::FETCH_ASSOC);
$implementation_status['content_with_tmdb'] = $content_stats['with_tmdb'];
$implementation_status['total_content'] = $content_stats['total'];

// 4. Verificar pedidos con TMDB ID
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN tmdb_id IS NOT NULL AND tmdb_id > 0 THEN 1 END) as with_tmdb
    FROM orders
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
");
$orders_stats = $stmt->fetch(PDO::FETCH_ASSOC);
$implementation_status['orders_with_tmdb'] = $orders_stats['with_tmdb'];
$implementation_status['total_recent_orders'] = $orders_stats['total'];

// 5. Verificar archivos implementados
$implemented_files = [
    'index.php' => file_exists('index.php'),
    'pedidos.php' => file_exists('pedidos.php'),
    'search_tmdb.php' => file_exists('search_tmdb.php'),
    'm3u_search_tmdb_enhanced.php' => file_exists('m3u_search_tmdb_enhanced.php'),
    'm3u_search_enhanced.php' => file_exists('m3u_search_enhanced.php'),
    'm3u_smart_search_v2.php' => file_exists('m3u_smart_search_v2.php'),
    'm3u_content_filter.php' => file_exists('m3u_content_filter.php'),
    'tmdb_config.php' => file_exists('tmdb_config.php')
];

$implementation_status['files_implemented'] = array_sum($implemented_files);
$implementation_status['total_files'] = count($implemented_files);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Resumen Implementación TMDB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            color: #46d347;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .status-card {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid #404040;
        }
        
        .status-card h3 {
            color: #46d347;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #404040;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-value {
            font-weight: bold;
        }
        
        .status-success {
            color: #10b981;
        }
        
        .status-warning {
            color: #f59e0b;
        }
        
        .status-error {
            color: #ef4444;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #404040;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #46d347;
            transition: width 0.3s ease;
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #1a1a1a;
            border-radius: 6px;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 2rem;
            display: inline-block;
        }
        
        .summary-section {
            background: #2d2d2d;
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid #404040;
            margin-bottom: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-implemented {
            color: #10b981;
        }
        
        .feature-pending {
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <div class="header">
            <h1>📊 Resumen de Implementación TMDB</h1>
            <p>Estado completo de la integración TMDB en el sistema</p>
        </div>
        
        <div class="status-grid">
            <!-- Estado de Base de Datos -->
            <div class="status-card">
                <h3><i class="fas fa-database"></i> Base de Datos</h3>
                
                <div class="status-item">
                    <span>Columnas TMDB en m3u_content:</span>
                    <span class="status-value <?php echo $implementation_status['m3u_content_columns'] >= 5 ? 'status-success' : 'status-warning'; ?>">
                        <?php echo $implementation_status['m3u_content_columns']; ?>/8
                    </span>
                </div>
                
                <div class="status-item">
                    <span>Columnas TMDB en orders:</span>
                    <span class="status-value <?php echo $implementation_status['orders_columns'] >= 3 ? 'status-success' : 'status-warning'; ?>">
                        <?php echo $implementation_status['orders_columns']; ?>/5
                    </span>
                </div>
                
                <div class="status-item">
                    <span>Contenido con TMDB ID:</span>
                    <span class="status-value status-success">
                        <?php echo number_format($implementation_status['content_with_tmdb']); ?>
                    </span>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $implementation_status['total_content'] > 0 ? ($implementation_status['content_with_tmdb']/$implementation_status['total_content'])*100 : 0; ?>%"></div>
                </div>
                <small><?php echo $implementation_status['total_content'] > 0 ? round(($implementation_status['content_with_tmdb']/$implementation_status['total_content'])*100, 1) : 0; ?>% del contenido procesado</small>
            </div>
            
            <!-- Estado de Archivos -->
            <div class="status-card">
                <h3><i class="fas fa-file-code"></i> Archivos Implementados</h3>
                
                <div class="status-item">
                    <span>Archivos completados:</span>
                    <span class="status-value status-success">
                        <?php echo $implementation_status['files_implemented']; ?>/<?php echo $implementation_status['total_files']; ?>
                    </span>
                </div>
                
                <div class="files-grid">
                    <?php foreach ($implemented_files as $file => $exists): ?>
                    <div class="file-item">
                        <i class="fas fa-<?php echo $exists ? 'check-circle status-success' : 'times-circle status-error'; ?>"></i>
                        <span><?php echo $file; ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Estado de Funcionalidades -->
            <div class="status-card">
                <h3><i class="fas fa-cogs"></i> Funcionalidades</h3>
                
                <ul class="feature-list">
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Búsqueda por TMDB ID</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Pedidos con TMDB automático</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Búsqueda pública mejorada</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Filtrado de contenido</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Análisis inteligente</span>
                    </li>
                    <li>
                        <i class="fas fa-check-circle feature-implemented"></i>
                        <span>Interfaz administrativa</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Resumen de Implementación -->
        <div class="summary-section">
            <h2><i class="fas fa-clipboard-check"></i> Resumen de Implementación Completa</h2>
            
            <h3>🎯 Funcionalidades Implementadas:</h3>
            <ul class="feature-list">
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Pedidos Inteligentes:</strong> Los pedidos incluyen automáticamente TMDB ID, título oficial, año y poster</li>
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Búsqueda Avanzada:</strong> Los usuarios pueden buscar por título o TMDB ID con resultados precisos</li>
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Filtrado Automático:</strong> Separa películas/series de canales TV para mejor precisión</li>
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Análisis Masivo:</strong> Procesa listas M3U completas con validación de similitud</li>
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Interfaz Mejorada:</strong> Muestra posters, información TMDB y enlaces directos</li>
                <li><i class="fas fa-check-circle feature-implemented"></i> <strong>Matching Multi-idioma:</strong> Encuentra contenido en es-MX, en-US, es-ES</li>
            </ul>
            
            <h3>📈 Estadísticas del Sistema:</h3>
            <ul class="feature-list">
                <li><i class="fas fa-database feature-implemented"></i> <strong>Contenido Total:</strong> <?php echo number_format($implementation_status['total_content']); ?> elementos</li>
                <li><i class="fas fa-film feature-implemented"></i> <strong>Con TMDB ID:</strong> <?php echo number_format($implementation_status['content_with_tmdb']); ?> elementos (<?php echo $implementation_status['total_content'] > 0 ? round(($implementation_status['content_with_tmdb']/$implementation_status['total_content'])*100, 1) : 0; ?>%)</li>
                <li><i class="fas fa-shopping-cart feature-implemented"></i> <strong>Pedidos Recientes:</strong> <?php echo $implementation_status['total_recent_orders']; ?> (<?php echo $implementation_status['orders_with_tmdb']; ?> con TMDB)</li>
            </ul>
            
            <h3>🚀 Próximos Pasos Recomendados:</h3>
            <ul class="feature-list">
                <li><i class="fas fa-play-circle feature-pending"></i> Continuar procesando listas M3U con <code>m3u_smart_search_v2.php</code></li>
                <li><i class="fas fa-chart-line feature-pending"></i> Monitorear tasa de éxito y ajustar parámetros de similitud</li>
                <li><i class="fas fa-users feature-pending"></i> Promocionar búsqueda avanzada a usuarios</li>
                <li><i class="fas fa-sync feature-pending"></i> Configurar procesamiento automático periódico</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: rgba(70, 211, 71, 0.1); border: 1px solid #46d347; border-radius: 12px;">
            <h2 style="color: #46d347; margin-bottom: 1rem;">🎉 ¡Implementación TMDB Completada!</h2>
            <p style="font-size: 1.1rem; margin-bottom: 1rem;">
                El sistema TMDB está completamente integrado y funcionando. Los usuarios ahora pueden:
            </p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>✅ Hacer pedidos que incluyen automáticamente información TMDB</li>
                <li>✅ Buscar contenido por título o TMDB ID con alta precisión</li>
                <li>✅ Ver posters, años y títulos oficiales en sus pedidos</li>
                <li>✅ Encontrar contenido en múltiples idiomas</li>
            </ul>
        </div>
    </div>
</body>
</html>
