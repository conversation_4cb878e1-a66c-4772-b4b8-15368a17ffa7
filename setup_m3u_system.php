<?php
// Script para crear el sistema de gestión de listas M3U
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Verificar si es admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

echo "<h1>📡 Setup Sistema M3U</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #141414; color: white; } .success { color: #28a745; } .error { color: #dc3545; } .info { color: #17a2b8; } .warning { color: #ffc107; } pre { background: #333; padding: 10px; border-radius: 5px; } .step { background: #222; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff; }</style>";

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión a BD exitosa</p>";
    
    // Definir las tablas necesarias
    $tables = [
        'm3u_lists' => "
            CREATE TABLE IF NOT EXISTS m3u_lists (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL COMMENT 'Nombre descriptivo de la lista',
                url TEXT NOT NULL COMMENT 'URL de la lista M3U',
                username VARCHAR(255) DEFAULT NULL COMMENT 'Usuario para autenticación',
                password VARCHAR(255) DEFAULT NULL COMMENT 'Contraseña para autenticación',
                is_active TINYINT(1) DEFAULT 1 COMMENT 'Si la lista está activa',
                last_updated TIMESTAMP NULL DEFAULT NULL COMMENT 'Última actualización',
                last_scan TIMESTAMP NULL DEFAULT NULL COMMENT 'Último escaneo de contenido',
                total_items INT DEFAULT 0 COMMENT 'Total de elementos en la lista',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_active (is_active),
                INDEX idx_last_scan (last_scan)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'm3u_content' => "
            CREATE TABLE IF NOT EXISTS m3u_content (
                id INT AUTO_INCREMENT PRIMARY KEY,
                list_id INT NOT NULL COMMENT 'ID de la lista M3U',
                title VARCHAR(500) NOT NULL COMMENT 'Título del contenido',
                clean_title VARCHAR(500) NOT NULL COMMENT 'Título limpio para búsquedas',
                media_type ENUM('movie', 'tv', 'unknown') DEFAULT 'unknown' COMMENT 'Tipo de contenido',
                season INT DEFAULT NULL COMMENT 'Temporada (para series)',
                episode INT DEFAULT NULL COMMENT 'Episodio (para series)',
                year INT DEFAULT NULL COMMENT 'Año de lanzamiento',
                url TEXT NOT NULL COMMENT 'URL del stream',
                group_title VARCHAR(255) DEFAULT NULL COMMENT 'Grupo/categoría',
                logo_url TEXT DEFAULT NULL COMMENT 'URL del logo/poster',
                tvg_id VARCHAR(255) DEFAULT NULL COMMENT 'ID TVG',
                language VARCHAR(10) DEFAULT NULL COMMENT 'Idioma del contenido',
                quality VARCHAR(50) DEFAULT NULL COMMENT 'Calidad (HD, FHD, 4K, etc)',
                file_size BIGINT DEFAULT NULL COMMENT 'Tamaño del archivo en bytes',
                duration INT DEFAULT NULL COMMENT 'Duración en segundos',
                is_available TINYINT(1) DEFAULT 1 COMMENT 'Si el contenido está disponible',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (list_id) REFERENCES m3u_lists(id) ON DELETE CASCADE,
                INDEX idx_title (clean_title),
                INDEX idx_media_type (media_type),
                INDEX idx_year (year),
                INDEX idx_season_episode (season, episode),
                INDEX idx_available (is_available),
                FULLTEXT idx_search (title, clean_title)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'content_matches' => "
            CREATE TABLE IF NOT EXISTS content_matches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL COMMENT 'ID del pedido',
                content_id INT NOT NULL COMMENT 'ID del contenido M3U',
                match_score DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Puntuación de coincidencia (0-100)',
                match_type ENUM('exact', 'partial', 'fuzzy') DEFAULT 'partial' COMMENT 'Tipo de coincidencia',
                is_downloaded TINYINT(1) DEFAULT 0 COMMENT 'Si ya fue descargado',
                downloaded_at TIMESTAMP NULL DEFAULT NULL COMMENT 'Cuándo fue descargado',
                downloaded_by INT DEFAULT NULL COMMENT 'ID del admin que lo descargó',
                notes TEXT DEFAULT NULL COMMENT 'Notas adicionales',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                FOREIGN KEY (content_id) REFERENCES m3u_content(id) ON DELETE CASCADE,
                UNIQUE KEY unique_match (order_id, content_id),
                INDEX idx_match_score (match_score),
                INDEX idx_downloaded (is_downloaded)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    echo "<h2>🔧 Creando Tablas del Sistema M3U:</h2>";
    
    foreach ($tables as $table_name => $sql) {
        try {
            $pdo->exec($sql);
            echo "<div class='step'>";
            echo "<p class='success'>✅ Tabla '$table_name' creada/verificada exitosamente</p>";
            
            // Mostrar información de la tabla
            $stmt = $pdo->query("DESCRIBE $table_name");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p class='info'>📊 Columnas: " . count($columns) . "</p>";
            echo "</div>";
            
        } catch (PDOException $e) {
            echo "<div class='step'>";
            echo "<p class='error'>❌ Error creando tabla '$table_name': " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    // Crear directorio para archivos M3U descargados
    $m3u_dir = 'uploads/m3u_files';
    if (!file_exists($m3u_dir)) {
        if (mkdir($m3u_dir, 0755, true)) {
            echo "<p class='success'>✅ Directorio '$m3u_dir' creado</p>";
        } else {
            echo "<p class='error'>❌ No se pudo crear directorio '$m3u_dir'</p>";
        }
    } else {
        echo "<p class='info'>📁 Directorio '$m3u_dir' ya existe</p>";
    }
    
    // Crear archivo .htaccess para proteger el directorio
    $htaccess_content = "Order Deny,Allow\nDeny from all\nAllow from 127.0.0.1\nAllow from ::1";
    file_put_contents($m3u_dir . '/.htaccess', $htaccess_content);
    echo "<p class='success'>✅ Protección .htaccess configurada</p>";
    
    echo "<h2>🎉 ¡Sistema M3U Configurado!</h2>";
    echo "<div class='step'>";
    echo "<h3>📋 Próximos Pasos:</h3>";
    echo "<ol>";
    echo "<li><strong>Gestión de Listas:</strong> <a href='m3u_manager.php' style='color: #28a745;'>Administrar listas M3U</a></li>";
    echo "<li><strong>Análisis de Contenido:</strong> <a href='m3u_analyzer.php' style='color: #17a2b8;'>Analizar contenido</a></li>";
    echo "<li><strong>Panel Admin Mejorado:</strong> <a href='admin.php' style='color: #ffc107;'>Ver coincidencias en pedidos</a></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>📊 Estadísticas del Sistema:</h2>";
    echo "<div class='step'>";
    
    // Contar registros existentes
    $stats = [];
    foreach (array_keys($tables) as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $stats[$table] = $stmt->fetchColumn();
        } catch (PDOException $e) {
            $stats[$table] = 'Error';
        }
    }
    
    echo "<ul>";
    echo "<li>📡 Listas M3U: {$stats['m3u_lists']}</li>";
    echo "<li>🎬 Contenido indexado: {$stats['m3u_content']}</li>";
    echo "<li>🎯 Coincidencias encontradas: {$stats['content_matches']}</li>";
    echo "</ul>";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='admin.php'>📊 Panel Admin</a> | <a href='index.php'>🏠 Inicio</a></p>";
?>
