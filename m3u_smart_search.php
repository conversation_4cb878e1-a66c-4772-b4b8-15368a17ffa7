<?php
// Búsqueda inteligente TMDB para listas sin metadatos
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

require_once 'tmdb_config.php';

$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Función de limpieza inteligente de títulos
function smartCleanTitle($title) {
    $variations = [];
    
    // 1. Título original
    $variations['original'] = $title;
    
    // 2. Quitar año
    $no_year = trim(preg_replace('/\s*\(\d{4}\)/', '', $title));
    $variations['no_year'] = $no_year;
    
    // 3. Quitar episodios
    $no_episodes = trim(preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/', '', $no_year));
    $variations['no_episodes'] = $no_episodes;
    
    // 4. Quitar calidad
    $no_quality = trim(preg_replace('/\s*(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV|CAM|TS|DVDRip|BRRip|DUAL|LATINO|SPANISH).*$/i', '', $no_episodes));
    $variations['no_quality'] = $no_quality;
    
    // 5. Quitar corchetes y paréntesis
    $no_brackets = $no_quality;
    $no_brackets = trim(preg_replace('/\s*\[.*?\]/', '', $no_brackets));
    $no_brackets = trim(preg_replace('/\s*\(.*?\)/', '', $no_brackets));
    $variations['no_brackets'] = $no_brackets;
    
    // 6. Solo caracteres alfanuméricos y espacios
    $clean = trim(preg_replace('/\s+/', ' ', preg_replace('/[^\w\s]/', ' ', $no_brackets)));
    $variations['clean'] = $clean;
    
    // 7. Primeras 3 palabras
    $words = explode(' ', $clean);
    if (count($words) >= 3) {
        $variations['first_3_words'] = implode(' ', array_slice($words, 0, 3));
    }
    
    // 8. Primeras 2 palabras
    if (count($words) >= 2) {
        $variations['first_2_words'] = implode(' ', array_slice($words, 0, 2));
    }
    
    // Filtrar variaciones válidas
    return array_filter($variations, function($v) {
        return strlen(trim($v)) > 2;
    });
}

// Función de búsqueda inteligente
function smartTMDBSearch($title) {
    $variations = smartCleanTitle($title);
    $languages = ['es-MX', 'en-US', 'es-ES'];
    
    foreach ($variations as $type => $clean_title) {
        foreach ($languages as $lang) {
            $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=$lang&query=" . urlencode($clean_title);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 8);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 && $response) {
                $data = json_decode($response, true);
                if ($data && isset($data['results']) && !empty($data['results'])) {
                    return [
                        'result' => $data['results'][0],
                        'variation_used' => $type,
                        'language_used' => $lang,
                        'search_term' => $clean_title
                    ];
                }
            }
            
            usleep(100000); // 0.1 segundos entre peticiones
        }
    }
    
    return null;
}

$message = '';
$error = '';

if (isset($_POST['smart_search']) && isset($_POST['list_id'])) {
    $list_id = (int)$_POST['list_id'];
    $batch_size = (int)($_POST['batch_size'] ?? 10);
    
    try {
        // Obtener solo películas y series (NO canales de TV)
        $stmt = $pdo->prepare("
            SELECT id, title, url, media_type
            FROM m3u_content
            WHERE list_id = ?
            AND (tmdb_id IS NULL OR tmdb_id = 0)
            AND (
                media_type IN ('movie', 'tv')
                OR url REGEXP '/movie/'
                OR url REGEXP '/series/'
                OR (url REGEXP '\\.(mkv|mp4|avi|m4v)$' AND url NOT REGEXP '/live/')
            )
            ORDER BY
                CASE
                    WHEN url REGEXP '/movie/' THEN 1
                    WHEN url REGEXP '/series/' THEN 2
                    WHEN title REGEXP '(HD|4K|1080p|720p|BluRay|WEB-DL|HDTV)' THEN 4
                    ELSE 3
                END,
                LENGTH(title),
                id
            LIMIT $batch_size
        ");
        $stmt->execute([$list_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $processed = 0;
        $found = 0;
        $errors = 0;
        $search_details = [];
        
        foreach ($items as $item) {
            $processed++;
            
            try {
                $search_result = smartTMDBSearch($item['title']);
                
                if ($search_result) {
                    $found++;
                    $tmdb_result = $search_result['result'];
                    
                    // Guardar detalles para mostrar
                    $search_details[] = [
                        'original_title' => $item['title'],
                        'search_term' => $search_result['search_term'],
                        'variation' => $search_result['variation_used'],
                        'language' => $search_result['language_used'],
                        'found_title' => $tmdb_result['title'] ?? $tmdb_result['name'],
                        'tmdb_id' => $tmdb_result['id']
                    ];
                    
                    // Actualizar base de datos
                    $update_stmt = $pdo->prepare("
                        UPDATE m3u_content SET 
                            tmdb_id = ?,
                            tmdb_title = ?,
                            tmdb_poster_path = ?,
                            media_type = ?
                        WHERE id = ?
                    ");
                    
                    $media_type = 'unknown';
                    if (isset($tmdb_result['media_type'])) {
                        $media_type = $tmdb_result['media_type'] === 'movie' ? 'movie' : 'tv';
                    }
                    
                    $update_stmt->execute([
                        $tmdb_result['id'],
                        $tmdb_result['title'] ?? $tmdb_result['name'],
                        $tmdb_result['poster_path'],
                        $media_type,
                        $item['id']
                    ]);
                }
                
            } catch (Exception $e) {
                $errors++;
            }
            
            // Pausa entre elementos
            usleep(300000); // 0.3 segundos
        }
        
        // Contar pendientes (solo contenido válido para TMDB)
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM m3u_content
            WHERE list_id = ?
            AND (tmdb_id IS NULL OR tmdb_id = 0)
            AND (
                media_type IN ('movie', 'tv')
                OR url REGEXP '/movie/'
                OR url REGEXP '/series/'
                OR (url REGEXP '\\.(mkv|mp4|avi|m4v)$' AND url NOT REGEXP '/live/')
            )
        ");
        $stmt->execute([$list_id]);
        $remaining = $stmt->fetchColumn();
        
        $message = "✅ Procesados: $processed | 🎯 Encontrados: $found | ❌ Errores: $errors | 📋 Pendientes: $remaining";
        
        // Guardar detalles en sesión para mostrar
        $_SESSION['search_details'] = $search_details;
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas con estadísticas de contenido válido para TMDB
$stmt = $pdo->query("
    SELECT
        l.id,
        l.name,
        COUNT(c.id) as total,
        COUNT(CASE
            WHEN (c.tmdb_id IS NULL OR c.tmdb_id = 0)
            AND (
                c.media_type IN ('movie', 'tv')
                OR c.url REGEXP '/movie/'
                OR c.url REGEXP '/series/'
                OR (c.url REGEXP '\\\\.(mkv|mp4|avi|m4v)$' AND c.url NOT REGEXP '/live/')
            ) THEN 1
        END) as pending_valid,
        COUNT(CASE WHEN c.url REGEXP '/movie/' THEN 1 END) as movies,
        COUNT(CASE WHEN c.url REGEXP '/series/' THEN 1 END) as series,
        COUNT(CASE WHEN c.url NOT REGEXP '/(movie|series)/' THEN 1 END) as live_tv
    FROM m3u_lists l
    LEFT JOIN m3u_content c ON l.id = c.list_id
    WHERE l.is_active = 1
    GROUP BY l.id, l.name
    HAVING pending_valid > 0
    ORDER BY pending_valid DESC, l.name
");
$lists = $stmt->fetchAll(PDO::FETCH_ASSOC);

$search_details = $_SESSION['search_details'] ?? [];
unset($_SESSION['search_details']);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Búsqueda Inteligente TMDB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .batch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .batch-btn {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-align: center;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .search-detail {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            border-left: 4px solid #46d347;
        }
        
        .original-title {
            color: #f59e0b;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .found-title {
            color: #10b981;
            font-weight: bold;
        }
        
        .search-info {
            color: #b0b0b0;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .info {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🧠 Búsqueda Inteligente TMDB</h1>
        
        <div class="info">
            <strong>💡 Búsqueda Inteligente:</strong> Esta herramienta procesa SOLO películas y series (excluye canales de TV) y usa múltiples variaciones de limpieza de títulos para maximizar las coincidencias en TMDB.
        </div>

        <div style="background: rgba(70, 211, 71, 0.2); border: 1px solid #46d347; color: #46d347; padding: 15px; border-radius: 4px; margin: 15px 0;">
            <strong>✅ Filtrado Automático:</strong> Solo procesa contenido válido para TMDB (/movie/, /series/, archivos de video). Los canales de TV en vivo se excluyen automáticamente.
        </div>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🔍 Procesar Lista con Búsqueda Inteligente</h2>
            
            <form method="POST" id="searchForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>">
                            <?php echo htmlspecialchars($list['name']); ?>
                            (<?php echo number_format($list['pending_valid']); ?> películas/series pendientes,
                            <?php echo number_format($list['live_tv']); ?> canales TV)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tamaño del Lote:</label>
                    <div class="batch-options">
                        <button type="submit" name="smart_search" onclick="setBatchSize(5)" class="batch-btn">
                            5 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(10)" class="batch-btn">
                            10 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(15)" class="batch-btn">
                            15 elementos
                        </button>
                        <button type="submit" name="smart_search" onclick="setBatchSize(20)" class="batch-btn">
                            20 elementos
                        </button>
                    </div>
                    <input type="hidden" name="batch_size" id="batch_size" value="10">
                </div>
            </form>
        </div>
        
        <?php if (!empty($search_details)): ?>
        <div class="card">
            <h2>🎯 Detalles de Búsquedas Exitosas</h2>
            <?php foreach ($search_details as $detail): ?>
            <div class="search-detail">
                <div class="original-title">
                    📺 Original: <?php echo htmlspecialchars($detail['original_title']); ?>
                </div>
                <div class="found-title">
                    🎬 Encontrado: <?php echo htmlspecialchars($detail['found_title']); ?> (ID: <?php echo $detail['tmdb_id']; ?>)
                </div>
                <div class="search-info">
                    🔍 Búsqueda: "<?php echo htmlspecialchars($detail['search_term']); ?>" 
                    | Variación: <?php echo $detail['variation']; ?> 
                    | Idioma: <?php echo $detail['language']; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>🧠 Cómo Funciona la Búsqueda Inteligente</h2>
            <ol>
                <li><strong>Múltiples Variaciones:</strong> Genera 8 variaciones diferentes del título original</li>
                <li><strong>Limpieza Progresiva:</strong> Quita años, episodios, calidad, caracteres especiales</li>
                <li><strong>Búsqueda Multi-idioma:</strong> Prueba en es-MX, en-US, es-ES</li>
                <li><strong>Priorización:</strong> Procesa primero títulos más limpios</li>
                <li><strong>Optimización:</strong> Para en la primera coincidencia encontrada</li>
            </ol>
            
            <h3>🎯 Ventajas:</h3>
            <ul>
                <li>✅ <strong>Mayor tasa de éxito:</strong> Múltiples intentos por título</li>
                <li>✅ <strong>Inteligente:</strong> Prioriza títulos más fáciles de encontrar</li>
                <li>✅ <strong>Detallado:</strong> Muestra exactamente cómo encontró cada resultado</li>
                <li>✅ <strong>Eficiente:</strong> Para en la primera coincidencia</li>
            </ul>
        </div>
    </div>
    
    <script>
        function setBatchSize(size) {
            document.getElementById('batch_size').value = size;
        }
    </script>
</body>
</html>
