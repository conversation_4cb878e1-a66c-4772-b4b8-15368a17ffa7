<?php
// Analizador TMDB Mejorado - Versión estable
session_start();

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Conexión básica
$db_host = 'localhost';
$db_name = 'u170528143_php';
$db_user = 'u170528143_php';
$db_pass = '&T4v!$=i';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Error de conexión: " . $e->getMessage());
}

require_once 'tmdb_config.php';

$message = '';
$error = '';

// Procesar lote
if (isset($_POST['analyze']) && isset($_POST['list_id']) && isset($_POST['batch_size'])) {
    $list_id = (int)$_POST['list_id'];
    $batch_size = (int)$_POST['batch_size'];
    
    try {
        // Obtener elementos sin TMDB ID
        $stmt = $pdo->prepare("
            SELECT id, title 
            FROM m3u_content 
            WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)
            LIMIT $batch_size
        ");
        $stmt->execute([$list_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $processed = 0;
        $found = 0;
        $errors = 0;
        
        foreach ($items as $item) {
            $processed++;
            
            try {
                // Limpiar título
                $clean_title = trim(preg_replace('/\s*\(\d{4}\)/', '', $item['title']));
                $clean_title = trim(preg_replace('/\s*[Ss]\d+[Ee]\d+.*$/', '', $clean_title));
                
                // Buscar en TMDB
                $url = TMDB_BASE_URL . "/search/multi?api_key=" . TMDB_API_KEY . "&language=es-MX&query=" . urlencode($clean_title);
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 8);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code == 200 && $response) {
                    $data = json_decode($response, true);
                    if ($data && isset($data['results']) && !empty($data['results'])) {
                        $tmdb_result = $data['results'][0];
                        $found++;
                        
                        // Actualizar con información básica
                        $update_stmt = $pdo->prepare("
                            UPDATE m3u_content SET 
                                tmdb_id = ?,
                                tmdb_title = ?,
                                tmdb_poster_path = ?,
                                media_type = ?
                            WHERE id = ?
                        ");
                        
                        $media_type = 'unknown';
                        if (isset($tmdb_result['media_type'])) {
                            $media_type = $tmdb_result['media_type'] === 'movie' ? 'movie' : 'tv';
                        }
                        
                        $update_stmt->execute([
                            $tmdb_result['id'],
                            $tmdb_result['title'] ?? $tmdb_result['name'],
                            $tmdb_result['poster_path'],
                            $media_type,
                            $item['id']
                        ]);
                    }
                }
                
            } catch (Exception $e) {
                $errors++;
            }
            
            // Pausa entre peticiones
            usleep(300000); // 0.3 segundos
        }
        
        // Contar pendientes
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM m3u_content WHERE list_id = ? AND (tmdb_id IS NULL OR tmdb_id = 0)");
        $stmt->execute([$list_id]);
        $remaining = $stmt->fetchColumn();
        
        $message = "✅ Procesados: $processed | 🎯 Encontrados: $found | ❌ Errores: $errors | 📋 Pendientes: $remaining";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener listas con información detallada
try {
    $stmt = $pdo->query("
        SELECT 
            l.id, 
            l.name,
            COUNT(c.id) as total,
            COUNT(CASE WHEN c.tmdb_id IS NULL OR c.tmdb_id = 0 THEN 1 END) as pending,
            COUNT(CASE WHEN c.tmdb_id IS NOT NULL AND c.tmdb_id > 0 THEN 1 END) as completed
        FROM m3u_lists l
        LEFT JOIN m3u_content c ON l.id = c.list_id
        WHERE l.is_active = 1
        GROUP BY l.id, l.name
        ORDER BY pending DESC, l.name
    ");
    $lists = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = "Error obteniendo listas: " . $e->getMessage();
    $lists = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Analizador TMDB Mejorado</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .card {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        h1 {
            color: #46d347;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #b0b0b0;
        }
        
        select {
            width: 100%;
            padding: 10px;
            background: #1a1a1a;
            color: white;
            border: 1px solid #404040;
            border-radius: 4px;
        }
        
        .batch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .batch-btn {
            background: #46d347;
            color: #1a1a1a;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-align: center;
        }
        
        .batch-btn:hover {
            background: #3bc73c;
        }
        
        .success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
            color: #28a745;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #ef4444;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .back-link {
            color: #46d347;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #404040;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #46d347;
        }
        
        .stat-label {
            color: #b0b0b0;
            font-size: 0.9em;
        }
        
        .list-item {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            border: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-info {
            flex-grow: 1;
        }
        
        .list-name {
            font-weight: bold;
            color: #46d347;
        }
        
        .list-stats {
            color: #b0b0b0;
            font-size: 0.9em;
        }
        
        .progress-bar {
            width: 100px;
            height: 8px;
            background: #404040;
            border-radius: 4px;
            overflow: hidden;
            margin-left: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: #46d347;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">← Volver al Admin</a>
        
        <h1>🎬 Analizador TMDB Mejorado</h1>
        
        <?php if ($error): ?>
        <div class="error">
            <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($message): ?>
        <div class="success">
            <strong>Resultado:</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📋 Procesar Lista M3U</h2>
            
            <form method="POST" id="analyzeForm">
                <div class="form-group">
                    <label for="list_id">Seleccionar Lista:</label>
                    <select name="list_id" id="list_id" required>
                        <option value="">-- Selecciona una lista --</option>
                        <?php foreach ($lists as $list): ?>
                        <option value="<?php echo $list['id']; ?>" <?php echo (isset($_POST['list_id']) && $_POST['list_id'] == $list['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($list['name']); ?> 
                            (<?php echo $list['pending']; ?> pendientes de <?php echo $list['total']; ?>)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tamaño del Lote:</label>
                    <div class="batch-options">
                        <button type="submit" name="analyze" value="1" onclick="setBatchSize(5)" class="batch-btn">
                            5 elementos
                        </button>
                        <button type="submit" name="analyze" value="1" onclick="setBatchSize(10)" class="batch-btn">
                            10 elementos
                        </button>
                        <button type="submit" name="analyze" value="1" onclick="setBatchSize(20)" class="batch-btn">
                            20 elementos
                        </button>
                        <button type="submit" name="analyze" value="1" onclick="setBatchSize(50)" class="batch-btn">
                            50 elementos
                        </button>
                    </div>
                    <input type="hidden" name="batch_size" id="batch_size" value="10">
                </div>
            </form>
        </div>
        
        <?php if (!empty($lists)): ?>
        <div class="card">
            <h2>📊 Estado de las Listas</h2>
            <?php foreach ($lists as $list): ?>
            <div class="list-item">
                <div class="list-info">
                    <div class="list-name"><?php echo htmlspecialchars($list['name']); ?></div>
                    <div class="list-stats">
                        Total: <?php echo $list['total']; ?> | 
                        Completados: <?php echo $list['completed']; ?> | 
                        Pendientes: <?php echo $list['pending']; ?>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $list['total'] > 0 ? ($list['completed']/$list['total'])*100 : 0; ?>%"></div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>📖 Instrucciones</h2>
            <ol>
                <li>Selecciona una lista con elementos pendientes</li>
                <li>Elige el tamaño del lote (recomendado: 10-20 elementos)</li>
                <li>Haz clic en el botón correspondiente</li>
                <li>Repite hasta completar la lista</li>
                <li>Las listas se ordenan por cantidad de pendientes</li>
            </ol>
        </div>
    </div>
    
    <script>
        function setBatchSize(size) {
            document.getElementById('batch_size').value = size;
        }
    </script>
</body>
</html>
